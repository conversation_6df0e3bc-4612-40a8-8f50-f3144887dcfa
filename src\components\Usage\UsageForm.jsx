import React, { useState } from 'react'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import { HiLightningBolt, HiCalculator } from 'react-icons/hi'
import { preventNumberInputScroll } from '../../hooks/usePreventNumberInputScroll'

function UsageForm() {
  const [currentReading, setCurrentReading] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { state, updateUsage, usageSinceLastRecording, getDisplayUnitName } = useApp()
  const { theme, currentTheme } = useTheme()

  // Helper function to get appropriate background for cards in dark mode
  const getCardBackground = (lightBg, darkBg = 'bg-gray-700/50') => {
    return currentTheme === 'dark' ? darkBg : lightBg
  }

  const newReading = parseFloat(currentReading) || 0
  const calculatedUsage = state.currentUnits - newReading
  const usageCost = calculatedUsage * state.unitCost

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const reading = parseFloat(currentReading)

      if (isNaN(reading) || reading < 0) {
        alert('Please enter a valid meter reading (0 or greater)')
        return
      }

      if (reading > state.currentUnits) {
        alert('Current reading cannot be higher than your available units')
        return
      }

      updateUsage(reading)
      
      // Reset form
      setCurrentReading('')
      
      // Show success message
      alert(`Usage recorded successfully! Used ${calculatedUsage.toFixed(2)} ${getDisplayUnitName()} costing ${state.currencySymbol || 'R'}${usageCost.toFixed(2)}`)
      
    } catch (error) {
      console.error('Error recording usage:', error)
      alert('Error recording usage. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Current status */}
      <div className={`p-5 ${theme.card} rounded-xl border ${theme.border} shadow-sm`}>
        <h3 className={`font-semibold ${theme.text} mb-4 flex items-center gap-2`}>
          <div className={`p-1 rounded-lg bg-gradient-to-br ${theme.gradient}`}>
            <HiLightningBolt className="h-4 w-4 text-white" />
          </div>
          Current Status
        </h3>
        <div className="space-y-3 text-sm">
          <div className={`flex justify-between items-center p-2 ${theme.secondary} rounded-lg`}>
            <span className={`${theme.textSecondary} font-medium`}>Available Units:</span>
            <span className={`${theme.text} font-bold`}>{state.currentUnits.toFixed(2)} {getDisplayUnitName()}</span>
          </div>
          <div className={`flex justify-between items-center p-2 ${theme.secondary} rounded-lg`}>
            <span className={`${theme.textSecondary} font-medium`}>Previous Reading:</span>
            <span className={`${theme.text} font-bold`}>{state.previousUnits.toFixed(2)} {getDisplayUnitName()}</span>
          </div>
          <div className={`flex justify-between items-center p-2 ${theme.secondary} rounded-lg`}>
            <span className={`${theme.textSecondary} font-medium`}>Usage Since Last:</span>
            <span className={`${theme.text} font-bold`}>{usageSinceLastRecording.toFixed(2)} {getDisplayUnitName()}</span>
          </div>
        </div>
      </div>

      {/* Current reading input */}
      <div>
        <label htmlFor="currentReading" className={`block text-sm font-semibold ${theme.text} mb-3`}>
          Current Meter Reading
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <div className={`p-1 rounded-lg bg-gradient-to-br ${theme.gradient}`}>
              <HiLightningBolt className="h-4 w-4 text-white" />
            </div>
          </div>
          <input
            type="number"
            id="currentReading"
            value={currentReading}
            onChange={(e) => setCurrentReading(e.target.value)}
            onWheel={preventNumberInputScroll}
            step="0.01"
            min="0"
            max={state.currentUnits}
            placeholder="Enter current meter reading"
            className={`w-full pl-12 pr-4 py-4 border-4 ${theme.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${theme.border} ${theme.card} ${theme.text} ${theme.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200`}
            required
          />
        </div>
        <p className={`mt-2 text-xs ${theme.textSecondary} opacity-80 font-medium`}>
          📊 Enter the current reading from your electricity meter
        </p>
      </div>

      {/* Usage calculation preview */}
      {newReading > 0 && (
        <div className={`p-6 ${theme.card} rounded-xl border ${theme.border} shadow-sm`}>
          <div className="flex items-center mb-4">
            <div className={`p-2 rounded-lg bg-gradient-to-br ${theme.gradient} mr-3`}>
              <HiCalculator className="h-5 w-5 text-white" />
            </div>
            <h3 className={`font-semibold ${theme.text} text-lg`}>Usage Calculation</h3>
          </div>
          <div className="space-y-3 text-sm">
            <div className={`flex justify-between items-center p-3 ${theme.secondary} rounded-lg`}>
              <span className={`${theme.textSecondary} font-medium`}>Previous Units:</span>
              <span className={`${theme.text} font-bold`}>{state.currentUnits.toFixed(2)} {getDisplayUnitName()}</span>
            </div>
            <div className={`flex justify-between items-center p-3 ${theme.secondary} rounded-lg`}>
              <span className={`${theme.textSecondary} font-medium`}>New Reading:</span>
              <span className={`${theme.text} font-bold`}>{newReading.toFixed(2)} {getDisplayUnitName()}</span>
            </div>
            <div className={`border-t ${theme.border} my-3`}></div>
            <div className={`flex justify-between items-center p-3 ${theme.secondary} rounded-lg border ${theme.border}`}>
              <span className={`${theme.textSecondary} font-medium`}>Units Used:</span>
              <span className={`font-bold text-lg ${theme.text}`}>
                {calculatedUsage.toFixed(2)} {getDisplayUnitName()}
              </span>
            </div>
            <div className={`flex justify-between items-center p-3 ${theme.secondary} rounded-lg border ${theme.border}`}>
              <span className={`${theme.textSecondary} font-medium`}>Cost of Usage:</span>
              <span className={`font-bold text-lg ${theme.text}`}>
                {state.currencySymbol || 'R'}{usageCost.toFixed(2)}
              </span>
            </div>
            <div className={`flex justify-between items-center p-3 ${theme.secondary} rounded-lg border ${theme.border}`}>
              <span className={`${theme.textSecondary} font-medium`}>Remaining Units:</span>
              <span className={`font-bold text-lg ${theme.text}`}>
                {newReading.toFixed(2)} {getDisplayUnitName()}
              </span>
            </div>
          </div>

          {calculatedUsage < 0 && (
            <div className={`mt-4 p-4 ${theme.secondary} border ${theme.border} rounded-xl`}>
              <div className="flex items-center">
                <div className={`p-1 rounded-lg ${theme.accent} mr-2`}>
                  <span className="text-white text-xs">⚠️</span>
                </div>
                <span className={`${theme.textSecondary} text-sm font-medium`}>
                  Warning: New reading cannot be higher than available units
                </span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Submit button */}
      <button
        type="submit"
        disabled={isSubmitting || newReading <= 0 || newReading > state.currentUnits}
        className={`w-full bg-gradient-to-r ${theme.gradient} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
      >
        <div className="flex items-center justify-center gap-2">
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
              Recording Usage...
            </>
          ) : (
            <>
              <HiLightningBolt className="h-5 w-5" />
              Record Usage
            </>
          )}
        </div>
      </button>

      {/* Help text */}
      <div className={`text-center p-4 ${theme.card} rounded-xl border ${theme.border}`}>
        <div className="flex items-center justify-center mb-2">
          <div className={`p-1 rounded-lg bg-gradient-to-br ${theme.gradient} mr-2`}>
            <span className="text-white text-xs">💡</span>
          </div>
          <span className={`text-sm font-semibold ${theme.text}`}>How it works</span>
        </div>
        <p className={`text-xs ${theme.textSecondary} leading-relaxed`}>
          Record your current meter reading to track electricity usage.
          The system will calculate how many units you've used since the last recording.
        </p>
      </div>
    </form>
  )
}

export default UsageForm
