import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import UsageDial from './UsageDial'
import ThresholdWarning from './ThresholdWarning'
import HelpTooltip from '../Common/HelpTooltip'
import { HiLightningBolt, HiCurrencyDollar, HiTrendingUp } from 'react-icons/hi'

function Dashboard() {
  const navigate = useNavigate()
  const {
    state,
    isThresholdExceeded,
    getDisplayUnitName,
    weeklyPurchaseTotal,
    monthlyPurchaseTotal,
    weeklyUsageTotal,
    monthlyUsageTotal,
    usageSinceLastRecording
  } = useApp()
  const { theme, currentTheme } = useTheme()

  // Helper function to get appropriate background for cards in dark mode
  const getCardBackground = (lightBg, darkBg = 'bg-gray-800') => {
    return currentTheme === 'dark' ? darkBg : lightBg
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <div className="flex items-center gap-3">
          <h1 className={`text-3xl font-bold ${theme.text}`}>Dashboard</h1>
          <HelpTooltip
            title="Dashboard Overview"
            content="This is your main control center. Here you can see your current units, usage patterns, weekly/monthly totals, and quick access to all major functions. The dial shows your usage visually, and the cards below show real-time calculations."
            position="bottom"
          />
        </div>
        <p className={`mt-2 ${theme.textSecondary}`}>
          Monitor your electricity usage and current meter readings
        </p>
      </div>

      {/* Threshold warning */}
      {isThresholdExceeded && <ThresholdWarning />}



      {/* Usage visualization and recent activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Usage dial */}
        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border}`}>
          <h2 className={`text-xl font-semibold ${theme.text} mb-4 flex items-center gap-3`}>
            <div className={`p-2 rounded-xl bg-gradient-to-br ${theme.gradient} shadow-md`}>
              <HiLightningBolt className="h-5 w-5 text-white" />
            </div>
            Usage Overview
          </h2>
          <div className={`${theme.secondary} rounded-xl p-4`}>
            <UsageDial />
          </div>
        </div>

        {/* Recent activity */}
        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border}`}>
          <h2 className={`text-xl font-semibold ${theme.text} mb-4 flex items-center gap-3`}>
            <div className={`p-2 rounded-xl bg-gradient-to-br ${theme.gradient} shadow-md`}>
              <HiTrendingUp className="h-5 w-5 text-white" />
            </div>
            Recent Activity
          </h2>
          <div className="space-y-3">
            {/* Recent purchases */}
            {state.purchases.slice(0, 3).map((purchase) => (
              <div key={purchase.id} className={`p-4 ${theme.secondary} border ${theme.border} rounded-xl shadow-sm hover:shadow-md transition-all duration-200`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`p-2 rounded-lg ${theme.accent} shadow-sm`}>
                      <HiCurrencyDollar className="h-4 w-4 text-white" />
                    </div>
                    <div className="ml-3">
                      <p className={`text-sm font-semibold ${theme.text}`}>
                        Purchase: {state.currencySymbol || 'R'}{purchase.currency.toFixed(2)}
                      </p>
                      <p className={`text-xs ${theme.textSecondary}`}>
                        {new Date(purchase.date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <span className={`text-sm font-semibold ${theme.text}`}>
                    +{purchase.units.toFixed(2)} {getDisplayUnitName()}
                  </span>
                </div>
              </div>
            ))}

            {/* Recent usage */}
            {state.usageHistory.slice(0, 2).map((usage) => (
              <div key={usage.id} className={`p-4 ${theme.secondary} border ${theme.border} rounded-xl shadow-sm hover:shadow-md transition-all duration-200`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`p-2 rounded-lg ${theme.accent} shadow-sm`}>
                      <HiTrendingUp className="h-4 w-4 text-white" />
                    </div>
                    <div className="ml-3">
                      <p className={`text-sm font-semibold ${theme.text}`}>
                        Usage recorded
                      </p>
                      <p className={`text-xs ${theme.textSecondary}`}>
                        {new Date(usage.date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <span className={`text-sm font-semibold ${theme.text}`}>
                    -{usage.usage.toFixed(2)} {getDisplayUnitName()}
                  </span>
                </div>
              </div>
            ))}

            {/* Empty state */}
            {state.purchases.length === 0 && state.usageHistory.length === 0 && (
              <div className={`text-center py-12 ${theme.secondary} border ${theme.border} rounded-xl`}>
                <div className={`p-4 rounded-2xl bg-gradient-to-br ${theme.gradient} w-fit mx-auto mb-4`}>
                  <HiLightningBolt className={`h-12 w-12 text-white`} />
                </div>
                <p className={`text-sm ${theme.textSecondary} font-medium`}>
                  No recent activity
                </p>
                <p className={`text-xs ${theme.textSecondary} mt-1`}>
                  Start by making a purchase or recording usage
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border}`}>
          <h2 className={`text-2xl font-bold ${theme.text} mb-4 flex items-center gap-3`}>
            <div className={`p-3 rounded-2xl bg-gradient-to-br ${theme.gradient} shadow-lg`}>
              <HiLightningBolt className="h-6 w-6 text-white" />
            </div>
            Quick Actions
          </h2>
          <div className="space-y-3">
            <button
              onClick={() => navigate('/purchases')}
              className={`w-full flex items-center gap-4 p-4 bg-gradient-to-r ${theme.gradient} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
            >
              <HiCurrencyDollar className="h-6 w-6" />
              <div className="text-left">
                <span className="block text-lg font-semibold">Add Purchase</span>
                <span className="block text-sm opacity-80">Top up your units</span>
              </div>
            </button>
            <button
              onClick={() => navigate('/usage')}
              className={`w-full flex items-center gap-4 p-4 bg-gradient-to-r ${theme.gradient} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
            >
              <HiTrendingUp className="h-6 w-6" />
              <div className="text-left">
                <span className="block text-lg font-semibold">Record Usage</span>
                <span className="block text-sm opacity-80">Track consumption</span>
              </div>
            </button>
            <button
              onClick={() => navigate('/history')}
              className={`w-full flex items-center gap-4 p-4 bg-gradient-to-r ${theme.gradient} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
            >
              <HiLightningBolt className="h-6 w-6" />
              <div className="text-left">
                <span className="block text-lg font-semibold">View History</span>
                <span className="block text-sm opacity-80">See all records</span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
