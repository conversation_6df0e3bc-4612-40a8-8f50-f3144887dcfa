import React, { useState } from 'react'
import { useTheme } from '../../context/ThemeContext'
import { HiLightningBolt } from 'react-icons/hi'

function Logo({ size = 'md', animated = true, showText = true }) {
  const { theme } = useTheme()
  const [imageError, setImageError] = useState(false)

  const sizes = {
    sm: {
      logo: 'h-12 w-12',
      text: 'text-sm',
      icon: 'h-6 w-6'
    },
    md: {
      logo: 'h-16 w-16',
      text: 'text-lg',
      icon: 'h-8 w-8'
    },
    lg: {
      logo: 'h-20 w-20',
      text: 'text-xl',
      icon: 'h-10 w-10'
    },
    xl: {
      logo: 'h-24 w-24',
      text: 'text-2xl',
      icon: 'h-12 w-12'
    }
  }

  const currentSize = sizes[size] || sizes.md

  return (
    <div className="flex items-center gap-3">
      {/* Logo - Image with Lightning Bolt Fallback */}
      <div className={`${currentSize.logo} flex items-center justify-center`}>
        {!imageError ? (
          <img
            src="/oie_transparent (1).png"
            alt="Prepaid User Electricity Logo"
            className={`${currentSize.logo} object-contain`}
            onError={() => setImageError(true)}
            onLoad={() => setImageError(false)}
          />
        ) : (
          <div className={`${currentSize.logo} rounded-2xl bg-gradient-to-br ${theme.gradient} shadow-lg flex items-center justify-center ${animated ? 'animate-pulse' : ''}`}>
            <HiLightningBolt className={`${currentSize.icon} text-white`} />
          </div>
        )}
      </div>

      {/* App name */}
      {showText && (
        <div className="flex flex-col">
          <h1 className={`${currentSize.text} font-black ${theme.text} leading-tight`}>
            Prepaid User
          </h1>
          <p className={`text-base font-bold ${theme.textSecondary} tracking-wider leading-tight`}>
            Electricity
          </p>
        </div>
      )}
    </div>
  )
}

export default Logo
