@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
  margin: 0 auto;
  text-align: center;
}

/* Custom animations */
@keyframes lightning {
  0%, 100% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.1) rotate(-5deg); }
  50% { transform: scale(1.2) rotate(5deg); }
  75% { transform: scale(1.1) rotate(-2deg); }
}

.lightning-animation {
  animation: lightning 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8); }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Remove spinner arrows from number inputs */
/* Chrome, Safari, Edge, Opera */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

/* Prevent scroll wheel from changing number input values */
input[type="number"] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}

/* Additional protection against scroll events */
input[type="number"]:focus {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}

/* Mobile safe area support - Theme-aware and narrower approach */
.safe-top {
  padding-top: calc(env(safe-area-inset-top, 0px) + 0.5rem);
}

.safe-bottom {
  padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 0.25rem);
}

.safe-bottom-nav {
  padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 4rem);
}

/* Mobile-specific adjustments with narrower heights */
@media (max-width: 768px) {
  .mobile-safe-top {
    padding-top: calc(env(safe-area-inset-top, 0px) + 0.75rem);
  }

  .mobile-safe-bottom {
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 0.5rem);
  }

  .mobile-safe-bottom-nav {
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 4rem);
  }

  /* Narrower header spacing */
  .mobile-header {
    min-height: calc(3rem + env(safe-area-inset-top, 0px));
    padding-top: calc(env(safe-area-inset-top, 0px) + 0.5rem);
  }

  /* Narrower footer spacing */
  .mobile-footer {
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 0.25rem);
    min-height: calc(4rem + env(safe-area-inset-bottom, 0px));
  }
}

/* Theme-aware safe area backgrounds */
.safe-area-electric {
  background: linear-gradient(135deg, #60A5FA 0%, #3B82F6 100%);
}

.safe-area-green {
  background: linear-gradient(135deg, #34D399 0%, #10B981 100%);
}

.safe-area-teal {
  background: linear-gradient(135deg, #2DD4BF 0%, #14B8A6 100%);
}

.safe-area-pink {
  background: linear-gradient(135deg, #F472B6 0%, #EC4899 100%);
}

.safe-area-dark {
  background: linear-gradient(135deg, #374151 0%, #111827 100%);
}

/* Additional viewport height fixes for mobile */
@media (max-width: 768px) and (orientation: portrait) {
  html, body {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
  }

  #root {
    height: 100vh;
    height: 100dvh;
  }
}

/* Prevent zoom on input focus and ensure proper safe areas */
input, select, textarea {
  font-size: 16px;
  -webkit-user-select: text;
  user-select: text;
}

/* Disable number input spinners */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}
