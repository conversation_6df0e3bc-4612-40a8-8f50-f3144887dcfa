<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sidebar Test</title>
    <script>
        // Test the app in browser
        function testSidebar() {
            console.log('Testing sidebar functionality...');
            
            // Open the app
            window.open('http://localhost:5173/', '_blank');
            
            setTimeout(() => {
                console.log('✅ App should be loaded');
                console.log('✅ Sidebar should be CLOSED by default');
                console.log('✅ Click hamburger menu (☰) to test toggle');
                console.log('✅ Click outside sidebar to close');
                console.log('✅ Navigate between pages to test auto-close');
            }, 2000);
        }
        
        // Auto-run test when page loads
        window.onload = testSidebar;
    </script>
</head>
<body>
    <h1>Prepaid Meter App - Sidebar Test</h1>
    <p>This test verifies the sidebar functionality works correctly.</p>
    
    <h2>Expected Behavior:</h2>
    <ul>
        <li>✅ Sidebar starts CLOSED on all pages</li>
        <li>✅ Hamburger menu (☰) toggles sidebar open/closed</li>
        <li>✅ Clicking outside sidebar closes it</li>
        <li>✅ Navigating between pages closes sidebar</li>
        <li>✅ Works on both browser and mobile</li>
    </ul>
    
    <button onclick="testSidebar()">Run Test</button>
</body>
</html>
