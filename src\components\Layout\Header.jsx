import React from 'react'
import { HiMenu } from 'react-icons/hi'
import { useTheme } from '../../context/ThemeContext'
import { useApp } from '../../context/AppContext'
import Logo from '../Common/Logo'

function Header({ onMenuClick }) {
  const { theme, currentTheme } = useTheme()
  const { state } = useApp()

  // Helper function to get appropriate background for header with glassmorphism
  const getHeaderBackground = () => {
    if (currentTheme === 'dark') {
      return 'bg-gray-900/95 backdrop-blur-xl border-gray-700/50'
    }

    // For non-dark themes, use specific color classes with proper opacity (balanced lighter shade)
    const backgroundMap = {
      'electric': 'bg-blue-500/90 backdrop-blur-xl border-white/30',
      'green': 'bg-green-500/90 backdrop-blur-xl border-white/30',
      'teal': 'bg-teal-500/90 backdrop-blur-xl border-white/30',
      'pink': 'bg-pink-500/90 backdrop-blur-xl border-white/30'
    }

    return backgroundMap[currentTheme] || 'bg-blue-500/90 backdrop-blur-xl border-white/30'
  }

  // Helper function to get appropriate text color for header
  const getHeaderTextColor = () => {
    if (currentTheme === 'dark') {
      return theme.text // Keep existing text color for dark mode
    }
    // For non-dark themes, use white text with better contrast
    return 'text-white drop-shadow-sm'
  }

  return (
    <header className={`${getHeaderBackground()} border-b px-4 py-0.5 flex items-center justify-between shadow-xl`}>
      {/* Left side - Menu button and logo */}
      <div className="flex items-center space-x-2">
        <button
          onClick={onMenuClick}
          className={`hamburger-menu lg:hidden p-1.5 rounded-lg bg-white/15 backdrop-blur-sm text-white hover:bg-white/25 transition-all duration-300 shadow-lg`}
        >
          <HiMenu className="h-6 w-6" />
        </button>

        <div className="flex items-center">
          <Logo size="sm" animated={true} showText={false} />
        </div>
      </div>

      {/* Right side - Current units display (visible on all screen sizes) */}
      <div className={`flex items-center space-x-1.5 bg-black/30 backdrop-blur-md rounded-lg px-2 py-0.5 border border-white/30 shadow-lg`}>
        <div className="text-right">
          <p className={`text-xs ${getHeaderTextColor()}`}>Current Units</p>
          <p className={`text-sm font-bold ${getHeaderTextColor()}`}>
            {state.currentUnits.toFixed(2)}
          </p>
        </div>
        <div className={`w-1.5 h-1.5 rounded-full bg-white/80 pulse-glow shadow-sm`} />
      </div>
    </header>
  )
}

export default Header
