var Ug=Object.defineProperty;var Bg=(t,e,n)=>e in t?Ug(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var F=(t,e,n)=>Bg(t,typeof e!="symbol"?e+"":e,n);function Hg(t,e){for(var n=0;n<e.length;n++){const r=e[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in t)){const s=Object.getOwnPropertyDescriptor(r,i);s&&Object.defineProperty(t,i,s.get?s:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();function Wg(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var ff={exports:{}},Wo={},mf={exports:{}},I={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zi=Symbol.for("react.element"),Vg=Symbol.for("react.portal"),Yg=Symbol.for("react.fragment"),Xg=Symbol.for("react.strict_mode"),Kg=Symbol.for("react.profiler"),Qg=Symbol.for("react.provider"),Gg=Symbol.for("react.context"),qg=Symbol.for("react.forward_ref"),Zg=Symbol.for("react.suspense"),Jg=Symbol.for("react.memo"),e0=Symbol.for("react.lazy"),bu=Symbol.iterator;function t0(t){return t===null||typeof t!="object"?null:(t=bu&&t[bu]||t["@@iterator"],typeof t=="function"?t:null)}var pf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},gf=Object.assign,xf={};function zr(t,e,n){this.props=t,this.context=e,this.refs=xf,this.updater=n||pf}zr.prototype.isReactComponent={};zr.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};zr.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function vf(){}vf.prototype=zr.prototype;function cc(t,e,n){this.props=t,this.context=e,this.refs=xf,this.updater=n||pf}var uc=cc.prototype=new vf;uc.constructor=cc;gf(uc,zr.prototype);uc.isPureReactComponent=!0;var wu=Array.isArray,yf=Object.prototype.hasOwnProperty,dc={current:null},bf={key:!0,ref:!0,__self:!0,__source:!0};function wf(t,e,n){var r,i={},s=null,o=null;if(e!=null)for(r in e.ref!==void 0&&(o=e.ref),e.key!==void 0&&(s=""+e.key),e)yf.call(e,r)&&!bf.hasOwnProperty(r)&&(i[r]=e[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}if(t&&t.defaultProps)for(r in a=t.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:Zi,type:t,key:s,ref:o,props:i,_owner:dc.current}}function n0(t,e){return{$$typeof:Zi,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}function hc(t){return typeof t=="object"&&t!==null&&t.$$typeof===Zi}function r0(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var Su=/\/+/g;function fa(t,e){return typeof t=="object"&&t!==null&&t.key!=null?r0(""+t.key):e.toString(36)}function zs(t,e,n,r,i){var s=typeof t;(s==="undefined"||s==="boolean")&&(t=null);var o=!1;if(t===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(t.$$typeof){case Zi:case Vg:o=!0}}if(o)return o=t,i=i(o),t=r===""?"."+fa(o,0):r,wu(i)?(n="",t!=null&&(n=t.replace(Su,"$&/")+"/"),zs(i,e,n,"",function(u){return u})):i!=null&&(hc(i)&&(i=n0(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(Su,"$&/")+"/")+t)),e.push(i)),1;if(o=0,r=r===""?".":r+":",wu(t))for(var a=0;a<t.length;a++){s=t[a];var l=r+fa(s,a);o+=zs(s,e,n,l,i)}else if(l=t0(t),typeof l=="function")for(t=l.call(t),a=0;!(s=t.next()).done;)s=s.value,l=r+fa(s,a++),o+=zs(s,e,n,l,i);else if(s==="object")throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");return o}function ls(t,e,n){if(t==null)return t;var r=[],i=0;return zs(t,r,"","",function(s){return e.call(n,s,i++)}),r}function i0(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var De={current:null},Is={transition:null},s0={ReactCurrentDispatcher:De,ReactCurrentBatchConfig:Is,ReactCurrentOwner:dc};function Sf(){throw Error("act(...) is not supported in production builds of React.")}I.Children={map:ls,forEach:function(t,e,n){ls(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return ls(t,function(){e++}),e},toArray:function(t){return ls(t,function(e){return e})||[]},only:function(t){if(!hc(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};I.Component=zr;I.Fragment=Yg;I.Profiler=Kg;I.PureComponent=cc;I.StrictMode=Xg;I.Suspense=Zg;I.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=s0;I.act=Sf;I.cloneElement=function(t,e,n){if(t==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var r=gf({},t.props),i=t.key,s=t.ref,o=t._owner;if(e!=null){if(e.ref!==void 0&&(s=e.ref,o=dc.current),e.key!==void 0&&(i=""+e.key),t.type&&t.type.defaultProps)var a=t.type.defaultProps;for(l in e)yf.call(e,l)&&!bf.hasOwnProperty(l)&&(r[l]=e[l]===void 0&&a!==void 0?a[l]:e[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:Zi,type:t.type,key:i,ref:s,props:r,_owner:o}};I.createContext=function(t){return t={$$typeof:Gg,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},t.Provider={$$typeof:Qg,_context:t},t.Consumer=t};I.createElement=wf;I.createFactory=function(t){var e=wf.bind(null,t);return e.type=t,e};I.createRef=function(){return{current:null}};I.forwardRef=function(t){return{$$typeof:qg,render:t}};I.isValidElement=hc;I.lazy=function(t){return{$$typeof:e0,_payload:{_status:-1,_result:t},_init:i0}};I.memo=function(t,e){return{$$typeof:Jg,type:t,compare:e===void 0?null:e}};I.startTransition=function(t){var e=Is.transition;Is.transition={};try{t()}finally{Is.transition=e}};I.unstable_act=Sf;I.useCallback=function(t,e){return De.current.useCallback(t,e)};I.useContext=function(t){return De.current.useContext(t)};I.useDebugValue=function(){};I.useDeferredValue=function(t){return De.current.useDeferredValue(t)};I.useEffect=function(t,e){return De.current.useEffect(t,e)};I.useId=function(){return De.current.useId()};I.useImperativeHandle=function(t,e,n){return De.current.useImperativeHandle(t,e,n)};I.useInsertionEffect=function(t,e){return De.current.useInsertionEffect(t,e)};I.useLayoutEffect=function(t,e){return De.current.useLayoutEffect(t,e)};I.useMemo=function(t,e){return De.current.useMemo(t,e)};I.useReducer=function(t,e,n){return De.current.useReducer(t,e,n)};I.useRef=function(t){return De.current.useRef(t)};I.useState=function(t){return De.current.useState(t)};I.useSyncExternalStore=function(t,e,n){return De.current.useSyncExternalStore(t,e,n)};I.useTransition=function(){return De.current.useTransition()};I.version="18.3.1";mf.exports=I;var N=mf.exports;const pt=Wg(N),o0=Hg({__proto__:null,default:pt},[N]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var a0=N,l0=Symbol.for("react.element"),c0=Symbol.for("react.fragment"),u0=Object.prototype.hasOwnProperty,d0=a0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,h0={key:!0,ref:!0,__self:!0,__source:!0};function _f(t,e,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),e.key!==void 0&&(s=""+e.key),e.ref!==void 0&&(o=e.ref);for(r in e)u0.call(e,r)&&!h0.hasOwnProperty(r)&&(i[r]=e[r]);if(t&&t.defaultProps)for(r in e=t.defaultProps,e)i[r]===void 0&&(i[r]=e[r]);return{$$typeof:l0,type:t,key:s,ref:o,props:i,_owner:d0.current}}Wo.Fragment=c0;Wo.jsx=_f;Wo.jsxs=_f;ff.exports=Wo;var c=ff.exports,el={},kf={exports:{}},Ze={},Nf={exports:{}},jf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(t){function e(M,L){var A=M.length;M.push(L);e:for(;0<A;){var Q=A-1>>>1,G=M[Q];if(0<i(G,L))M[Q]=L,M[A]=G,A=Q;else break e}}function n(M){return M.length===0?null:M[0]}function r(M){if(M.length===0)return null;var L=M[0],A=M.pop();if(A!==L){M[0]=A;e:for(var Q=0,G=M.length,yt=G>>>1;Q<yt;){var Me=2*(Q+1)-1,$t=M[Me],$e=Me+1,as=M[$e];if(0>i($t,A))$e<G&&0>i(as,$t)?(M[Q]=as,M[$e]=A,Q=$e):(M[Q]=$t,M[Me]=A,Q=Me);else if($e<G&&0>i(as,A))M[Q]=as,M[$e]=A,Q=$e;else break e}}return L}function i(M,L){var A=M.sortIndex-L.sortIndex;return A!==0?A:M.id-L.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;t.unstable_now=function(){return s.now()}}else{var o=Date,a=o.now();t.unstable_now=function(){return o.now()-a}}var l=[],u=[],d=1,h=null,f=3,m=!1,x=!1,v=!1,b=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(M){for(var L=n(u);L!==null;){if(L.callback===null)r(u);else if(L.startTime<=M)r(u),L.sortIndex=L.expirationTime,e(l,L);else break;L=n(u)}}function w(M){if(v=!1,y(M),!x)if(n(l)!==null)x=!0,B(_);else{var L=n(u);L!==null&&R(w,L.startTime-M)}}function _(M,L){x=!1,v&&(v=!1,g(j),j=-1),m=!0;var A=f;try{for(y(L),h=n(l);h!==null&&(!(h.expirationTime>L)||M&&!$());){var Q=h.callback;if(typeof Q=="function"){h.callback=null,f=h.priorityLevel;var G=Q(h.expirationTime<=L);L=t.unstable_now(),typeof G=="function"?h.callback=G:h===n(l)&&r(l),y(L)}else r(l);h=n(l)}if(h!==null)var yt=!0;else{var Me=n(u);Me!==null&&R(w,Me.startTime-L),yt=!1}return yt}finally{h=null,f=A,m=!1}}var S=!1,k=null,j=-1,E=5,C=-1;function $(){return!(t.unstable_now()-C<E)}function D(){if(k!==null){var M=t.unstable_now();C=M;var L=!0;try{L=k(!0,M)}finally{L?T():(S=!1,k=null)}}else S=!1}var T;if(typeof p=="function")T=function(){p(D)};else if(typeof MessageChannel<"u"){var U=new MessageChannel,z=U.port2;U.port1.onmessage=D,T=function(){z.postMessage(null)}}else T=function(){b(D,0)};function B(M){k=M,S||(S=!0,T())}function R(M,L){j=b(function(){M(t.unstable_now())},L)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(M){M.callback=null},t.unstable_continueExecution=function(){x||m||(x=!0,B(_))},t.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):E=0<M?Math.floor(1e3/M):5},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_getFirstCallbackNode=function(){return n(l)},t.unstable_next=function(M){switch(f){case 1:case 2:case 3:var L=3;break;default:L=f}var A=f;f=L;try{return M()}finally{f=A}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(M,L){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var A=f;f=M;try{return L()}finally{f=A}},t.unstable_scheduleCallback=function(M,L,A){var Q=t.unstable_now();switch(typeof A=="object"&&A!==null?(A=A.delay,A=typeof A=="number"&&0<A?Q+A:Q):A=Q,M){case 1:var G=-1;break;case 2:G=250;break;case 5:G=**********;break;case 4:G=1e4;break;default:G=5e3}return G=A+G,M={id:d++,callback:L,priorityLevel:M,startTime:A,expirationTime:G,sortIndex:-1},A>Q?(M.sortIndex=A,e(u,M),n(l)===null&&M===n(u)&&(v?(g(j),j=-1):v=!0,R(w,A-Q))):(M.sortIndex=G,e(l,M),x||m||(x=!0,B(_))),M},t.unstable_shouldYield=$,t.unstable_wrapCallback=function(M){var L=f;return function(){var A=f;f=L;try{return M.apply(this,arguments)}finally{f=A}}}})(jf);Nf.exports=jf;var f0=Nf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var m0=N,qe=f0;function P(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Cf=new Set,Ci={};function Gn(t,e){Cr(t,e),Cr(t+"Capture",e)}function Cr(t,e){for(Ci[t]=e,t=0;t<e.length;t++)Cf.add(e[t])}var Bt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),tl=Object.prototype.hasOwnProperty,p0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,_u={},ku={};function g0(t){return tl.call(ku,t)?!0:tl.call(_u,t)?!1:p0.test(t)?ku[t]=!0:(_u[t]=!0,!1)}function x0(t,e,n,r){if(n!==null&&n.type===0)return!1;switch(typeof e){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function v0(t,e,n,r){if(e===null||typeof e>"u"||x0(t,e,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!e;case 4:return e===!1;case 5:return isNaN(e);case 6:return isNaN(e)||1>e}return!1}function Oe(t,e,n,r,i,s,o){this.acceptsBooleans=e===2||e===3||e===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=t,this.type=e,this.sanitizeURL=s,this.removeEmptyString=o}var _e={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){_e[t]=new Oe(t,0,!1,t,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var e=t[0];_e[e]=new Oe(e,1,!1,t[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(t){_e[t]=new Oe(t,2,!1,t.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){_e[t]=new Oe(t,2,!1,t,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){_e[t]=new Oe(t,3,!1,t.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(t){_e[t]=new Oe(t,3,!0,t,null,!1,!1)});["capture","download"].forEach(function(t){_e[t]=new Oe(t,4,!1,t,null,!1,!1)});["cols","rows","size","span"].forEach(function(t){_e[t]=new Oe(t,6,!1,t,null,!1,!1)});["rowSpan","start"].forEach(function(t){_e[t]=new Oe(t,5,!1,t.toLowerCase(),null,!1,!1)});var fc=/[\-:]([a-z])/g;function mc(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var e=t.replace(fc,mc);_e[e]=new Oe(e,1,!1,t,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var e=t.replace(fc,mc);_e[e]=new Oe(e,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(t){var e=t.replace(fc,mc);_e[e]=new Oe(e,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(t){_e[t]=new Oe(t,1,!1,t.toLowerCase(),null,!1,!1)});_e.xlinkHref=new Oe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(t){_e[t]=new Oe(t,1,!1,t.toLowerCase(),null,!0,!0)});function pc(t,e,n,r){var i=_e.hasOwnProperty(e)?_e[e]:null;(i!==null?i.type!==0:r||!(2<e.length)||e[0]!=="o"&&e[0]!=="O"||e[1]!=="n"&&e[1]!=="N")&&(v0(e,n,i,r)&&(n=null),r||i===null?g0(e)&&(n===null?t.removeAttribute(e):t.setAttribute(e,""+n)):i.mustUseProperty?t[i.propertyName]=n===null?i.type===3?!1:"":n:(e=i.attributeName,r=i.attributeNamespace,n===null?t.removeAttribute(e):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?t.setAttributeNS(r,e,n):t.setAttribute(e,n))))}var Xt=m0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,cs=Symbol.for("react.element"),sr=Symbol.for("react.portal"),or=Symbol.for("react.fragment"),gc=Symbol.for("react.strict_mode"),nl=Symbol.for("react.profiler"),Pf=Symbol.for("react.provider"),Ef=Symbol.for("react.context"),xc=Symbol.for("react.forward_ref"),rl=Symbol.for("react.suspense"),il=Symbol.for("react.suspense_list"),vc=Symbol.for("react.memo"),qt=Symbol.for("react.lazy"),Mf=Symbol.for("react.offscreen"),Nu=Symbol.iterator;function Vr(t){return t===null||typeof t!="object"?null:(t=Nu&&t[Nu]||t["@@iterator"],typeof t=="function"?t:null)}var oe=Object.assign,ma;function oi(t){if(ma===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);ma=e&&e[1]||""}return`
`+ma+t}var pa=!1;function ga(t,e){if(!t||pa)return"";pa=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(e)if(e=function(){throw Error()},Object.defineProperty(e.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(e,[])}catch(u){var r=u}Reflect.construct(t,[],e)}else{try{e.call()}catch(u){r=u}t.call(e.prototype)}else{try{throw Error()}catch(u){r=u}t()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,a=s.length-1;1<=o&&0<=a&&i[o]!==s[a];)a--;for(;1<=o&&0<=a;o--,a--)if(i[o]!==s[a]){if(o!==1||a!==1)do if(o--,a--,0>a||i[o]!==s[a]){var l=`
`+i[o].replace(" at new "," at ");return t.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",t.displayName)),l}while(1<=o&&0<=a);break}}}finally{pa=!1,Error.prepareStackTrace=n}return(t=t?t.displayName||t.name:"")?oi(t):""}function y0(t){switch(t.tag){case 5:return oi(t.type);case 16:return oi("Lazy");case 13:return oi("Suspense");case 19:return oi("SuspenseList");case 0:case 2:case 15:return t=ga(t.type,!1),t;case 11:return t=ga(t.type.render,!1),t;case 1:return t=ga(t.type,!0),t;default:return""}}function sl(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case or:return"Fragment";case sr:return"Portal";case nl:return"Profiler";case gc:return"StrictMode";case rl:return"Suspense";case il:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case Ef:return(t.displayName||"Context")+".Consumer";case Pf:return(t._context.displayName||"Context")+".Provider";case xc:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case vc:return e=t.displayName||null,e!==null?e:sl(t.type)||"Memo";case qt:e=t._payload,t=t._init;try{return sl(t(e))}catch{}}return null}function b0(t){var e=t.type;switch(t.tag){case 24:return"Cache";case 9:return(e.displayName||"Context")+".Consumer";case 10:return(e._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=e.render,t=t.displayName||t.name||"",e.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return e;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return sl(e);case 8:return e===gc?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e}return null}function yn(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function $f(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function w0(t){var e=$f(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function us(t){t._valueTracker||(t._valueTracker=w0(t))}function Tf(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),r="";return t&&(r=$f(t)?t.checked?"true":"false":t.value),t=r,t!==n?(e.setValue(t),!0):!1}function no(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}function ol(t,e){var n=e.checked;return oe({},e,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??t._wrapperState.initialChecked})}function ju(t,e){var n=e.defaultValue==null?"":e.defaultValue,r=e.checked!=null?e.checked:e.defaultChecked;n=yn(e.value!=null?e.value:n),t._wrapperState={initialChecked:r,initialValue:n,controlled:e.type==="checkbox"||e.type==="radio"?e.checked!=null:e.value!=null}}function Rf(t,e){e=e.checked,e!=null&&pc(t,"checked",e,!1)}function al(t,e){Rf(t,e);var n=yn(e.value),r=e.type;if(n!=null)r==="number"?(n===0&&t.value===""||t.value!=n)&&(t.value=""+n):t.value!==""+n&&(t.value=""+n);else if(r==="submit"||r==="reset"){t.removeAttribute("value");return}e.hasOwnProperty("value")?ll(t,e.type,n):e.hasOwnProperty("defaultValue")&&ll(t,e.type,yn(e.defaultValue)),e.checked==null&&e.defaultChecked!=null&&(t.defaultChecked=!!e.defaultChecked)}function Cu(t,e,n){if(e.hasOwnProperty("value")||e.hasOwnProperty("defaultValue")){var r=e.type;if(!(r!=="submit"&&r!=="reset"||e.value!==void 0&&e.value!==null))return;e=""+t._wrapperState.initialValue,n||e===t.value||(t.value=e),t.defaultValue=e}n=t.name,n!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,n!==""&&(t.name=n)}function ll(t,e,n){(e!=="number"||no(t.ownerDocument)!==t)&&(n==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+n&&(t.defaultValue=""+n))}var ai=Array.isArray;function xr(t,e,n,r){if(t=t.options,e){e={};for(var i=0;i<n.length;i++)e["$"+n[i]]=!0;for(n=0;n<t.length;n++)i=e.hasOwnProperty("$"+t[n].value),t[n].selected!==i&&(t[n].selected=i),i&&r&&(t[n].defaultSelected=!0)}else{for(n=""+yn(n),e=null,i=0;i<t.length;i++){if(t[i].value===n){t[i].selected=!0,r&&(t[i].defaultSelected=!0);return}e!==null||t[i].disabled||(e=t[i])}e!==null&&(e.selected=!0)}}function cl(t,e){if(e.dangerouslySetInnerHTML!=null)throw Error(P(91));return oe({},e,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function Pu(t,e){var n=e.value;if(n==null){if(n=e.children,e=e.defaultValue,n!=null){if(e!=null)throw Error(P(92));if(ai(n)){if(1<n.length)throw Error(P(93));n=n[0]}e=n}e==null&&(e=""),n=e}t._wrapperState={initialValue:yn(n)}}function Df(t,e){var n=yn(e.value),r=yn(e.defaultValue);n!=null&&(n=""+n,n!==t.value&&(t.value=n),e.defaultValue==null&&t.defaultValue!==n&&(t.defaultValue=n)),r!=null&&(t.defaultValue=""+r)}function Eu(t){var e=t.textContent;e===t._wrapperState.initialValue&&e!==""&&e!==null&&(t.value=e)}function Of(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ul(t,e){return t==null||t==="http://www.w3.org/1999/xhtml"?Of(e):t==="http://www.w3.org/2000/svg"&&e==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var ds,Lf=function(t){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(e,n,r,i){MSApp.execUnsafeLocalFunction(function(){return t(e,n,r,i)})}:t}(function(t,e){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=e;else{for(ds=ds||document.createElement("div"),ds.innerHTML="<svg>"+e.valueOf().toString()+"</svg>",e=ds.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;e.firstChild;)t.appendChild(e.firstChild)}});function Pi(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var pi={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},S0=["Webkit","ms","Moz","O"];Object.keys(pi).forEach(function(t){S0.forEach(function(e){e=e+t.charAt(0).toUpperCase()+t.substring(1),pi[e]=pi[t]})});function Af(t,e,n){return e==null||typeof e=="boolean"||e===""?"":n||typeof e!="number"||e===0||pi.hasOwnProperty(t)&&pi[t]?(""+e).trim():e+"px"}function Ff(t,e){t=t.style;for(var n in e)if(e.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Af(n,e[n],r);n==="float"&&(n="cssFloat"),r?t.setProperty(n,i):t[n]=i}}var _0=oe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function dl(t,e){if(e){if(_0[t]&&(e.children!=null||e.dangerouslySetInnerHTML!=null))throw Error(P(137,t));if(e.dangerouslySetInnerHTML!=null){if(e.children!=null)throw Error(P(60));if(typeof e.dangerouslySetInnerHTML!="object"||!("__html"in e.dangerouslySetInnerHTML))throw Error(P(61))}if(e.style!=null&&typeof e.style!="object")throw Error(P(62))}}function hl(t,e){if(t.indexOf("-")===-1)return typeof e.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var fl=null;function yc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ml=null,vr=null,yr=null;function Mu(t){if(t=ts(t)){if(typeof ml!="function")throw Error(P(280));var e=t.stateNode;e&&(e=Qo(e),ml(t.stateNode,t.type,e))}}function zf(t){vr?yr?yr.push(t):yr=[t]:vr=t}function If(){if(vr){var t=vr,e=yr;if(yr=vr=null,Mu(t),e)for(t=0;t<e.length;t++)Mu(e[t])}}function Uf(t,e){return t(e)}function Bf(){}var xa=!1;function Hf(t,e,n){if(xa)return t(e,n);xa=!0;try{return Uf(t,e,n)}finally{xa=!1,(vr!==null||yr!==null)&&(Bf(),If())}}function Ei(t,e){var n=t.stateNode;if(n===null)return null;var r=Qo(n);if(r===null)return null;n=r[e];e:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break e;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(P(231,e,typeof n));return n}var pl=!1;if(Bt)try{var Yr={};Object.defineProperty(Yr,"passive",{get:function(){pl=!0}}),window.addEventListener("test",Yr,Yr),window.removeEventListener("test",Yr,Yr)}catch{pl=!1}function k0(t,e,n,r,i,s,o,a,l){var u=Array.prototype.slice.call(arguments,3);try{e.apply(n,u)}catch(d){this.onError(d)}}var gi=!1,ro=null,io=!1,gl=null,N0={onError:function(t){gi=!0,ro=t}};function j0(t,e,n,r,i,s,o,a,l){gi=!1,ro=null,k0.apply(N0,arguments)}function C0(t,e,n,r,i,s,o,a,l){if(j0.apply(this,arguments),gi){if(gi){var u=ro;gi=!1,ro=null}else throw Error(P(198));io||(io=!0,gl=u)}}function qn(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function Wf(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function $u(t){if(qn(t)!==t)throw Error(P(188))}function P0(t){var e=t.alternate;if(!e){if(e=qn(t),e===null)throw Error(P(188));return e!==t?null:t}for(var n=t,r=e;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return $u(i),t;if(s===r)return $u(i),e;s=s.sibling}throw Error(P(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o){for(a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o)throw Error(P(189))}}if(n.alternate!==r)throw Error(P(190))}if(n.tag!==3)throw Error(P(188));return n.stateNode.current===n?t:e}function Vf(t){return t=P0(t),t!==null?Yf(t):null}function Yf(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var e=Yf(t);if(e!==null)return e;t=t.sibling}return null}var Xf=qe.unstable_scheduleCallback,Tu=qe.unstable_cancelCallback,E0=qe.unstable_shouldYield,M0=qe.unstable_requestPaint,ce=qe.unstable_now,$0=qe.unstable_getCurrentPriorityLevel,bc=qe.unstable_ImmediatePriority,Kf=qe.unstable_UserBlockingPriority,so=qe.unstable_NormalPriority,T0=qe.unstable_LowPriority,Qf=qe.unstable_IdlePriority,Vo=null,Ct=null;function R0(t){if(Ct&&typeof Ct.onCommitFiberRoot=="function")try{Ct.onCommitFiberRoot(Vo,t,void 0,(t.current.flags&128)===128)}catch{}}var gt=Math.clz32?Math.clz32:L0,D0=Math.log,O0=Math.LN2;function L0(t){return t>>>=0,t===0?32:31-(D0(t)/O0|0)|0}var hs=64,fs=4194304;function li(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function oo(t,e){var n=t.pendingLanes;if(n===0)return 0;var r=0,i=t.suspendedLanes,s=t.pingedLanes,o=n&268435455;if(o!==0){var a=o&~i;a!==0?r=li(a):(s&=o,s!==0&&(r=li(s)))}else o=n&~i,o!==0?r=li(o):s!==0&&(r=li(s));if(r===0)return 0;if(e!==0&&e!==r&&!(e&i)&&(i=r&-r,s=e&-e,i>=s||i===16&&(s&4194240)!==0))return e;if(r&4&&(r|=n&16),e=t.entangledLanes,e!==0)for(t=t.entanglements,e&=r;0<e;)n=31-gt(e),i=1<<n,r|=t[n],e&=~i;return r}function A0(t,e){switch(t){case 1:case 2:case 4:return e+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function F0(t,e){for(var n=t.suspendedLanes,r=t.pingedLanes,i=t.expirationTimes,s=t.pendingLanes;0<s;){var o=31-gt(s),a=1<<o,l=i[o];l===-1?(!(a&n)||a&r)&&(i[o]=A0(a,e)):l<=e&&(t.expiredLanes|=a),s&=~a}}function xl(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function Gf(){var t=hs;return hs<<=1,!(hs&4194240)&&(hs=64),t}function va(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Ji(t,e,n){t.pendingLanes|=e,e!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,e=31-gt(e),t[e]=n}function z0(t,e){var n=t.pendingLanes&~e;t.pendingLanes=e,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=e,t.mutableReadLanes&=e,t.entangledLanes&=e,e=t.entanglements;var r=t.eventTimes;for(t=t.expirationTimes;0<n;){var i=31-gt(n),s=1<<i;e[i]=0,r[i]=-1,t[i]=-1,n&=~s}}function wc(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var r=31-gt(n),i=1<<r;i&e|t[r]&e&&(t[r]|=e),n&=~i}}var K=0;function qf(t){return t&=-t,1<t?4<t?t&268435455?16:536870912:4:1}var Zf,Sc,Jf,em,tm,vl=!1,ms=[],an=null,ln=null,cn=null,Mi=new Map,$i=new Map,Jt=[],I0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ru(t,e){switch(t){case"focusin":case"focusout":an=null;break;case"dragenter":case"dragleave":ln=null;break;case"mouseover":case"mouseout":cn=null;break;case"pointerover":case"pointerout":Mi.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":$i.delete(e.pointerId)}}function Xr(t,e,n,r,i,s){return t===null||t.nativeEvent!==s?(t={blockedOn:e,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},e!==null&&(e=ts(e),e!==null&&Sc(e)),t):(t.eventSystemFlags|=r,e=t.targetContainers,i!==null&&e.indexOf(i)===-1&&e.push(i),t)}function U0(t,e,n,r,i){switch(e){case"focusin":return an=Xr(an,t,e,n,r,i),!0;case"dragenter":return ln=Xr(ln,t,e,n,r,i),!0;case"mouseover":return cn=Xr(cn,t,e,n,r,i),!0;case"pointerover":var s=i.pointerId;return Mi.set(s,Xr(Mi.get(s)||null,t,e,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,$i.set(s,Xr($i.get(s)||null,t,e,n,r,i)),!0}return!1}function nm(t){var e=On(t.target);if(e!==null){var n=qn(e);if(n!==null){if(e=n.tag,e===13){if(e=Wf(n),e!==null){t.blockedOn=e,tm(t.priority,function(){Jf(n)});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Us(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=yl(t.domEventName,t.eventSystemFlags,e[0],t.nativeEvent);if(n===null){n=t.nativeEvent;var r=new n.constructor(n.type,n);fl=r,n.target.dispatchEvent(r),fl=null}else return e=ts(n),e!==null&&Sc(e),t.blockedOn=n,!1;e.shift()}return!0}function Du(t,e,n){Us(t)&&n.delete(e)}function B0(){vl=!1,an!==null&&Us(an)&&(an=null),ln!==null&&Us(ln)&&(ln=null),cn!==null&&Us(cn)&&(cn=null),Mi.forEach(Du),$i.forEach(Du)}function Kr(t,e){t.blockedOn===e&&(t.blockedOn=null,vl||(vl=!0,qe.unstable_scheduleCallback(qe.unstable_NormalPriority,B0)))}function Ti(t){function e(i){return Kr(i,t)}if(0<ms.length){Kr(ms[0],t);for(var n=1;n<ms.length;n++){var r=ms[n];r.blockedOn===t&&(r.blockedOn=null)}}for(an!==null&&Kr(an,t),ln!==null&&Kr(ln,t),cn!==null&&Kr(cn,t),Mi.forEach(e),$i.forEach(e),n=0;n<Jt.length;n++)r=Jt[n],r.blockedOn===t&&(r.blockedOn=null);for(;0<Jt.length&&(n=Jt[0],n.blockedOn===null);)nm(n),n.blockedOn===null&&Jt.shift()}var br=Xt.ReactCurrentBatchConfig,ao=!0;function H0(t,e,n,r){var i=K,s=br.transition;br.transition=null;try{K=1,_c(t,e,n,r)}finally{K=i,br.transition=s}}function W0(t,e,n,r){var i=K,s=br.transition;br.transition=null;try{K=4,_c(t,e,n,r)}finally{K=i,br.transition=s}}function _c(t,e,n,r){if(ao){var i=yl(t,e,n,r);if(i===null)Pa(t,e,r,lo,n),Ru(t,r);else if(U0(i,t,e,n,r))r.stopPropagation();else if(Ru(t,r),e&4&&-1<I0.indexOf(t)){for(;i!==null;){var s=ts(i);if(s!==null&&Zf(s),s=yl(t,e,n,r),s===null&&Pa(t,e,r,lo,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else Pa(t,e,r,null,n)}}var lo=null;function yl(t,e,n,r){if(lo=null,t=yc(r),t=On(t),t!==null)if(e=qn(t),e===null)t=null;else if(n=e.tag,n===13){if(t=Wf(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null);return lo=t,null}function rm(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch($0()){case bc:return 1;case Kf:return 4;case so:case T0:return 16;case Qf:return 536870912;default:return 16}default:return 16}}var tn=null,kc=null,Bs=null;function im(){if(Bs)return Bs;var t,e=kc,n=e.length,r,i="value"in tn?tn.value:tn.textContent,s=i.length;for(t=0;t<n&&e[t]===i[t];t++);var o=n-t;for(r=1;r<=o&&e[n-r]===i[s-r];r++);return Bs=i.slice(t,1<r?1-r:void 0)}function Hs(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function ps(){return!0}function Ou(){return!1}function Je(t){function e(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var a in t)t.hasOwnProperty(a)&&(n=t[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?ps:Ou,this.isPropagationStopped=Ou,this}return oe(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ps)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ps)},persist:function(){},isPersistent:ps}),e}var Ir={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Nc=Je(Ir),es=oe({},Ir,{view:0,detail:0}),V0=Je(es),ya,ba,Qr,Yo=oe({},es,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:jc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Qr&&(Qr&&t.type==="mousemove"?(ya=t.screenX-Qr.screenX,ba=t.screenY-Qr.screenY):ba=ya=0,Qr=t),ya)},movementY:function(t){return"movementY"in t?t.movementY:ba}}),Lu=Je(Yo),Y0=oe({},Yo,{dataTransfer:0}),X0=Je(Y0),K0=oe({},es,{relatedTarget:0}),wa=Je(K0),Q0=oe({},Ir,{animationName:0,elapsedTime:0,pseudoElement:0}),G0=Je(Q0),q0=oe({},Ir,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Z0=Je(q0),J0=oe({},Ir,{data:0}),Au=Je(J0),ex={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},tx={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},nx={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function rx(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=nx[t])?!!e[t]:!1}function jc(){return rx}var ix=oe({},es,{key:function(t){if(t.key){var e=ex[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Hs(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?tx[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:jc,charCode:function(t){return t.type==="keypress"?Hs(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Hs(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),sx=Je(ix),ox=oe({},Yo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Fu=Je(ox),ax=oe({},es,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:jc}),lx=Je(ax),cx=oe({},Ir,{propertyName:0,elapsedTime:0,pseudoElement:0}),ux=Je(cx),dx=oe({},Yo,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),hx=Je(dx),fx=[9,13,27,32],Cc=Bt&&"CompositionEvent"in window,xi=null;Bt&&"documentMode"in document&&(xi=document.documentMode);var mx=Bt&&"TextEvent"in window&&!xi,sm=Bt&&(!Cc||xi&&8<xi&&11>=xi),zu=" ",Iu=!1;function om(t,e){switch(t){case"keyup":return fx.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function am(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ar=!1;function px(t,e){switch(t){case"compositionend":return am(e);case"keypress":return e.which!==32?null:(Iu=!0,zu);case"textInput":return t=e.data,t===zu&&Iu?null:t;default:return null}}function gx(t,e){if(ar)return t==="compositionend"||!Cc&&om(t,e)?(t=im(),Bs=kc=tn=null,ar=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return sm&&e.locale!=="ko"?null:e.data;default:return null}}var xx={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Uu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!xx[t.type]:e==="textarea"}function lm(t,e,n,r){zf(r),e=co(e,"onChange"),0<e.length&&(n=new Nc("onChange","change",null,n,r),t.push({event:n,listeners:e}))}var vi=null,Ri=null;function vx(t){ym(t,0)}function Xo(t){var e=ur(t);if(Tf(e))return t}function yx(t,e){if(t==="change")return e}var cm=!1;if(Bt){var Sa;if(Bt){var _a="oninput"in document;if(!_a){var Bu=document.createElement("div");Bu.setAttribute("oninput","return;"),_a=typeof Bu.oninput=="function"}Sa=_a}else Sa=!1;cm=Sa&&(!document.documentMode||9<document.documentMode)}function Hu(){vi&&(vi.detachEvent("onpropertychange",um),Ri=vi=null)}function um(t){if(t.propertyName==="value"&&Xo(Ri)){var e=[];lm(e,Ri,t,yc(t)),Hf(vx,e)}}function bx(t,e,n){t==="focusin"?(Hu(),vi=e,Ri=n,vi.attachEvent("onpropertychange",um)):t==="focusout"&&Hu()}function wx(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Xo(Ri)}function Sx(t,e){if(t==="click")return Xo(e)}function _x(t,e){if(t==="input"||t==="change")return Xo(e)}function kx(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var vt=typeof Object.is=="function"?Object.is:kx;function Di(t,e){if(vt(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!tl.call(e,i)||!vt(t[i],e[i]))return!1}return!0}function Wu(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Vu(t,e){var n=Wu(t);t=0;for(var r;n;){if(n.nodeType===3){if(r=t+n.textContent.length,t<=e&&r>=e)return{node:n,offset:e-t};t=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Wu(n)}}function dm(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?dm(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function hm(){for(var t=window,e=no();e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=no(t.document)}return e}function Pc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}function Nx(t){var e=hm(),n=t.focusedElem,r=t.selectionRange;if(e!==n&&n&&n.ownerDocument&&dm(n.ownerDocument.documentElement,n)){if(r!==null&&Pc(n)){if(e=r.start,t=r.end,t===void 0&&(t=e),"selectionStart"in n)n.selectionStart=e,n.selectionEnd=Math.min(t,n.value.length);else if(t=(e=n.ownerDocument||document)&&e.defaultView||window,t.getSelection){t=t.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!t.extend&&s>r&&(i=r,r=s,s=i),i=Vu(n,s);var o=Vu(n,r);i&&o&&(t.rangeCount!==1||t.anchorNode!==i.node||t.anchorOffset!==i.offset||t.focusNode!==o.node||t.focusOffset!==o.offset)&&(e=e.createRange(),e.setStart(i.node,i.offset),t.removeAllRanges(),s>r?(t.addRange(e),t.extend(o.node,o.offset)):(e.setEnd(o.node,o.offset),t.addRange(e)))}}for(e=[],t=n;t=t.parentNode;)t.nodeType===1&&e.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<e.length;n++)t=e[n],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var jx=Bt&&"documentMode"in document&&11>=document.documentMode,lr=null,bl=null,yi=null,wl=!1;function Yu(t,e,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;wl||lr==null||lr!==no(r)||(r=lr,"selectionStart"in r&&Pc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),yi&&Di(yi,r)||(yi=r,r=co(bl,"onSelect"),0<r.length&&(e=new Nc("onSelect","select",null,e,n),t.push({event:e,listeners:r}),e.target=lr)))}function gs(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var cr={animationend:gs("Animation","AnimationEnd"),animationiteration:gs("Animation","AnimationIteration"),animationstart:gs("Animation","AnimationStart"),transitionend:gs("Transition","TransitionEnd")},ka={},fm={};Bt&&(fm=document.createElement("div").style,"AnimationEvent"in window||(delete cr.animationend.animation,delete cr.animationiteration.animation,delete cr.animationstart.animation),"TransitionEvent"in window||delete cr.transitionend.transition);function Ko(t){if(ka[t])return ka[t];if(!cr[t])return t;var e=cr[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in fm)return ka[t]=e[n];return t}var mm=Ko("animationend"),pm=Ko("animationiteration"),gm=Ko("animationstart"),xm=Ko("transitionend"),vm=new Map,Xu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Sn(t,e){vm.set(t,e),Gn(e,[t])}for(var Na=0;Na<Xu.length;Na++){var ja=Xu[Na],Cx=ja.toLowerCase(),Px=ja[0].toUpperCase()+ja.slice(1);Sn(Cx,"on"+Px)}Sn(mm,"onAnimationEnd");Sn(pm,"onAnimationIteration");Sn(gm,"onAnimationStart");Sn("dblclick","onDoubleClick");Sn("focusin","onFocus");Sn("focusout","onBlur");Sn(xm,"onTransitionEnd");Cr("onMouseEnter",["mouseout","mouseover"]);Cr("onMouseLeave",["mouseout","mouseover"]);Cr("onPointerEnter",["pointerout","pointerover"]);Cr("onPointerLeave",["pointerout","pointerover"]);Gn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Gn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Gn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Gn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Gn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Gn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ci="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ex=new Set("cancel close invalid load scroll toggle".split(" ").concat(ci));function Ku(t,e,n){var r=t.type||"unknown-event";t.currentTarget=n,C0(r,e,void 0,t),t.currentTarget=null}function ym(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var r=t[n],i=r.event;r=r.listeners;e:{var s=void 0;if(e)for(var o=r.length-1;0<=o;o--){var a=r[o],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==s&&i.isPropagationStopped())break e;Ku(i,a,u),s=l}else for(o=0;o<r.length;o++){if(a=r[o],l=a.instance,u=a.currentTarget,a=a.listener,l!==s&&i.isPropagationStopped())break e;Ku(i,a,u),s=l}}}if(io)throw t=gl,io=!1,gl=null,t}function J(t,e){var n=e[jl];n===void 0&&(n=e[jl]=new Set);var r=t+"__bubble";n.has(r)||(bm(e,t,2,!1),n.add(r))}function Ca(t,e,n){var r=0;e&&(r|=4),bm(n,t,r,e)}var xs="_reactListening"+Math.random().toString(36).slice(2);function Oi(t){if(!t[xs]){t[xs]=!0,Cf.forEach(function(n){n!=="selectionchange"&&(Ex.has(n)||Ca(n,!1,t),Ca(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[xs]||(e[xs]=!0,Ca("selectionchange",!1,e))}}function bm(t,e,n,r){switch(rm(e)){case 1:var i=H0;break;case 4:i=W0;break;default:i=_c}n=i.bind(null,e,n,t),i=void 0,!pl||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(i=!0),r?i!==void 0?t.addEventListener(e,n,{capture:!0,passive:i}):t.addEventListener(e,n,!0):i!==void 0?t.addEventListener(e,n,{passive:i}):t.addEventListener(e,n,!1)}function Pa(t,e,n,r,i){var s=r;if(!(e&1)&&!(e&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;o=o.return}for(;a!==null;){if(o=On(a),o===null)return;if(l=o.tag,l===5||l===6){r=s=o;continue e}a=a.parentNode}}r=r.return}Hf(function(){var u=s,d=yc(n),h=[];e:{var f=vm.get(t);if(f!==void 0){var m=Nc,x=t;switch(t){case"keypress":if(Hs(n)===0)break e;case"keydown":case"keyup":m=sx;break;case"focusin":x="focus",m=wa;break;case"focusout":x="blur",m=wa;break;case"beforeblur":case"afterblur":m=wa;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=Lu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=X0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=lx;break;case mm:case pm:case gm:m=G0;break;case xm:m=ux;break;case"scroll":m=V0;break;case"wheel":m=hx;break;case"copy":case"cut":case"paste":m=Z0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=Fu}var v=(e&4)!==0,b=!v&&t==="scroll",g=v?f!==null?f+"Capture":null:f;v=[];for(var p=u,y;p!==null;){y=p;var w=y.stateNode;if(y.tag===5&&w!==null&&(y=w,g!==null&&(w=Ei(p,g),w!=null&&v.push(Li(p,w,y)))),b)break;p=p.return}0<v.length&&(f=new m(f,x,null,n,d),h.push({event:f,listeners:v}))}}if(!(e&7)){e:{if(f=t==="mouseover"||t==="pointerover",m=t==="mouseout"||t==="pointerout",f&&n!==fl&&(x=n.relatedTarget||n.fromElement)&&(On(x)||x[Ht]))break e;if((m||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,m?(x=n.relatedTarget||n.toElement,m=u,x=x?On(x):null,x!==null&&(b=qn(x),x!==b||x.tag!==5&&x.tag!==6)&&(x=null)):(m=null,x=u),m!==x)){if(v=Lu,w="onMouseLeave",g="onMouseEnter",p="mouse",(t==="pointerout"||t==="pointerover")&&(v=Fu,w="onPointerLeave",g="onPointerEnter",p="pointer"),b=m==null?f:ur(m),y=x==null?f:ur(x),f=new v(w,p+"leave",m,n,d),f.target=b,f.relatedTarget=y,w=null,On(d)===u&&(v=new v(g,p+"enter",x,n,d),v.target=y,v.relatedTarget=b,w=v),b=w,m&&x)t:{for(v=m,g=x,p=0,y=v;y;y=er(y))p++;for(y=0,w=g;w;w=er(w))y++;for(;0<p-y;)v=er(v),p--;for(;0<y-p;)g=er(g),y--;for(;p--;){if(v===g||g!==null&&v===g.alternate)break t;v=er(v),g=er(g)}v=null}else v=null;m!==null&&Qu(h,f,m,v,!1),x!==null&&b!==null&&Qu(h,b,x,v,!0)}}e:{if(f=u?ur(u):window,m=f.nodeName&&f.nodeName.toLowerCase(),m==="select"||m==="input"&&f.type==="file")var _=yx;else if(Uu(f))if(cm)_=_x;else{_=wx;var S=bx}else(m=f.nodeName)&&m.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(_=Sx);if(_&&(_=_(t,u))){lm(h,_,n,d);break e}S&&S(t,f,u),t==="focusout"&&(S=f._wrapperState)&&S.controlled&&f.type==="number"&&ll(f,"number",f.value)}switch(S=u?ur(u):window,t){case"focusin":(Uu(S)||S.contentEditable==="true")&&(lr=S,bl=u,yi=null);break;case"focusout":yi=bl=lr=null;break;case"mousedown":wl=!0;break;case"contextmenu":case"mouseup":case"dragend":wl=!1,Yu(h,n,d);break;case"selectionchange":if(jx)break;case"keydown":case"keyup":Yu(h,n,d)}var k;if(Cc)e:{switch(t){case"compositionstart":var j="onCompositionStart";break e;case"compositionend":j="onCompositionEnd";break e;case"compositionupdate":j="onCompositionUpdate";break e}j=void 0}else ar?om(t,n)&&(j="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(j="onCompositionStart");j&&(sm&&n.locale!=="ko"&&(ar||j!=="onCompositionStart"?j==="onCompositionEnd"&&ar&&(k=im()):(tn=d,kc="value"in tn?tn.value:tn.textContent,ar=!0)),S=co(u,j),0<S.length&&(j=new Au(j,t,null,n,d),h.push({event:j,listeners:S}),k?j.data=k:(k=am(n),k!==null&&(j.data=k)))),(k=mx?px(t,n):gx(t,n))&&(u=co(u,"onBeforeInput"),0<u.length&&(d=new Au("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:u}),d.data=k))}ym(h,e)})}function Li(t,e,n){return{instance:t,listener:e,currentTarget:n}}function co(t,e){for(var n=e+"Capture",r=[];t!==null;){var i=t,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Ei(t,n),s!=null&&r.unshift(Li(t,s,i)),s=Ei(t,e),s!=null&&r.push(Li(t,s,i))),t=t.return}return r}function er(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function Qu(t,e,n,r,i){for(var s=e._reactName,o=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,i?(l=Ei(n,s),l!=null&&o.unshift(Li(n,l,a))):i||(l=Ei(n,s),l!=null&&o.push(Li(n,l,a)))),n=n.return}o.length!==0&&t.push({event:e,listeners:o})}var Mx=/\r\n?/g,$x=/\u0000|\uFFFD/g;function Gu(t){return(typeof t=="string"?t:""+t).replace(Mx,`
`).replace($x,"")}function vs(t,e,n){if(e=Gu(e),Gu(t)!==e&&n)throw Error(P(425))}function uo(){}var Sl=null,_l=null;function kl(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Nl=typeof setTimeout=="function"?setTimeout:void 0,Tx=typeof clearTimeout=="function"?clearTimeout:void 0,qu=typeof Promise=="function"?Promise:void 0,Rx=typeof queueMicrotask=="function"?queueMicrotask:typeof qu<"u"?function(t){return qu.resolve(null).then(t).catch(Dx)}:Nl;function Dx(t){setTimeout(function(){throw t})}function Ea(t,e){var n=e,r=0;do{var i=n.nextSibling;if(t.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){t.removeChild(i),Ti(e);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Ti(e)}function un(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?")break;if(e==="/$")return null}}return t}function Zu(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}var Ur=Math.random().toString(36).slice(2),Nt="__reactFiber$"+Ur,Ai="__reactProps$"+Ur,Ht="__reactContainer$"+Ur,jl="__reactEvents$"+Ur,Ox="__reactListeners$"+Ur,Lx="__reactHandles$"+Ur;function On(t){var e=t[Nt];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Ht]||n[Nt]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Zu(t);t!==null;){if(n=t[Nt])return n;t=Zu(t)}return e}t=n,n=t.parentNode}return null}function ts(t){return t=t[Nt]||t[Ht],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function ur(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(P(33))}function Qo(t){return t[Ai]||null}var Cl=[],dr=-1;function _n(t){return{current:t}}function te(t){0>dr||(t.current=Cl[dr],Cl[dr]=null,dr--)}function Z(t,e){dr++,Cl[dr]=t.current,t.current=e}var bn={},Ee=_n(bn),Be=_n(!1),Bn=bn;function Pr(t,e){var n=t.type.contextTypes;if(!n)return bn;var r=t.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===e)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=e[s];return r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=e,t.__reactInternalMemoizedMaskedChildContext=i),i}function He(t){return t=t.childContextTypes,t!=null}function ho(){te(Be),te(Ee)}function Ju(t,e,n){if(Ee.current!==bn)throw Error(P(168));Z(Ee,e),Z(Be,n)}function wm(t,e,n){var r=t.stateNode;if(e=e.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in e))throw Error(P(108,b0(t)||"Unknown",i));return oe({},n,r)}function fo(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||bn,Bn=Ee.current,Z(Ee,t),Z(Be,Be.current),!0}function ed(t,e,n){var r=t.stateNode;if(!r)throw Error(P(169));n?(t=wm(t,e,Bn),r.__reactInternalMemoizedMergedChildContext=t,te(Be),te(Ee),Z(Ee,t)):te(Be),Z(Be,n)}var Lt=null,Go=!1,Ma=!1;function Sm(t){Lt===null?Lt=[t]:Lt.push(t)}function Ax(t){Go=!0,Sm(t)}function kn(){if(!Ma&&Lt!==null){Ma=!0;var t=0,e=K;try{var n=Lt;for(K=1;t<n.length;t++){var r=n[t];do r=r(!0);while(r!==null)}Lt=null,Go=!1}catch(i){throw Lt!==null&&(Lt=Lt.slice(t+1)),Xf(bc,kn),i}finally{K=e,Ma=!1}}return null}var hr=[],fr=0,mo=null,po=0,tt=[],nt=0,Hn=null,Ft=1,zt="";function Mn(t,e){hr[fr++]=po,hr[fr++]=mo,mo=t,po=e}function _m(t,e,n){tt[nt++]=Ft,tt[nt++]=zt,tt[nt++]=Hn,Hn=t;var r=Ft;t=zt;var i=32-gt(r)-1;r&=~(1<<i),n+=1;var s=32-gt(e)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,Ft=1<<32-gt(e)+i|n<<i|r,zt=s+t}else Ft=1<<s|n<<i|r,zt=t}function Ec(t){t.return!==null&&(Mn(t,1),_m(t,1,0))}function Mc(t){for(;t===mo;)mo=hr[--fr],hr[fr]=null,po=hr[--fr],hr[fr]=null;for(;t===Hn;)Hn=tt[--nt],tt[nt]=null,zt=tt[--nt],tt[nt]=null,Ft=tt[--nt],tt[nt]=null}var Ge=null,Qe=null,re=!1,ft=null;function km(t,e){var n=rt(5,null,null,0);n.elementType="DELETED",n.stateNode=e,n.return=t,e=t.deletions,e===null?(t.deletions=[n],t.flags|=16):e.push(n)}function td(t,e){switch(t.tag){case 5:var n=t.type;return e=e.nodeType!==1||n.toLowerCase()!==e.nodeName.toLowerCase()?null:e,e!==null?(t.stateNode=e,Ge=t,Qe=un(e.firstChild),!0):!1;case 6:return e=t.pendingProps===""||e.nodeType!==3?null:e,e!==null?(t.stateNode=e,Ge=t,Qe=null,!0):!1;case 13:return e=e.nodeType!==8?null:e,e!==null?(n=Hn!==null?{id:Ft,overflow:zt}:null,t.memoizedState={dehydrated:e,treeContext:n,retryLane:1073741824},n=rt(18,null,null,0),n.stateNode=e,n.return=t,t.child=n,Ge=t,Qe=null,!0):!1;default:return!1}}function Pl(t){return(t.mode&1)!==0&&(t.flags&128)===0}function El(t){if(re){var e=Qe;if(e){var n=e;if(!td(t,e)){if(Pl(t))throw Error(P(418));e=un(n.nextSibling);var r=Ge;e&&td(t,e)?km(r,n):(t.flags=t.flags&-4097|2,re=!1,Ge=t)}}else{if(Pl(t))throw Error(P(418));t.flags=t.flags&-4097|2,re=!1,Ge=t}}}function nd(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;Ge=t}function ys(t){if(t!==Ge)return!1;if(!re)return nd(t),re=!0,!1;var e;if((e=t.tag!==3)&&!(e=t.tag!==5)&&(e=t.type,e=e!=="head"&&e!=="body"&&!kl(t.type,t.memoizedProps)),e&&(e=Qe)){if(Pl(t))throw Nm(),Error(P(418));for(;e;)km(t,e),e=un(e.nextSibling)}if(nd(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(P(317));e:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="/$"){if(e===0){Qe=un(t.nextSibling);break e}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++}t=t.nextSibling}Qe=null}}else Qe=Ge?un(t.stateNode.nextSibling):null;return!0}function Nm(){for(var t=Qe;t;)t=un(t.nextSibling)}function Er(){Qe=Ge=null,re=!1}function $c(t){ft===null?ft=[t]:ft.push(t)}var Fx=Xt.ReactCurrentBatchConfig;function Gr(t,e,n){if(t=n.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(P(309));var r=n.stateNode}if(!r)throw Error(P(147,t));var i=r,s=""+t;return e!==null&&e.ref!==null&&typeof e.ref=="function"&&e.ref._stringRef===s?e.ref:(e=function(o){var a=i.refs;o===null?delete a[s]:a[s]=o},e._stringRef=s,e)}if(typeof t!="string")throw Error(P(284));if(!n._owner)throw Error(P(290,t))}return t}function bs(t,e){throw t=Object.prototype.toString.call(e),Error(P(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t))}function rd(t){var e=t._init;return e(t._payload)}function jm(t){function e(g,p){if(t){var y=g.deletions;y===null?(g.deletions=[p],g.flags|=16):y.push(p)}}function n(g,p){if(!t)return null;for(;p!==null;)e(g,p),p=p.sibling;return null}function r(g,p){for(g=new Map;p!==null;)p.key!==null?g.set(p.key,p):g.set(p.index,p),p=p.sibling;return g}function i(g,p){return g=mn(g,p),g.index=0,g.sibling=null,g}function s(g,p,y){return g.index=y,t?(y=g.alternate,y!==null?(y=y.index,y<p?(g.flags|=2,p):y):(g.flags|=2,p)):(g.flags|=1048576,p)}function o(g){return t&&g.alternate===null&&(g.flags|=2),g}function a(g,p,y,w){return p===null||p.tag!==6?(p=Aa(y,g.mode,w),p.return=g,p):(p=i(p,y),p.return=g,p)}function l(g,p,y,w){var _=y.type;return _===or?d(g,p,y.props.children,w,y.key):p!==null&&(p.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===qt&&rd(_)===p.type)?(w=i(p,y.props),w.ref=Gr(g,p,y),w.return=g,w):(w=Gs(y.type,y.key,y.props,null,g.mode,w),w.ref=Gr(g,p,y),w.return=g,w)}function u(g,p,y,w){return p===null||p.tag!==4||p.stateNode.containerInfo!==y.containerInfo||p.stateNode.implementation!==y.implementation?(p=Fa(y,g.mode,w),p.return=g,p):(p=i(p,y.children||[]),p.return=g,p)}function d(g,p,y,w,_){return p===null||p.tag!==7?(p=In(y,g.mode,w,_),p.return=g,p):(p=i(p,y),p.return=g,p)}function h(g,p,y){if(typeof p=="string"&&p!==""||typeof p=="number")return p=Aa(""+p,g.mode,y),p.return=g,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case cs:return y=Gs(p.type,p.key,p.props,null,g.mode,y),y.ref=Gr(g,null,p),y.return=g,y;case sr:return p=Fa(p,g.mode,y),p.return=g,p;case qt:var w=p._init;return h(g,w(p._payload),y)}if(ai(p)||Vr(p))return p=In(p,g.mode,y,null),p.return=g,p;bs(g,p)}return null}function f(g,p,y,w){var _=p!==null?p.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return _!==null?null:a(g,p,""+y,w);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case cs:return y.key===_?l(g,p,y,w):null;case sr:return y.key===_?u(g,p,y,w):null;case qt:return _=y._init,f(g,p,_(y._payload),w)}if(ai(y)||Vr(y))return _!==null?null:d(g,p,y,w,null);bs(g,y)}return null}function m(g,p,y,w,_){if(typeof w=="string"&&w!==""||typeof w=="number")return g=g.get(y)||null,a(p,g,""+w,_);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case cs:return g=g.get(w.key===null?y:w.key)||null,l(p,g,w,_);case sr:return g=g.get(w.key===null?y:w.key)||null,u(p,g,w,_);case qt:var S=w._init;return m(g,p,y,S(w._payload),_)}if(ai(w)||Vr(w))return g=g.get(y)||null,d(p,g,w,_,null);bs(p,w)}return null}function x(g,p,y,w){for(var _=null,S=null,k=p,j=p=0,E=null;k!==null&&j<y.length;j++){k.index>j?(E=k,k=null):E=k.sibling;var C=f(g,k,y[j],w);if(C===null){k===null&&(k=E);break}t&&k&&C.alternate===null&&e(g,k),p=s(C,p,j),S===null?_=C:S.sibling=C,S=C,k=E}if(j===y.length)return n(g,k),re&&Mn(g,j),_;if(k===null){for(;j<y.length;j++)k=h(g,y[j],w),k!==null&&(p=s(k,p,j),S===null?_=k:S.sibling=k,S=k);return re&&Mn(g,j),_}for(k=r(g,k);j<y.length;j++)E=m(k,g,j,y[j],w),E!==null&&(t&&E.alternate!==null&&k.delete(E.key===null?j:E.key),p=s(E,p,j),S===null?_=E:S.sibling=E,S=E);return t&&k.forEach(function($){return e(g,$)}),re&&Mn(g,j),_}function v(g,p,y,w){var _=Vr(y);if(typeof _!="function")throw Error(P(150));if(y=_.call(y),y==null)throw Error(P(151));for(var S=_=null,k=p,j=p=0,E=null,C=y.next();k!==null&&!C.done;j++,C=y.next()){k.index>j?(E=k,k=null):E=k.sibling;var $=f(g,k,C.value,w);if($===null){k===null&&(k=E);break}t&&k&&$.alternate===null&&e(g,k),p=s($,p,j),S===null?_=$:S.sibling=$,S=$,k=E}if(C.done)return n(g,k),re&&Mn(g,j),_;if(k===null){for(;!C.done;j++,C=y.next())C=h(g,C.value,w),C!==null&&(p=s(C,p,j),S===null?_=C:S.sibling=C,S=C);return re&&Mn(g,j),_}for(k=r(g,k);!C.done;j++,C=y.next())C=m(k,g,j,C.value,w),C!==null&&(t&&C.alternate!==null&&k.delete(C.key===null?j:C.key),p=s(C,p,j),S===null?_=C:S.sibling=C,S=C);return t&&k.forEach(function(D){return e(g,D)}),re&&Mn(g,j),_}function b(g,p,y,w){if(typeof y=="object"&&y!==null&&y.type===or&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case cs:e:{for(var _=y.key,S=p;S!==null;){if(S.key===_){if(_=y.type,_===or){if(S.tag===7){n(g,S.sibling),p=i(S,y.props.children),p.return=g,g=p;break e}}else if(S.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===qt&&rd(_)===S.type){n(g,S.sibling),p=i(S,y.props),p.ref=Gr(g,S,y),p.return=g,g=p;break e}n(g,S);break}else e(g,S);S=S.sibling}y.type===or?(p=In(y.props.children,g.mode,w,y.key),p.return=g,g=p):(w=Gs(y.type,y.key,y.props,null,g.mode,w),w.ref=Gr(g,p,y),w.return=g,g=w)}return o(g);case sr:e:{for(S=y.key;p!==null;){if(p.key===S)if(p.tag===4&&p.stateNode.containerInfo===y.containerInfo&&p.stateNode.implementation===y.implementation){n(g,p.sibling),p=i(p,y.children||[]),p.return=g,g=p;break e}else{n(g,p);break}else e(g,p);p=p.sibling}p=Fa(y,g.mode,w),p.return=g,g=p}return o(g);case qt:return S=y._init,b(g,p,S(y._payload),w)}if(ai(y))return x(g,p,y,w);if(Vr(y))return v(g,p,y,w);bs(g,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,p!==null&&p.tag===6?(n(g,p.sibling),p=i(p,y),p.return=g,g=p):(n(g,p),p=Aa(y,g.mode,w),p.return=g,g=p),o(g)):n(g,p)}return b}var Mr=jm(!0),Cm=jm(!1),go=_n(null),xo=null,mr=null,Tc=null;function Rc(){Tc=mr=xo=null}function Dc(t){var e=go.current;te(go),t._currentValue=e}function Ml(t,e,n){for(;t!==null;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,r!==null&&(r.childLanes|=e)):r!==null&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===n)break;t=t.return}}function wr(t,e){xo=t,Tc=mr=null,t=t.dependencies,t!==null&&t.firstContext!==null&&(t.lanes&e&&(Ie=!0),t.firstContext=null)}function ot(t){var e=t._currentValue;if(Tc!==t)if(t={context:t,memoizedValue:e,next:null},mr===null){if(xo===null)throw Error(P(308));mr=t,xo.dependencies={lanes:0,firstContext:t}}else mr=mr.next=t;return e}var Ln=null;function Oc(t){Ln===null?Ln=[t]:Ln.push(t)}function Pm(t,e,n,r){var i=e.interleaved;return i===null?(n.next=n,Oc(e)):(n.next=i.next,i.next=n),e.interleaved=n,Wt(t,r)}function Wt(t,e){t.lanes|=e;var n=t.alternate;for(n!==null&&(n.lanes|=e),n=t,t=t.return;t!==null;)t.childLanes|=e,n=t.alternate,n!==null&&(n.childLanes|=e),n=t,t=t.return;return n.tag===3?n.stateNode:null}var Zt=!1;function Lc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Em(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function Ut(t,e){return{eventTime:t,lane:e,tag:0,payload:null,callback:null,next:null}}function dn(t,e,n){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,H&2){var i=r.pending;return i===null?e.next=e:(e.next=i.next,i.next=e),r.pending=e,Wt(t,n)}return i=r.interleaved,i===null?(e.next=e,Oc(r)):(e.next=i.next,i.next=e),r.interleaved=e,Wt(t,n)}function Ws(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194240)!==0)){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,wc(t,n)}}function id(t,e){var n=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=e:s=s.next=e}else i=s=e;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}function vo(t,e,n,r){var i=t.updateQueue;Zt=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,u=l.next;l.next=null,o===null?s=u:o.next=u,o=l;var d=t.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==o&&(a===null?d.firstBaseUpdate=u:a.next=u,d.lastBaseUpdate=l))}if(s!==null){var h=i.baseState;o=0,d=u=l=null,a=s;do{var f=a.lane,m=a.eventTime;if((r&f)===f){d!==null&&(d=d.next={eventTime:m,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var x=t,v=a;switch(f=e,m=n,v.tag){case 1:if(x=v.payload,typeof x=="function"){h=x.call(m,h,f);break e}h=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=v.payload,f=typeof x=="function"?x.call(m,h,f):x,f==null)break e;h=oe({},h,f);break e;case 2:Zt=!0}}a.callback!==null&&a.lane!==0&&(t.flags|=64,f=i.effects,f===null?i.effects=[a]:f.push(a))}else m={eventTime:m,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(u=d=m,l=h):d=d.next=m,o|=f;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;f=a,a=f.next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}while(!0);if(d===null&&(l=h),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=d,e=i.shared.interleaved,e!==null){i=e;do o|=i.lane,i=i.next;while(i!==e)}else s===null&&(i.shared.lanes=0);Vn|=o,t.lanes=o,t.memoizedState=h}}function sd(t,e,n){if(t=e.effects,e.effects=null,t!==null)for(e=0;e<t.length;e++){var r=t[e],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(P(191,i));i.call(r)}}}var ns={},Pt=_n(ns),Fi=_n(ns),zi=_n(ns);function An(t){if(t===ns)throw Error(P(174));return t}function Ac(t,e){switch(Z(zi,e),Z(Fi,t),Z(Pt,ns),t=e.nodeType,t){case 9:case 11:e=(e=e.documentElement)?e.namespaceURI:ul(null,"");break;default:t=t===8?e.parentNode:e,e=t.namespaceURI||null,t=t.tagName,e=ul(e,t)}te(Pt),Z(Pt,e)}function $r(){te(Pt),te(Fi),te(zi)}function Mm(t){An(zi.current);var e=An(Pt.current),n=ul(e,t.type);e!==n&&(Z(Fi,t),Z(Pt,n))}function Fc(t){Fi.current===t&&(te(Pt),te(Fi))}var ie=_n(0);function yo(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var $a=[];function zc(){for(var t=0;t<$a.length;t++)$a[t]._workInProgressVersionPrimary=null;$a.length=0}var Vs=Xt.ReactCurrentDispatcher,Ta=Xt.ReactCurrentBatchConfig,Wn=0,se=null,pe=null,ye=null,bo=!1,bi=!1,Ii=0,zx=0;function ke(){throw Error(P(321))}function Ic(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!vt(t[n],e[n]))return!1;return!0}function Uc(t,e,n,r,i,s){if(Wn=s,se=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,Vs.current=t===null||t.memoizedState===null?Hx:Wx,t=n(r,i),bi){s=0;do{if(bi=!1,Ii=0,25<=s)throw Error(P(301));s+=1,ye=pe=null,e.updateQueue=null,Vs.current=Vx,t=n(r,i)}while(bi)}if(Vs.current=wo,e=pe!==null&&pe.next!==null,Wn=0,ye=pe=se=null,bo=!1,e)throw Error(P(300));return t}function Bc(){var t=Ii!==0;return Ii=0,t}function _t(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ye===null?se.memoizedState=ye=t:ye=ye.next=t,ye}function at(){if(pe===null){var t=se.alternate;t=t!==null?t.memoizedState:null}else t=pe.next;var e=ye===null?se.memoizedState:ye.next;if(e!==null)ye=e,pe=t;else{if(t===null)throw Error(P(310));pe=t,t={memoizedState:pe.memoizedState,baseState:pe.baseState,baseQueue:pe.baseQueue,queue:pe.queue,next:null},ye===null?se.memoizedState=ye=t:ye=ye.next=t}return ye}function Ui(t,e){return typeof e=="function"?e(t):e}function Ra(t){var e=at(),n=e.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=t;var r=pe,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var a=o=null,l=null,u=s;do{var d=u.lane;if((Wn&d)===d)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:t(r,u.action);else{var h={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=h,o=r):l=l.next=h,se.lanes|=d,Vn|=d}u=u.next}while(u!==null&&u!==s);l===null?o=r:l.next=a,vt(r,e.memoizedState)||(Ie=!0),e.memoizedState=r,e.baseState=o,e.baseQueue=l,n.lastRenderedState=r}if(t=n.interleaved,t!==null){i=t;do s=i.lane,se.lanes|=s,Vn|=s,i=i.next;while(i!==t)}else i===null&&(n.lanes=0);return[e.memoizedState,n.dispatch]}function Da(t){var e=at(),n=e.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=t;var r=n.dispatch,i=n.pending,s=e.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=t(s,o.action),o=o.next;while(o!==i);vt(s,e.memoizedState)||(Ie=!0),e.memoizedState=s,e.baseQueue===null&&(e.baseState=s),n.lastRenderedState=s}return[s,r]}function $m(){}function Tm(t,e){var n=se,r=at(),i=e(),s=!vt(r.memoizedState,i);if(s&&(r.memoizedState=i,Ie=!0),r=r.queue,Hc(Om.bind(null,n,r,t),[t]),r.getSnapshot!==e||s||ye!==null&&ye.memoizedState.tag&1){if(n.flags|=2048,Bi(9,Dm.bind(null,n,r,i,e),void 0,null),be===null)throw Error(P(349));Wn&30||Rm(n,e,i)}return i}function Rm(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=se.updateQueue,e===null?(e={lastEffect:null,stores:null},se.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Dm(t,e,n,r){e.value=n,e.getSnapshot=r,Lm(e)&&Am(t)}function Om(t,e,n){return n(function(){Lm(e)&&Am(t)})}function Lm(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!vt(t,n)}catch{return!0}}function Am(t){var e=Wt(t,1);e!==null&&xt(e,t,1,-1)}function od(t){var e=_t();return typeof t=="function"&&(t=t()),e.memoizedState=e.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ui,lastRenderedState:t},e.queue=t,t=t.dispatch=Bx.bind(null,se,t),[e.memoizedState,t]}function Bi(t,e,n,r){return t={tag:t,create:e,destroy:n,deps:r,next:null},e=se.updateQueue,e===null?(e={lastEffect:null,stores:null},se.updateQueue=e,e.lastEffect=t.next=t):(n=e.lastEffect,n===null?e.lastEffect=t.next=t:(r=n.next,n.next=t,t.next=r,e.lastEffect=t)),t}function Fm(){return at().memoizedState}function Ys(t,e,n,r){var i=_t();se.flags|=t,i.memoizedState=Bi(1|e,n,void 0,r===void 0?null:r)}function qo(t,e,n,r){var i=at();r=r===void 0?null:r;var s=void 0;if(pe!==null){var o=pe.memoizedState;if(s=o.destroy,r!==null&&Ic(r,o.deps)){i.memoizedState=Bi(e,n,s,r);return}}se.flags|=t,i.memoizedState=Bi(1|e,n,s,r)}function ad(t,e){return Ys(8390656,8,t,e)}function Hc(t,e){return qo(2048,8,t,e)}function zm(t,e){return qo(4,2,t,e)}function Im(t,e){return qo(4,4,t,e)}function Um(t,e){if(typeof e=="function")return t=t(),e(t),function(){e(null)};if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Bm(t,e,n){return n=n!=null?n.concat([t]):null,qo(4,4,Um.bind(null,e,t),n)}function Wc(){}function Hm(t,e){var n=at();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&Ic(e,r[1])?r[0]:(n.memoizedState=[t,e],t)}function Wm(t,e){var n=at();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&Ic(e,r[1])?r[0]:(t=t(),n.memoizedState=[t,e],t)}function Vm(t,e,n){return Wn&21?(vt(n,e)||(n=Gf(),se.lanes|=n,Vn|=n,t.baseState=!0),e):(t.baseState&&(t.baseState=!1,Ie=!0),t.memoizedState=n)}function Ix(t,e){var n=K;K=n!==0&&4>n?n:4,t(!0);var r=Ta.transition;Ta.transition={};try{t(!1),e()}finally{K=n,Ta.transition=r}}function Ym(){return at().memoizedState}function Ux(t,e,n){var r=fn(t);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Xm(t))Km(e,n);else if(n=Pm(t,e,n,r),n!==null){var i=Re();xt(n,t,r,i),Qm(n,e,r)}}function Bx(t,e,n){var r=fn(t),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Xm(t))Km(e,i);else{var s=t.alternate;if(t.lanes===0&&(s===null||s.lanes===0)&&(s=e.lastRenderedReducer,s!==null))try{var o=e.lastRenderedState,a=s(o,n);if(i.hasEagerState=!0,i.eagerState=a,vt(a,o)){var l=e.interleaved;l===null?(i.next=i,Oc(e)):(i.next=l.next,l.next=i),e.interleaved=i;return}}catch{}finally{}n=Pm(t,e,i,r),n!==null&&(i=Re(),xt(n,t,r,i),Qm(n,e,r))}}function Xm(t){var e=t.alternate;return t===se||e!==null&&e===se}function Km(t,e){bi=bo=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Qm(t,e,n){if(n&4194240){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,wc(t,n)}}var wo={readContext:ot,useCallback:ke,useContext:ke,useEffect:ke,useImperativeHandle:ke,useInsertionEffect:ke,useLayoutEffect:ke,useMemo:ke,useReducer:ke,useRef:ke,useState:ke,useDebugValue:ke,useDeferredValue:ke,useTransition:ke,useMutableSource:ke,useSyncExternalStore:ke,useId:ke,unstable_isNewReconciler:!1},Hx={readContext:ot,useCallback:function(t,e){return _t().memoizedState=[t,e===void 0?null:e],t},useContext:ot,useEffect:ad,useImperativeHandle:function(t,e,n){return n=n!=null?n.concat([t]):null,Ys(4194308,4,Um.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Ys(4194308,4,t,e)},useInsertionEffect:function(t,e){return Ys(4,2,t,e)},useMemo:function(t,e){var n=_t();return e=e===void 0?null:e,t=t(),n.memoizedState=[t,e],t},useReducer:function(t,e,n){var r=_t();return e=n!==void 0?n(e):e,r.memoizedState=r.baseState=e,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:e},r.queue=t,t=t.dispatch=Ux.bind(null,se,t),[r.memoizedState,t]},useRef:function(t){var e=_t();return t={current:t},e.memoizedState=t},useState:od,useDebugValue:Wc,useDeferredValue:function(t){return _t().memoizedState=t},useTransition:function(){var t=od(!1),e=t[0];return t=Ix.bind(null,t[1]),_t().memoizedState=t,[e,t]},useMutableSource:function(){},useSyncExternalStore:function(t,e,n){var r=se,i=_t();if(re){if(n===void 0)throw Error(P(407));n=n()}else{if(n=e(),be===null)throw Error(P(349));Wn&30||Rm(r,e,n)}i.memoizedState=n;var s={value:n,getSnapshot:e};return i.queue=s,ad(Om.bind(null,r,s,t),[t]),r.flags|=2048,Bi(9,Dm.bind(null,r,s,n,e),void 0,null),n},useId:function(){var t=_t(),e=be.identifierPrefix;if(re){var n=zt,r=Ft;n=(r&~(1<<32-gt(r)-1)).toString(32)+n,e=":"+e+"R"+n,n=Ii++,0<n&&(e+="H"+n.toString(32)),e+=":"}else n=zx++,e=":"+e+"r"+n.toString(32)+":";return t.memoizedState=e},unstable_isNewReconciler:!1},Wx={readContext:ot,useCallback:Hm,useContext:ot,useEffect:Hc,useImperativeHandle:Bm,useInsertionEffect:zm,useLayoutEffect:Im,useMemo:Wm,useReducer:Ra,useRef:Fm,useState:function(){return Ra(Ui)},useDebugValue:Wc,useDeferredValue:function(t){var e=at();return Vm(e,pe.memoizedState,t)},useTransition:function(){var t=Ra(Ui)[0],e=at().memoizedState;return[t,e]},useMutableSource:$m,useSyncExternalStore:Tm,useId:Ym,unstable_isNewReconciler:!1},Vx={readContext:ot,useCallback:Hm,useContext:ot,useEffect:Hc,useImperativeHandle:Bm,useInsertionEffect:zm,useLayoutEffect:Im,useMemo:Wm,useReducer:Da,useRef:Fm,useState:function(){return Da(Ui)},useDebugValue:Wc,useDeferredValue:function(t){var e=at();return pe===null?e.memoizedState=t:Vm(e,pe.memoizedState,t)},useTransition:function(){var t=Da(Ui)[0],e=at().memoizedState;return[t,e]},useMutableSource:$m,useSyncExternalStore:Tm,useId:Ym,unstable_isNewReconciler:!1};function dt(t,e){if(t&&t.defaultProps){e=oe({},e),t=t.defaultProps;for(var n in t)e[n]===void 0&&(e[n]=t[n]);return e}return e}function $l(t,e,n,r){e=t.memoizedState,n=n(r,e),n=n==null?e:oe({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Zo={isMounted:function(t){return(t=t._reactInternals)?qn(t)===t:!1},enqueueSetState:function(t,e,n){t=t._reactInternals;var r=Re(),i=fn(t),s=Ut(r,i);s.payload=e,n!=null&&(s.callback=n),e=dn(t,s,i),e!==null&&(xt(e,t,i,r),Ws(e,t,i))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var r=Re(),i=fn(t),s=Ut(r,i);s.tag=1,s.payload=e,n!=null&&(s.callback=n),e=dn(t,s,i),e!==null&&(xt(e,t,i,r),Ws(e,t,i))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=Re(),r=fn(t),i=Ut(n,r);i.tag=2,e!=null&&(i.callback=e),e=dn(t,i,r),e!==null&&(xt(e,t,r,n),Ws(e,t,r))}};function ld(t,e,n,r,i,s,o){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,s,o):e.prototype&&e.prototype.isPureReactComponent?!Di(n,r)||!Di(i,s):!0}function Gm(t,e,n){var r=!1,i=bn,s=e.contextType;return typeof s=="object"&&s!==null?s=ot(s):(i=He(e)?Bn:Ee.current,r=e.contextTypes,s=(r=r!=null)?Pr(t,i):bn),e=new e(n,s),t.memoizedState=e.state!==null&&e.state!==void 0?e.state:null,e.updater=Zo,t.stateNode=e,e._reactInternals=t,r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=i,t.__reactInternalMemoizedMaskedChildContext=s),e}function cd(t,e,n,r){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,r),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,r),e.state!==t&&Zo.enqueueReplaceState(e,e.state,null)}function Tl(t,e,n,r){var i=t.stateNode;i.props=n,i.state=t.memoizedState,i.refs={},Lc(t);var s=e.contextType;typeof s=="object"&&s!==null?i.context=ot(s):(s=He(e)?Bn:Ee.current,i.context=Pr(t,s)),i.state=t.memoizedState,s=e.getDerivedStateFromProps,typeof s=="function"&&($l(t,e,s,n),i.state=t.memoizedState),typeof e.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(e=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),e!==i.state&&Zo.enqueueReplaceState(i,i.state,null),vo(t,n,i,r),i.state=t.memoizedState),typeof i.componentDidMount=="function"&&(t.flags|=4194308)}function Tr(t,e){try{var n="",r=e;do n+=y0(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:t,source:e,stack:i,digest:null}}function Oa(t,e,n){return{value:t,source:null,stack:n??null,digest:e??null}}function Rl(t,e){try{console.error(e.value)}catch(n){setTimeout(function(){throw n})}}var Yx=typeof WeakMap=="function"?WeakMap:Map;function qm(t,e,n){n=Ut(-1,n),n.tag=3,n.payload={element:null};var r=e.value;return n.callback=function(){_o||(_o=!0,Hl=r),Rl(t,e)},n}function Zm(t,e,n){n=Ut(-1,n),n.tag=3;var r=t.type.getDerivedStateFromError;if(typeof r=="function"){var i=e.value;n.payload=function(){return r(i)},n.callback=function(){Rl(t,e)}}var s=t.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Rl(t,e),typeof r!="function"&&(hn===null?hn=new Set([this]):hn.add(this));var o=e.stack;this.componentDidCatch(e.value,{componentStack:o!==null?o:""})}),n}function ud(t,e,n){var r=t.pingCache;if(r===null){r=t.pingCache=new Yx;var i=new Set;r.set(e,i)}else i=r.get(e),i===void 0&&(i=new Set,r.set(e,i));i.has(n)||(i.add(n),t=ov.bind(null,t,e,n),e.then(t,t))}function dd(t){do{var e;if((e=t.tag===13)&&(e=t.memoizedState,e=e!==null?e.dehydrated!==null:!0),e)return t;t=t.return}while(t!==null);return null}function hd(t,e,n,r,i){return t.mode&1?(t.flags|=65536,t.lanes=i,t):(t===e?t.flags|=65536:(t.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(e=Ut(-1,1),e.tag=2,dn(n,e,1))),n.lanes|=1),t)}var Xx=Xt.ReactCurrentOwner,Ie=!1;function Te(t,e,n,r){e.child=t===null?Cm(e,null,n,r):Mr(e,t.child,n,r)}function fd(t,e,n,r,i){n=n.render;var s=e.ref;return wr(e,i),r=Uc(t,e,n,r,s,i),n=Bc(),t!==null&&!Ie?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i,Vt(t,e,i)):(re&&n&&Ec(e),e.flags|=1,Te(t,e,r,i),e.child)}function md(t,e,n,r,i){if(t===null){var s=n.type;return typeof s=="function"&&!Zc(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(e.tag=15,e.type=s,Jm(t,e,s,r,i)):(t=Gs(n.type,null,r,e,e.mode,i),t.ref=e.ref,t.return=e,e.child=t)}if(s=t.child,!(t.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:Di,n(o,r)&&t.ref===e.ref)return Vt(t,e,i)}return e.flags|=1,t=mn(s,r),t.ref=e.ref,t.return=e,e.child=t}function Jm(t,e,n,r,i){if(t!==null){var s=t.memoizedProps;if(Di(s,r)&&t.ref===e.ref)if(Ie=!1,e.pendingProps=r=s,(t.lanes&i)!==0)t.flags&131072&&(Ie=!0);else return e.lanes=t.lanes,Vt(t,e,i)}return Dl(t,e,n,r,i)}function ep(t,e,n){var r=e.pendingProps,i=r.children,s=t!==null?t.memoizedState:null;if(r.mode==="hidden")if(!(e.mode&1))e.memoizedState={baseLanes:0,cachePool:null,transitions:null},Z(gr,Ke),Ke|=n;else{if(!(n&1073741824))return t=s!==null?s.baseLanes|n:n,e.lanes=e.childLanes=1073741824,e.memoizedState={baseLanes:t,cachePool:null,transitions:null},e.updateQueue=null,Z(gr,Ke),Ke|=t,null;e.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,Z(gr,Ke),Ke|=r}else s!==null?(r=s.baseLanes|n,e.memoizedState=null):r=n,Z(gr,Ke),Ke|=r;return Te(t,e,i,n),e.child}function tp(t,e){var n=e.ref;(t===null&&n!==null||t!==null&&t.ref!==n)&&(e.flags|=512,e.flags|=2097152)}function Dl(t,e,n,r,i){var s=He(n)?Bn:Ee.current;return s=Pr(e,s),wr(e,i),n=Uc(t,e,n,r,s,i),r=Bc(),t!==null&&!Ie?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i,Vt(t,e,i)):(re&&r&&Ec(e),e.flags|=1,Te(t,e,n,i),e.child)}function pd(t,e,n,r,i){if(He(n)){var s=!0;fo(e)}else s=!1;if(wr(e,i),e.stateNode===null)Xs(t,e),Gm(e,n,r),Tl(e,n,r,i),r=!0;else if(t===null){var o=e.stateNode,a=e.memoizedProps;o.props=a;var l=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=ot(u):(u=He(n)?Bn:Ee.current,u=Pr(e,u));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";h||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||l!==u)&&cd(e,o,r,u),Zt=!1;var f=e.memoizedState;o.state=f,vo(e,r,o,i),l=e.memoizedState,a!==r||f!==l||Be.current||Zt?(typeof d=="function"&&($l(e,n,d,r),l=e.memoizedState),(a=Zt||ld(e,n,a,r,f,l,u))?(h||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(e.flags|=4194308)):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=l),o.props=r,o.state=l,o.context=u,r=a):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),r=!1)}else{o=e.stateNode,Em(t,e),a=e.memoizedProps,u=e.type===e.elementType?a:dt(e.type,a),o.props=u,h=e.pendingProps,f=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=ot(l):(l=He(n)?Bn:Ee.current,l=Pr(e,l));var m=n.getDerivedStateFromProps;(d=typeof m=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==h||f!==l)&&cd(e,o,r,l),Zt=!1,f=e.memoizedState,o.state=f,vo(e,r,o,i);var x=e.memoizedState;a!==h||f!==x||Be.current||Zt?(typeof m=="function"&&($l(e,n,m,r),x=e.memoizedState),(u=Zt||ld(e,n,u,r,f,x,l)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,x,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,x,l)),typeof o.componentDidUpdate=="function"&&(e.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=x),o.props=r,o.state=x,o.context=l,r=u):(typeof o.componentDidUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),r=!1)}return Ol(t,e,n,r,s,i)}function Ol(t,e,n,r,i,s){tp(t,e);var o=(e.flags&128)!==0;if(!r&&!o)return i&&ed(e,n,!1),Vt(t,e,s);r=e.stateNode,Xx.current=e;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return e.flags|=1,t!==null&&o?(e.child=Mr(e,t.child,null,s),e.child=Mr(e,null,a,s)):Te(t,e,a,s),e.memoizedState=r.state,i&&ed(e,n,!0),e.child}function np(t){var e=t.stateNode;e.pendingContext?Ju(t,e.pendingContext,e.pendingContext!==e.context):e.context&&Ju(t,e.context,!1),Ac(t,e.containerInfo)}function gd(t,e,n,r,i){return Er(),$c(i),e.flags|=256,Te(t,e,n,r),e.child}var Ll={dehydrated:null,treeContext:null,retryLane:0};function Al(t){return{baseLanes:t,cachePool:null,transitions:null}}function rp(t,e,n){var r=e.pendingProps,i=ie.current,s=!1,o=(e.flags&128)!==0,a;if((a=o)||(a=t!==null&&t.memoizedState===null?!1:(i&2)!==0),a?(s=!0,e.flags&=-129):(t===null||t.memoizedState!==null)&&(i|=1),Z(ie,i&1),t===null)return El(e),t=e.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?(e.mode&1?t.data==="$!"?e.lanes=8:e.lanes=1073741824:e.lanes=1,null):(o=r.children,t=r.fallback,s?(r=e.mode,s=e.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=ta(o,r,0,null),t=In(t,r,n,null),s.return=e,t.return=e,s.sibling=t,e.child=s,e.child.memoizedState=Al(n),e.memoizedState=Ll,t):Vc(e,o));if(i=t.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return Kx(t,e,o,r,a,i,n);if(s){s=r.fallback,o=e.mode,i=t.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(o&1)&&e.child!==i?(r=e.child,r.childLanes=0,r.pendingProps=l,e.deletions=null):(r=mn(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?s=mn(a,s):(s=In(s,o,n,null),s.flags|=2),s.return=e,r.return=e,r.sibling=s,e.child=r,r=s,s=e.child,o=t.child.memoizedState,o=o===null?Al(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=t.childLanes&~n,e.memoizedState=Ll,r}return s=t.child,t=s.sibling,r=mn(s,{mode:"visible",children:r.children}),!(e.mode&1)&&(r.lanes=n),r.return=e,r.sibling=null,t!==null&&(n=e.deletions,n===null?(e.deletions=[t],e.flags|=16):n.push(t)),e.child=r,e.memoizedState=null,r}function Vc(t,e){return e=ta({mode:"visible",children:e},t.mode,0,null),e.return=t,t.child=e}function ws(t,e,n,r){return r!==null&&$c(r),Mr(e,t.child,null,n),t=Vc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Kx(t,e,n,r,i,s,o){if(n)return e.flags&256?(e.flags&=-257,r=Oa(Error(P(422))),ws(t,e,o,r)):e.memoizedState!==null?(e.child=t.child,e.flags|=128,null):(s=r.fallback,i=e.mode,r=ta({mode:"visible",children:r.children},i,0,null),s=In(s,i,o,null),s.flags|=2,r.return=e,s.return=e,r.sibling=s,e.child=r,e.mode&1&&Mr(e,t.child,null,o),e.child.memoizedState=Al(o),e.memoizedState=Ll,s);if(!(e.mode&1))return ws(t,e,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(P(419)),r=Oa(s,r,void 0),ws(t,e,o,r)}if(a=(o&t.childLanes)!==0,Ie||a){if(r=be,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,Wt(t,i),xt(r,t,i,-1))}return qc(),r=Oa(Error(P(421))),ws(t,e,o,r)}return i.data==="$?"?(e.flags|=128,e.child=t.child,e=av.bind(null,t),i._reactRetry=e,null):(t=s.treeContext,Qe=un(i.nextSibling),Ge=e,re=!0,ft=null,t!==null&&(tt[nt++]=Ft,tt[nt++]=zt,tt[nt++]=Hn,Ft=t.id,zt=t.overflow,Hn=e),e=Vc(e,r.children),e.flags|=4096,e)}function xd(t,e,n){t.lanes|=e;var r=t.alternate;r!==null&&(r.lanes|=e),Ml(t.return,e,n)}function La(t,e,n,r,i){var s=t.memoizedState;s===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=e,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function ip(t,e,n){var r=e.pendingProps,i=r.revealOrder,s=r.tail;if(Te(t,e,r.children,n),r=ie.current,r&2)r=r&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)e:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&xd(t,n,e);else if(t.tag===19)xd(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}if(Z(ie,r),!(e.mode&1))e.memoizedState=null;else switch(i){case"forwards":for(n=e.child,i=null;n!==null;)t=n.alternate,t!==null&&yo(t)===null&&(i=n),n=n.sibling;n=i,n===null?(i=e.child,e.child=null):(i=n.sibling,n.sibling=null),La(e,!1,i,n,s);break;case"backwards":for(n=null,i=e.child,e.child=null;i!==null;){if(t=i.alternate,t!==null&&yo(t)===null){e.child=i;break}t=i.sibling,i.sibling=n,n=i,i=t}La(e,!0,n,null,s);break;case"together":La(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Xs(t,e){!(e.mode&1)&&t!==null&&(t.alternate=null,e.alternate=null,e.flags|=2)}function Vt(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Vn|=e.lanes,!(n&e.childLanes))return null;if(t!==null&&e.child!==t.child)throw Error(P(153));if(e.child!==null){for(t=e.child,n=mn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=mn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Qx(t,e,n){switch(e.tag){case 3:np(e),Er();break;case 5:Mm(e);break;case 1:He(e.type)&&fo(e);break;case 4:Ac(e,e.stateNode.containerInfo);break;case 10:var r=e.type._context,i=e.memoizedProps.value;Z(go,r._currentValue),r._currentValue=i;break;case 13:if(r=e.memoizedState,r!==null)return r.dehydrated!==null?(Z(ie,ie.current&1),e.flags|=128,null):n&e.child.childLanes?rp(t,e,n):(Z(ie,ie.current&1),t=Vt(t,e,n),t!==null?t.sibling:null);Z(ie,ie.current&1);break;case 19:if(r=(n&e.childLanes)!==0,t.flags&128){if(r)return ip(t,e,n);e.flags|=128}if(i=e.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),Z(ie,ie.current),r)break;return null;case 22:case 23:return e.lanes=0,ep(t,e,n)}return Vt(t,e,n)}var sp,Fl,op,ap;sp=function(t,e){for(var n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Fl=function(){};op=function(t,e,n,r){var i=t.memoizedProps;if(i!==r){t=e.stateNode,An(Pt.current);var s=null;switch(n){case"input":i=ol(t,i),r=ol(t,r),s=[];break;case"select":i=oe({},i,{value:void 0}),r=oe({},r,{value:void 0}),s=[];break;case"textarea":i=cl(t,i),r=cl(t,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(t.onclick=uo)}dl(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var a=i[u];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Ci.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var l=r[u];if(a=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(o in a)!a.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&a[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(s||(s=[]),s.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(s=s||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Ci.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&J("scroll",t),s||a===l||(s=[])):(s=s||[]).push(u,l))}n&&(s=s||[]).push("style",n);var u=s;(e.updateQueue=u)&&(e.flags|=4)}};ap=function(t,e,n,r){n!==r&&(e.flags|=4)};function qr(t,e){if(!re)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function Ne(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,r=0;if(e)for(var i=t.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=t,i=i.sibling;else for(i=t.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=t,i=i.sibling;return t.subtreeFlags|=r,t.childLanes=n,e}function Gx(t,e,n){var r=e.pendingProps;switch(Mc(e),e.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ne(e),null;case 1:return He(e.type)&&ho(),Ne(e),null;case 3:return r=e.stateNode,$r(),te(Be),te(Ee),zc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(t===null||t.child===null)&&(ys(e)?e.flags|=4:t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,ft!==null&&(Yl(ft),ft=null))),Fl(t,e),Ne(e),null;case 5:Fc(e);var i=An(zi.current);if(n=e.type,t!==null&&e.stateNode!=null)op(t,e,n,r,i),t.ref!==e.ref&&(e.flags|=512,e.flags|=2097152);else{if(!r){if(e.stateNode===null)throw Error(P(166));return Ne(e),null}if(t=An(Pt.current),ys(e)){r=e.stateNode,n=e.type;var s=e.memoizedProps;switch(r[Nt]=e,r[Ai]=s,t=(e.mode&1)!==0,n){case"dialog":J("cancel",r),J("close",r);break;case"iframe":case"object":case"embed":J("load",r);break;case"video":case"audio":for(i=0;i<ci.length;i++)J(ci[i],r);break;case"source":J("error",r);break;case"img":case"image":case"link":J("error",r),J("load",r);break;case"details":J("toggle",r);break;case"input":ju(r,s),J("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},J("invalid",r);break;case"textarea":Pu(r,s),J("invalid",r)}dl(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var a=s[o];o==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&vs(r.textContent,a,t),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&vs(r.textContent,a,t),i=["children",""+a]):Ci.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&J("scroll",r)}switch(n){case"input":us(r),Cu(r,s,!0);break;case"textarea":us(r),Eu(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=uo)}r=i,e.updateQueue=r,r!==null&&(e.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=Of(n)),t==="http://www.w3.org/1999/xhtml"?n==="script"?(t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof r.is=="string"?t=o.createElement(n,{is:r.is}):(t=o.createElement(n),n==="select"&&(o=t,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):t=o.createElementNS(t,n),t[Nt]=e,t[Ai]=r,sp(t,e,!1,!1),e.stateNode=t;e:{switch(o=hl(n,r),n){case"dialog":J("cancel",t),J("close",t),i=r;break;case"iframe":case"object":case"embed":J("load",t),i=r;break;case"video":case"audio":for(i=0;i<ci.length;i++)J(ci[i],t);i=r;break;case"source":J("error",t),i=r;break;case"img":case"image":case"link":J("error",t),J("load",t),i=r;break;case"details":J("toggle",t),i=r;break;case"input":ju(t,r),i=ol(t,r),J("invalid",t);break;case"option":i=r;break;case"select":t._wrapperState={wasMultiple:!!r.multiple},i=oe({},r,{value:void 0}),J("invalid",t);break;case"textarea":Pu(t,r),i=cl(t,r),J("invalid",t);break;default:i=r}dl(n,i),a=i;for(s in a)if(a.hasOwnProperty(s)){var l=a[s];s==="style"?Ff(t,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Lf(t,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Pi(t,l):typeof l=="number"&&Pi(t,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Ci.hasOwnProperty(s)?l!=null&&s==="onScroll"&&J("scroll",t):l!=null&&pc(t,s,l,o))}switch(n){case"input":us(t),Cu(t,r,!1);break;case"textarea":us(t),Eu(t);break;case"option":r.value!=null&&t.setAttribute("value",""+yn(r.value));break;case"select":t.multiple=!!r.multiple,s=r.value,s!=null?xr(t,!!r.multiple,s,!1):r.defaultValue!=null&&xr(t,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(t.onclick=uo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(e.flags|=4)}e.ref!==null&&(e.flags|=512,e.flags|=2097152)}return Ne(e),null;case 6:if(t&&e.stateNode!=null)ap(t,e,t.memoizedProps,r);else{if(typeof r!="string"&&e.stateNode===null)throw Error(P(166));if(n=An(zi.current),An(Pt.current),ys(e)){if(r=e.stateNode,n=e.memoizedProps,r[Nt]=e,(s=r.nodeValue!==n)&&(t=Ge,t!==null))switch(t.tag){case 3:vs(r.nodeValue,n,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&vs(r.nodeValue,n,(t.mode&1)!==0)}s&&(e.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Nt]=e,e.stateNode=r}return Ne(e),null;case 13:if(te(ie),r=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(re&&Qe!==null&&e.mode&1&&!(e.flags&128))Nm(),Er(),e.flags|=98560,s=!1;else if(s=ys(e),r!==null&&r.dehydrated!==null){if(t===null){if(!s)throw Error(P(318));if(s=e.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(P(317));s[Nt]=e}else Er(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;Ne(e),s=!1}else ft!==null&&(Yl(ft),ft=null),s=!0;if(!s)return e.flags&65536?e:null}return e.flags&128?(e.lanes=n,e):(r=r!==null,r!==(t!==null&&t.memoizedState!==null)&&r&&(e.child.flags|=8192,e.mode&1&&(t===null||ie.current&1?xe===0&&(xe=3):qc())),e.updateQueue!==null&&(e.flags|=4),Ne(e),null);case 4:return $r(),Fl(t,e),t===null&&Oi(e.stateNode.containerInfo),Ne(e),null;case 10:return Dc(e.type._context),Ne(e),null;case 17:return He(e.type)&&ho(),Ne(e),null;case 19:if(te(ie),s=e.memoizedState,s===null)return Ne(e),null;if(r=(e.flags&128)!==0,o=s.rendering,o===null)if(r)qr(s,!1);else{if(xe!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(o=yo(t),o!==null){for(e.flags|=128,qr(s,!1),r=o.updateQueue,r!==null&&(e.updateQueue=r,e.flags|=4),e.subtreeFlags=0,r=n,n=e.child;n!==null;)s=n,t=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=t,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,t=o.dependencies,s.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),n=n.sibling;return Z(ie,ie.current&1|2),e.child}t=t.sibling}s.tail!==null&&ce()>Rr&&(e.flags|=128,r=!0,qr(s,!1),e.lanes=4194304)}else{if(!r)if(t=yo(o),t!==null){if(e.flags|=128,r=!0,n=t.updateQueue,n!==null&&(e.updateQueue=n,e.flags|=4),qr(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!re)return Ne(e),null}else 2*ce()-s.renderingStartTime>Rr&&n!==1073741824&&(e.flags|=128,r=!0,qr(s,!1),e.lanes=4194304);s.isBackwards?(o.sibling=e.child,e.child=o):(n=s.last,n!==null?n.sibling=o:e.child=o,s.last=o)}return s.tail!==null?(e=s.tail,s.rendering=e,s.tail=e.sibling,s.renderingStartTime=ce(),e.sibling=null,n=ie.current,Z(ie,r?n&1|2:n&1),e):(Ne(e),null);case 22:case 23:return Gc(),r=e.memoizedState!==null,t!==null&&t.memoizedState!==null!==r&&(e.flags|=8192),r&&e.mode&1?Ke&1073741824&&(Ne(e),e.subtreeFlags&6&&(e.flags|=8192)):Ne(e),null;case 24:return null;case 25:return null}throw Error(P(156,e.tag))}function qx(t,e){switch(Mc(e),e.tag){case 1:return He(e.type)&&ho(),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return $r(),te(Be),te(Ee),zc(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 5:return Fc(e),null;case 13:if(te(ie),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(P(340));Er()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return te(ie),null;case 4:return $r(),null;case 10:return Dc(e.type._context),null;case 22:case 23:return Gc(),null;case 24:return null;default:return null}}var Ss=!1,Ce=!1,Zx=typeof WeakSet=="function"?WeakSet:Set,O=null;function pr(t,e){var n=t.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ae(t,e,r)}else n.current=null}function zl(t,e,n){try{n()}catch(r){ae(t,e,r)}}var vd=!1;function Jx(t,e){if(Sl=ao,t=hm(),Pc(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else e:{n=(n=t.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,a=-1,l=-1,u=0,d=0,h=t,f=null;t:for(;;){for(var m;h!==n||i!==0&&h.nodeType!==3||(a=o+i),h!==s||r!==0&&h.nodeType!==3||(l=o+r),h.nodeType===3&&(o+=h.nodeValue.length),(m=h.firstChild)!==null;)f=h,h=m;for(;;){if(h===t)break t;if(f===n&&++u===i&&(a=o),f===s&&++d===r&&(l=o),(m=h.nextSibling)!==null)break;h=f,f=h.parentNode}h=m}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(_l={focusedElem:t,selectionRange:n},ao=!1,O=e;O!==null;)if(e=O,t=e.child,(e.subtreeFlags&1028)!==0&&t!==null)t.return=e,O=t;else for(;O!==null;){e=O;try{var x=e.alternate;if(e.flags&1024)switch(e.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var v=x.memoizedProps,b=x.memoizedState,g=e.stateNode,p=g.getSnapshotBeforeUpdate(e.elementType===e.type?v:dt(e.type,v),b);g.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var y=e.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(P(163))}}catch(w){ae(e,e.return,w)}if(t=e.sibling,t!==null){t.return=e.return,O=t;break}O=e.return}return x=vd,vd=!1,x}function wi(t,e,n){var r=e.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&t)===t){var s=i.destroy;i.destroy=void 0,s!==void 0&&zl(e,n,s)}i=i.next}while(i!==r)}}function Jo(t,e){if(e=e.updateQueue,e=e!==null?e.lastEffect:null,e!==null){var n=e=e.next;do{if((n.tag&t)===t){var r=n.create;n.destroy=r()}n=n.next}while(n!==e)}}function Il(t){var e=t.ref;if(e!==null){var n=t.stateNode;switch(t.tag){case 5:t=n;break;default:t=n}typeof e=="function"?e(t):e.current=t}}function lp(t){var e=t.alternate;e!==null&&(t.alternate=null,lp(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&(delete e[Nt],delete e[Ai],delete e[jl],delete e[Ox],delete e[Lx])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function cp(t){return t.tag===5||t.tag===3||t.tag===4}function yd(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||cp(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Ul(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.nodeType===8?n.parentNode.insertBefore(t,e):n.insertBefore(t,e):(n.nodeType===8?(e=n.parentNode,e.insertBefore(t,n)):(e=n,e.appendChild(t)),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=uo));else if(r!==4&&(t=t.child,t!==null))for(Ul(t,e,n),t=t.sibling;t!==null;)Ul(t,e,n),t=t.sibling}function Bl(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(r!==4&&(t=t.child,t!==null))for(Bl(t,e,n),t=t.sibling;t!==null;)Bl(t,e,n),t=t.sibling}var we=null,ht=!1;function Kt(t,e,n){for(n=n.child;n!==null;)up(t,e,n),n=n.sibling}function up(t,e,n){if(Ct&&typeof Ct.onCommitFiberUnmount=="function")try{Ct.onCommitFiberUnmount(Vo,n)}catch{}switch(n.tag){case 5:Ce||pr(n,e);case 6:var r=we,i=ht;we=null,Kt(t,e,n),we=r,ht=i,we!==null&&(ht?(t=we,n=n.stateNode,t.nodeType===8?t.parentNode.removeChild(n):t.removeChild(n)):we.removeChild(n.stateNode));break;case 18:we!==null&&(ht?(t=we,n=n.stateNode,t.nodeType===8?Ea(t.parentNode,n):t.nodeType===1&&Ea(t,n),Ti(t)):Ea(we,n.stateNode));break;case 4:r=we,i=ht,we=n.stateNode.containerInfo,ht=!0,Kt(t,e,n),we=r,ht=i;break;case 0:case 11:case 14:case 15:if(!Ce&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&zl(n,e,o),i=i.next}while(i!==r)}Kt(t,e,n);break;case 1:if(!Ce&&(pr(n,e),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ae(n,e,a)}Kt(t,e,n);break;case 21:Kt(t,e,n);break;case 22:n.mode&1?(Ce=(r=Ce)||n.memoizedState!==null,Kt(t,e,n),Ce=r):Kt(t,e,n);break;default:Kt(t,e,n)}}function bd(t){var e=t.updateQueue;if(e!==null){t.updateQueue=null;var n=t.stateNode;n===null&&(n=t.stateNode=new Zx),e.forEach(function(r){var i=lv.bind(null,t,r);n.has(r)||(n.add(r),r.then(i,i))})}}function ut(t,e){var n=e.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=t,o=e,a=o;e:for(;a!==null;){switch(a.tag){case 5:we=a.stateNode,ht=!1;break e;case 3:we=a.stateNode.containerInfo,ht=!0;break e;case 4:we=a.stateNode.containerInfo,ht=!0;break e}a=a.return}if(we===null)throw Error(P(160));up(s,o,i),we=null,ht=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){ae(i,e,u)}}if(e.subtreeFlags&12854)for(e=e.child;e!==null;)dp(e,t),e=e.sibling}function dp(t,e){var n=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(ut(e,t),bt(t),r&4){try{wi(3,t,t.return),Jo(3,t)}catch(v){ae(t,t.return,v)}try{wi(5,t,t.return)}catch(v){ae(t,t.return,v)}}break;case 1:ut(e,t),bt(t),r&512&&n!==null&&pr(n,n.return);break;case 5:if(ut(e,t),bt(t),r&512&&n!==null&&pr(n,n.return),t.flags&32){var i=t.stateNode;try{Pi(i,"")}catch(v){ae(t,t.return,v)}}if(r&4&&(i=t.stateNode,i!=null)){var s=t.memoizedProps,o=n!==null?n.memoizedProps:s,a=t.type,l=t.updateQueue;if(t.updateQueue=null,l!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&Rf(i,s),hl(a,o);var u=hl(a,s);for(o=0;o<l.length;o+=2){var d=l[o],h=l[o+1];d==="style"?Ff(i,h):d==="dangerouslySetInnerHTML"?Lf(i,h):d==="children"?Pi(i,h):pc(i,d,h,u)}switch(a){case"input":al(i,s);break;case"textarea":Df(i,s);break;case"select":var f=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var m=s.value;m!=null?xr(i,!!s.multiple,m,!1):f!==!!s.multiple&&(s.defaultValue!=null?xr(i,!!s.multiple,s.defaultValue,!0):xr(i,!!s.multiple,s.multiple?[]:"",!1))}i[Ai]=s}catch(v){ae(t,t.return,v)}}break;case 6:if(ut(e,t),bt(t),r&4){if(t.stateNode===null)throw Error(P(162));i=t.stateNode,s=t.memoizedProps;try{i.nodeValue=s}catch(v){ae(t,t.return,v)}}break;case 3:if(ut(e,t),bt(t),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Ti(e.containerInfo)}catch(v){ae(t,t.return,v)}break;case 4:ut(e,t),bt(t);break;case 13:ut(e,t),bt(t),i=t.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(Kc=ce())),r&4&&bd(t);break;case 22:if(d=n!==null&&n.memoizedState!==null,t.mode&1?(Ce=(u=Ce)||d,ut(e,t),Ce=u):ut(e,t),bt(t),r&8192){if(u=t.memoizedState!==null,(t.stateNode.isHidden=u)&&!d&&t.mode&1)for(O=t,d=t.child;d!==null;){for(h=O=d;O!==null;){switch(f=O,m=f.child,f.tag){case 0:case 11:case 14:case 15:wi(4,f,f.return);break;case 1:pr(f,f.return);var x=f.stateNode;if(typeof x.componentWillUnmount=="function"){r=f,n=f.return;try{e=r,x.props=e.memoizedProps,x.state=e.memoizedState,x.componentWillUnmount()}catch(v){ae(r,n,v)}}break;case 5:pr(f,f.return);break;case 22:if(f.memoizedState!==null){Sd(h);continue}}m!==null?(m.return=f,O=m):Sd(h)}d=d.sibling}e:for(d=null,h=t;;){if(h.tag===5){if(d===null){d=h;try{i=h.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=h.stateNode,l=h.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Af("display",o))}catch(v){ae(t,t.return,v)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=u?"":h.memoizedProps}catch(v){ae(t,t.return,v)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===t)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===t)break e;for(;h.sibling===null;){if(h.return===null||h.return===t)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:ut(e,t),bt(t),r&4&&bd(t);break;case 21:break;default:ut(e,t),bt(t)}}function bt(t){var e=t.flags;if(e&2){try{e:{for(var n=t.return;n!==null;){if(cp(n)){var r=n;break e}n=n.return}throw Error(P(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Pi(i,""),r.flags&=-33);var s=yd(t);Bl(t,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,a=yd(t);Ul(t,a,o);break;default:throw Error(P(161))}}catch(l){ae(t,t.return,l)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function ev(t,e,n){O=t,hp(t)}function hp(t,e,n){for(var r=(t.mode&1)!==0;O!==null;){var i=O,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||Ss;if(!o){var a=i.alternate,l=a!==null&&a.memoizedState!==null||Ce;a=Ss;var u=Ce;if(Ss=o,(Ce=l)&&!u)for(O=i;O!==null;)o=O,l=o.child,o.tag===22&&o.memoizedState!==null?_d(i):l!==null?(l.return=o,O=l):_d(i);for(;s!==null;)O=s,hp(s),s=s.sibling;O=i,Ss=a,Ce=u}wd(t)}else i.subtreeFlags&8772&&s!==null?(s.return=i,O=s):wd(t)}}function wd(t){for(;O!==null;){var e=O;if(e.flags&8772){var n=e.alternate;try{if(e.flags&8772)switch(e.tag){case 0:case 11:case 15:Ce||Jo(5,e);break;case 1:var r=e.stateNode;if(e.flags&4&&!Ce)if(n===null)r.componentDidMount();else{var i=e.elementType===e.type?n.memoizedProps:dt(e.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=e.updateQueue;s!==null&&sd(e,s,r);break;case 3:var o=e.updateQueue;if(o!==null){if(n=null,e.child!==null)switch(e.child.tag){case 5:n=e.child.stateNode;break;case 1:n=e.child.stateNode}sd(e,o,n)}break;case 5:var a=e.stateNode;if(n===null&&e.flags&4){n=a;var l=e.memoizedProps;switch(e.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(e.memoizedState===null){var u=e.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&Ti(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(P(163))}Ce||e.flags&512&&Il(e)}catch(f){ae(e,e.return,f)}}if(e===t){O=null;break}if(n=e.sibling,n!==null){n.return=e.return,O=n;break}O=e.return}}function Sd(t){for(;O!==null;){var e=O;if(e===t){O=null;break}var n=e.sibling;if(n!==null){n.return=e.return,O=n;break}O=e.return}}function _d(t){for(;O!==null;){var e=O;try{switch(e.tag){case 0:case 11:case 15:var n=e.return;try{Jo(4,e)}catch(l){ae(e,n,l)}break;case 1:var r=e.stateNode;if(typeof r.componentDidMount=="function"){var i=e.return;try{r.componentDidMount()}catch(l){ae(e,i,l)}}var s=e.return;try{Il(e)}catch(l){ae(e,s,l)}break;case 5:var o=e.return;try{Il(e)}catch(l){ae(e,o,l)}}}catch(l){ae(e,e.return,l)}if(e===t){O=null;break}var a=e.sibling;if(a!==null){a.return=e.return,O=a;break}O=e.return}}var tv=Math.ceil,So=Xt.ReactCurrentDispatcher,Yc=Xt.ReactCurrentOwner,st=Xt.ReactCurrentBatchConfig,H=0,be=null,de=null,Se=0,Ke=0,gr=_n(0),xe=0,Hi=null,Vn=0,ea=0,Xc=0,Si=null,Fe=null,Kc=0,Rr=1/0,Ot=null,_o=!1,Hl=null,hn=null,_s=!1,nn=null,ko=0,_i=0,Wl=null,Ks=-1,Qs=0;function Re(){return H&6?ce():Ks!==-1?Ks:Ks=ce()}function fn(t){return t.mode&1?H&2&&Se!==0?Se&-Se:Fx.transition!==null?(Qs===0&&(Qs=Gf()),Qs):(t=K,t!==0||(t=window.event,t=t===void 0?16:rm(t.type)),t):1}function xt(t,e,n,r){if(50<_i)throw _i=0,Wl=null,Error(P(185));Ji(t,n,r),(!(H&2)||t!==be)&&(t===be&&(!(H&2)&&(ea|=n),xe===4&&en(t,Se)),We(t,r),n===1&&H===0&&!(e.mode&1)&&(Rr=ce()+500,Go&&kn()))}function We(t,e){var n=t.callbackNode;F0(t,e);var r=oo(t,t===be?Se:0);if(r===0)n!==null&&Tu(n),t.callbackNode=null,t.callbackPriority=0;else if(e=r&-r,t.callbackPriority!==e){if(n!=null&&Tu(n),e===1)t.tag===0?Ax(kd.bind(null,t)):Sm(kd.bind(null,t)),Rx(function(){!(H&6)&&kn()}),n=null;else{switch(qf(r)){case 1:n=bc;break;case 4:n=Kf;break;case 16:n=so;break;case 536870912:n=Qf;break;default:n=so}n=bp(n,fp.bind(null,t))}t.callbackPriority=e,t.callbackNode=n}}function fp(t,e){if(Ks=-1,Qs=0,H&6)throw Error(P(327));var n=t.callbackNode;if(Sr()&&t.callbackNode!==n)return null;var r=oo(t,t===be?Se:0);if(r===0)return null;if(r&30||r&t.expiredLanes||e)e=No(t,r);else{e=r;var i=H;H|=2;var s=pp();(be!==t||Se!==e)&&(Ot=null,Rr=ce()+500,zn(t,e));do try{iv();break}catch(a){mp(t,a)}while(!0);Rc(),So.current=s,H=i,de!==null?e=0:(be=null,Se=0,e=xe)}if(e!==0){if(e===2&&(i=xl(t),i!==0&&(r=i,e=Vl(t,i))),e===1)throw n=Hi,zn(t,0),en(t,r),We(t,ce()),n;if(e===6)en(t,r);else{if(i=t.current.alternate,!(r&30)&&!nv(i)&&(e=No(t,r),e===2&&(s=xl(t),s!==0&&(r=s,e=Vl(t,s))),e===1))throw n=Hi,zn(t,0),en(t,r),We(t,ce()),n;switch(t.finishedWork=i,t.finishedLanes=r,e){case 0:case 1:throw Error(P(345));case 2:$n(t,Fe,Ot);break;case 3:if(en(t,r),(r&130023424)===r&&(e=Kc+500-ce(),10<e)){if(oo(t,0)!==0)break;if(i=t.suspendedLanes,(i&r)!==r){Re(),t.pingedLanes|=t.suspendedLanes&i;break}t.timeoutHandle=Nl($n.bind(null,t,Fe,Ot),e);break}$n(t,Fe,Ot);break;case 4:if(en(t,r),(r&4194240)===r)break;for(e=t.eventTimes,i=-1;0<r;){var o=31-gt(r);s=1<<o,o=e[o],o>i&&(i=o),r&=~s}if(r=i,r=ce()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*tv(r/1960))-r,10<r){t.timeoutHandle=Nl($n.bind(null,t,Fe,Ot),r);break}$n(t,Fe,Ot);break;case 5:$n(t,Fe,Ot);break;default:throw Error(P(329))}}}return We(t,ce()),t.callbackNode===n?fp.bind(null,t):null}function Vl(t,e){var n=Si;return t.current.memoizedState.isDehydrated&&(zn(t,e).flags|=256),t=No(t,e),t!==2&&(e=Fe,Fe=n,e!==null&&Yl(e)),t}function Yl(t){Fe===null?Fe=t:Fe.push.apply(Fe,t)}function nv(t){for(var e=t;;){if(e.flags&16384){var n=e.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!vt(s(),i))return!1}catch{return!1}}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function en(t,e){for(e&=~Xc,e&=~ea,t.suspendedLanes|=e,t.pingedLanes&=~e,t=t.expirationTimes;0<e;){var n=31-gt(e),r=1<<n;t[n]=-1,e&=~r}}function kd(t){if(H&6)throw Error(P(327));Sr();var e=oo(t,0);if(!(e&1))return We(t,ce()),null;var n=No(t,e);if(t.tag!==0&&n===2){var r=xl(t);r!==0&&(e=r,n=Vl(t,r))}if(n===1)throw n=Hi,zn(t,0),en(t,e),We(t,ce()),n;if(n===6)throw Error(P(345));return t.finishedWork=t.current.alternate,t.finishedLanes=e,$n(t,Fe,Ot),We(t,ce()),null}function Qc(t,e){var n=H;H|=1;try{return t(e)}finally{H=n,H===0&&(Rr=ce()+500,Go&&kn())}}function Yn(t){nn!==null&&nn.tag===0&&!(H&6)&&Sr();var e=H;H|=1;var n=st.transition,r=K;try{if(st.transition=null,K=1,t)return t()}finally{K=r,st.transition=n,H=e,!(H&6)&&kn()}}function Gc(){Ke=gr.current,te(gr)}function zn(t,e){t.finishedWork=null,t.finishedLanes=0;var n=t.timeoutHandle;if(n!==-1&&(t.timeoutHandle=-1,Tx(n)),de!==null)for(n=de.return;n!==null;){var r=n;switch(Mc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ho();break;case 3:$r(),te(Be),te(Ee),zc();break;case 5:Fc(r);break;case 4:$r();break;case 13:te(ie);break;case 19:te(ie);break;case 10:Dc(r.type._context);break;case 22:case 23:Gc()}n=n.return}if(be=t,de=t=mn(t.current,null),Se=Ke=e,xe=0,Hi=null,Xc=ea=Vn=0,Fe=Si=null,Ln!==null){for(e=0;e<Ln.length;e++)if(n=Ln[e],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}Ln=null}return t}function mp(t,e){do{var n=de;try{if(Rc(),Vs.current=wo,bo){for(var r=se.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}bo=!1}if(Wn=0,ye=pe=se=null,bi=!1,Ii=0,Yc.current=null,n===null||n.return===null){xe=1,Hi=e,de=null;break}e:{var s=t,o=n.return,a=n,l=e;if(e=Se,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,d=a,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var m=dd(o);if(m!==null){m.flags&=-257,hd(m,o,a,s,e),m.mode&1&&ud(s,u,e),e=m,l=u;var x=e.updateQueue;if(x===null){var v=new Set;v.add(l),e.updateQueue=v}else x.add(l);break e}else{if(!(e&1)){ud(s,u,e),qc();break e}l=Error(P(426))}}else if(re&&a.mode&1){var b=dd(o);if(b!==null){!(b.flags&65536)&&(b.flags|=256),hd(b,o,a,s,e),$c(Tr(l,a));break e}}s=l=Tr(l,a),xe!==4&&(xe=2),Si===null?Si=[s]:Si.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,e&=-e,s.lanes|=e;var g=qm(s,l,e);id(s,g);break e;case 1:a=l;var p=s.type,y=s.stateNode;if(!(s.flags&128)&&(typeof p.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(hn===null||!hn.has(y)))){s.flags|=65536,e&=-e,s.lanes|=e;var w=Zm(s,a,e);id(s,w);break e}}s=s.return}while(s!==null)}xp(n)}catch(_){e=_,de===n&&n!==null&&(de=n=n.return);continue}break}while(!0)}function pp(){var t=So.current;return So.current=wo,t===null?wo:t}function qc(){(xe===0||xe===3||xe===2)&&(xe=4),be===null||!(Vn&268435455)&&!(ea&268435455)||en(be,Se)}function No(t,e){var n=H;H|=2;var r=pp();(be!==t||Se!==e)&&(Ot=null,zn(t,e));do try{rv();break}catch(i){mp(t,i)}while(!0);if(Rc(),H=n,So.current=r,de!==null)throw Error(P(261));return be=null,Se=0,xe}function rv(){for(;de!==null;)gp(de)}function iv(){for(;de!==null&&!E0();)gp(de)}function gp(t){var e=yp(t.alternate,t,Ke);t.memoizedProps=t.pendingProps,e===null?xp(t):de=e,Yc.current=null}function xp(t){var e=t;do{var n=e.alternate;if(t=e.return,e.flags&32768){if(n=qx(n,e),n!==null){n.flags&=32767,de=n;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{xe=6,de=null;return}}else if(n=Gx(n,e,Ke),n!==null){de=n;return}if(e=e.sibling,e!==null){de=e;return}de=e=t}while(e!==null);xe===0&&(xe=5)}function $n(t,e,n){var r=K,i=st.transition;try{st.transition=null,K=1,sv(t,e,n,r)}finally{st.transition=i,K=r}return null}function sv(t,e,n,r){do Sr();while(nn!==null);if(H&6)throw Error(P(327));n=t.finishedWork;var i=t.finishedLanes;if(n===null)return null;if(t.finishedWork=null,t.finishedLanes=0,n===t.current)throw Error(P(177));t.callbackNode=null,t.callbackPriority=0;var s=n.lanes|n.childLanes;if(z0(t,s),t===be&&(de=be=null,Se=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||_s||(_s=!0,bp(so,function(){return Sr(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=st.transition,st.transition=null;var o=K;K=1;var a=H;H|=4,Yc.current=null,Jx(t,n),dp(n,t),Nx(_l),ao=!!Sl,_l=Sl=null,t.current=n,ev(n),M0(),H=a,K=o,st.transition=s}else t.current=n;if(_s&&(_s=!1,nn=t,ko=i),s=t.pendingLanes,s===0&&(hn=null),R0(n.stateNode),We(t,ce()),e!==null)for(r=t.onRecoverableError,n=0;n<e.length;n++)i=e[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(_o)throw _o=!1,t=Hl,Hl=null,t;return ko&1&&t.tag!==0&&Sr(),s=t.pendingLanes,s&1?t===Wl?_i++:(_i=0,Wl=t):_i=0,kn(),null}function Sr(){if(nn!==null){var t=qf(ko),e=st.transition,n=K;try{if(st.transition=null,K=16>t?16:t,nn===null)var r=!1;else{if(t=nn,nn=null,ko=0,H&6)throw Error(P(331));var i=H;for(H|=4,O=t.current;O!==null;){var s=O,o=s.child;if(O.flags&16){var a=s.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(O=u;O!==null;){var d=O;switch(d.tag){case 0:case 11:case 15:wi(8,d,s)}var h=d.child;if(h!==null)h.return=d,O=h;else for(;O!==null;){d=O;var f=d.sibling,m=d.return;if(lp(d),d===u){O=null;break}if(f!==null){f.return=m,O=f;break}O=m}}}var x=s.alternate;if(x!==null){var v=x.child;if(v!==null){x.child=null;do{var b=v.sibling;v.sibling=null,v=b}while(v!==null)}}O=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,O=o;else e:for(;O!==null;){if(s=O,s.flags&2048)switch(s.tag){case 0:case 11:case 15:wi(9,s,s.return)}var g=s.sibling;if(g!==null){g.return=s.return,O=g;break e}O=s.return}}var p=t.current;for(O=p;O!==null;){o=O;var y=o.child;if(o.subtreeFlags&2064&&y!==null)y.return=o,O=y;else e:for(o=p;O!==null;){if(a=O,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Jo(9,a)}}catch(_){ae(a,a.return,_)}if(a===o){O=null;break e}var w=a.sibling;if(w!==null){w.return=a.return,O=w;break e}O=a.return}}if(H=i,kn(),Ct&&typeof Ct.onPostCommitFiberRoot=="function")try{Ct.onPostCommitFiberRoot(Vo,t)}catch{}r=!0}return r}finally{K=n,st.transition=e}}return!1}function Nd(t,e,n){e=Tr(n,e),e=qm(t,e,1),t=dn(t,e,1),e=Re(),t!==null&&(Ji(t,1,e),We(t,e))}function ae(t,e,n){if(t.tag===3)Nd(t,t,n);else for(;e!==null;){if(e.tag===3){Nd(e,t,n);break}else if(e.tag===1){var r=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(hn===null||!hn.has(r))){t=Tr(n,t),t=Zm(e,t,1),e=dn(e,t,1),t=Re(),e!==null&&(Ji(e,1,t),We(e,t));break}}e=e.return}}function ov(t,e,n){var r=t.pingCache;r!==null&&r.delete(e),e=Re(),t.pingedLanes|=t.suspendedLanes&n,be===t&&(Se&n)===n&&(xe===4||xe===3&&(Se&130023424)===Se&&500>ce()-Kc?zn(t,0):Xc|=n),We(t,e)}function vp(t,e){e===0&&(t.mode&1?(e=fs,fs<<=1,!(fs&130023424)&&(fs=4194304)):e=1);var n=Re();t=Wt(t,e),t!==null&&(Ji(t,e,n),We(t,n))}function av(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),vp(t,n)}function lv(t,e){var n=0;switch(t.tag){case 13:var r=t.stateNode,i=t.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=t.stateNode;break;default:throw Error(P(314))}r!==null&&r.delete(e),vp(t,n)}var yp;yp=function(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps||Be.current)Ie=!0;else{if(!(t.lanes&n)&&!(e.flags&128))return Ie=!1,Qx(t,e,n);Ie=!!(t.flags&131072)}else Ie=!1,re&&e.flags&1048576&&_m(e,po,e.index);switch(e.lanes=0,e.tag){case 2:var r=e.type;Xs(t,e),t=e.pendingProps;var i=Pr(e,Ee.current);wr(e,n),i=Uc(null,e,r,t,i,n);var s=Bc();return e.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(e.tag=1,e.memoizedState=null,e.updateQueue=null,He(r)?(s=!0,fo(e)):s=!1,e.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Lc(e),i.updater=Zo,e.stateNode=i,i._reactInternals=e,Tl(e,r,t,n),e=Ol(null,e,r,!0,s,n)):(e.tag=0,re&&s&&Ec(e),Te(null,e,i,n),e=e.child),e;case 16:r=e.elementType;e:{switch(Xs(t,e),t=e.pendingProps,i=r._init,r=i(r._payload),e.type=r,i=e.tag=uv(r),t=dt(r,t),i){case 0:e=Dl(null,e,r,t,n);break e;case 1:e=pd(null,e,r,t,n);break e;case 11:e=fd(null,e,r,t,n);break e;case 14:e=md(null,e,r,dt(r.type,t),n);break e}throw Error(P(306,r,""))}return e;case 0:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:dt(r,i),Dl(t,e,r,i,n);case 1:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:dt(r,i),pd(t,e,r,i,n);case 3:e:{if(np(e),t===null)throw Error(P(387));r=e.pendingProps,s=e.memoizedState,i=s.element,Em(t,e),vo(e,r,null,n);var o=e.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},e.updateQueue.baseState=s,e.memoizedState=s,e.flags&256){i=Tr(Error(P(423)),e),e=gd(t,e,r,n,i);break e}else if(r!==i){i=Tr(Error(P(424)),e),e=gd(t,e,r,n,i);break e}else for(Qe=un(e.stateNode.containerInfo.firstChild),Ge=e,re=!0,ft=null,n=Cm(e,null,r,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Er(),r===i){e=Vt(t,e,n);break e}Te(t,e,r,n)}e=e.child}return e;case 5:return Mm(e),t===null&&El(e),r=e.type,i=e.pendingProps,s=t!==null?t.memoizedProps:null,o=i.children,kl(r,i)?o=null:s!==null&&kl(r,s)&&(e.flags|=32),tp(t,e),Te(t,e,o,n),e.child;case 6:return t===null&&El(e),null;case 13:return rp(t,e,n);case 4:return Ac(e,e.stateNode.containerInfo),r=e.pendingProps,t===null?e.child=Mr(e,null,r,n):Te(t,e,r,n),e.child;case 11:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:dt(r,i),fd(t,e,r,i,n);case 7:return Te(t,e,e.pendingProps,n),e.child;case 8:return Te(t,e,e.pendingProps.children,n),e.child;case 12:return Te(t,e,e.pendingProps.children,n),e.child;case 10:e:{if(r=e.type._context,i=e.pendingProps,s=e.memoizedProps,o=i.value,Z(go,r._currentValue),r._currentValue=o,s!==null)if(vt(s.value,o)){if(s.children===i.children&&!Be.current){e=Vt(t,e,n);break e}}else for(s=e.child,s!==null&&(s.return=e);s!==null;){var a=s.dependencies;if(a!==null){o=s.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=Ut(-1,n&-n),l.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?l.next=l:(l.next=d.next,d.next=l),u.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Ml(s.return,n,e),a.lanes|=n;break}l=l.next}}else if(s.tag===10)o=s.type===e.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(P(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),Ml(o,n,e),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===e){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}Te(t,e,i.children,n),e=e.child}return e;case 9:return i=e.type,r=e.pendingProps.children,wr(e,n),i=ot(i),r=r(i),e.flags|=1,Te(t,e,r,n),e.child;case 14:return r=e.type,i=dt(r,e.pendingProps),i=dt(r.type,i),md(t,e,r,i,n);case 15:return Jm(t,e,e.type,e.pendingProps,n);case 17:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:dt(r,i),Xs(t,e),e.tag=1,He(r)?(t=!0,fo(e)):t=!1,wr(e,n),Gm(e,r,i),Tl(e,r,i,n),Ol(null,e,r,!0,t,n);case 19:return ip(t,e,n);case 22:return ep(t,e,n)}throw Error(P(156,e.tag))};function bp(t,e){return Xf(t,e)}function cv(t,e,n,r){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function rt(t,e,n,r){return new cv(t,e,n,r)}function Zc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function uv(t){if(typeof t=="function")return Zc(t)?1:0;if(t!=null){if(t=t.$$typeof,t===xc)return 11;if(t===vc)return 14}return 2}function mn(t,e){var n=t.alternate;return n===null?(n=rt(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&14680064,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n}function Gs(t,e,n,r,i,s){var o=2;if(r=t,typeof t=="function")Zc(t)&&(o=1);else if(typeof t=="string")o=5;else e:switch(t){case or:return In(n.children,i,s,e);case gc:o=8,i|=8;break;case nl:return t=rt(12,n,e,i|2),t.elementType=nl,t.lanes=s,t;case rl:return t=rt(13,n,e,i),t.elementType=rl,t.lanes=s,t;case il:return t=rt(19,n,e,i),t.elementType=il,t.lanes=s,t;case Mf:return ta(n,i,s,e);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Pf:o=10;break e;case Ef:o=9;break e;case xc:o=11;break e;case vc:o=14;break e;case qt:o=16,r=null;break e}throw Error(P(130,t==null?t:typeof t,""))}return e=rt(o,n,e,i),e.elementType=t,e.type=r,e.lanes=s,e}function In(t,e,n,r){return t=rt(7,t,r,e),t.lanes=n,t}function ta(t,e,n,r){return t=rt(22,t,r,e),t.elementType=Mf,t.lanes=n,t.stateNode={isHidden:!1},t}function Aa(t,e,n){return t=rt(6,t,null,e),t.lanes=n,t}function Fa(t,e,n){return e=rt(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function dv(t,e,n,r,i){this.tag=e,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=va(0),this.expirationTimes=va(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=va(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Jc(t,e,n,r,i,s,o,a,l){return t=new dv(t,e,n,a,l),e===1?(e=1,s===!0&&(e|=8)):e=0,s=rt(3,null,null,e),t.current=s,s.stateNode=t,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Lc(s),t}function hv(t,e,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:sr,key:r==null?null:""+r,children:t,containerInfo:e,implementation:n}}function wp(t){if(!t)return bn;t=t._reactInternals;e:{if(qn(t)!==t||t.tag!==1)throw Error(P(170));var e=t;do{switch(e.tag){case 3:e=e.stateNode.context;break e;case 1:if(He(e.type)){e=e.stateNode.__reactInternalMemoizedMergedChildContext;break e}}e=e.return}while(e!==null);throw Error(P(171))}if(t.tag===1){var n=t.type;if(He(n))return wm(t,n,e)}return e}function Sp(t,e,n,r,i,s,o,a,l){return t=Jc(n,r,!0,t,i,s,o,a,l),t.context=wp(null),n=t.current,r=Re(),i=fn(n),s=Ut(r,i),s.callback=e??null,dn(n,s,i),t.current.lanes=i,Ji(t,i,r),We(t,r),t}function na(t,e,n,r){var i=e.current,s=Re(),o=fn(i);return n=wp(n),e.context===null?e.context=n:e.pendingContext=n,e=Ut(s,o),e.payload={element:t},r=r===void 0?null:r,r!==null&&(e.callback=r),t=dn(i,e,o),t!==null&&(xt(t,i,o,s),Ws(t,i,o)),o}function jo(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function jd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function eu(t,e){jd(t,e),(t=t.alternate)&&jd(t,e)}function fv(){return null}var _p=typeof reportError=="function"?reportError:function(t){console.error(t)};function tu(t){this._internalRoot=t}ra.prototype.render=tu.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(P(409));na(t,e,null,null)};ra.prototype.unmount=tu.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Yn(function(){na(null,t,null,null)}),e[Ht]=null}};function ra(t){this._internalRoot=t}ra.prototype.unstable_scheduleHydration=function(t){if(t){var e=em();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Jt.length&&e!==0&&e<Jt[n].priority;n++);Jt.splice(n,0,t),n===0&&nm(t)}};function nu(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function ia(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function Cd(){}function mv(t,e,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=jo(o);s.call(u)}}var o=Sp(e,r,t,0,null,!1,!1,"",Cd);return t._reactRootContainer=o,t[Ht]=o.current,Oi(t.nodeType===8?t.parentNode:t),Yn(),o}for(;i=t.lastChild;)t.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var u=jo(l);a.call(u)}}var l=Jc(t,0,!1,null,null,!1,!1,"",Cd);return t._reactRootContainer=l,t[Ht]=l.current,Oi(t.nodeType===8?t.parentNode:t),Yn(function(){na(e,l,n,r)}),l}function sa(t,e,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var a=i;i=function(){var l=jo(o);a.call(l)}}na(e,o,t,i)}else o=mv(n,e,t,i,r);return jo(o)}Zf=function(t){switch(t.tag){case 3:var e=t.stateNode;if(e.current.memoizedState.isDehydrated){var n=li(e.pendingLanes);n!==0&&(wc(e,n|1),We(e,ce()),!(H&6)&&(Rr=ce()+500,kn()))}break;case 13:Yn(function(){var r=Wt(t,1);if(r!==null){var i=Re();xt(r,t,1,i)}}),eu(t,1)}};Sc=function(t){if(t.tag===13){var e=Wt(t,134217728);if(e!==null){var n=Re();xt(e,t,134217728,n)}eu(t,134217728)}};Jf=function(t){if(t.tag===13){var e=fn(t),n=Wt(t,e);if(n!==null){var r=Re();xt(n,t,e,r)}eu(t,e)}};em=function(){return K};tm=function(t,e){var n=K;try{return K=t,e()}finally{K=n}};ml=function(t,e,n){switch(e){case"input":if(al(t,n),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+e)+'][type="radio"]'),e=0;e<n.length;e++){var r=n[e];if(r!==t&&r.form===t.form){var i=Qo(r);if(!i)throw Error(P(90));Tf(r),al(r,i)}}}break;case"textarea":Df(t,n);break;case"select":e=n.value,e!=null&&xr(t,!!n.multiple,e,!1)}};Uf=Qc;Bf=Yn;var pv={usingClientEntryPoint:!1,Events:[ts,ur,Qo,zf,If,Qc]},Zr={findFiberByHostInstance:On,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},gv={bundleType:Zr.bundleType,version:Zr.version,rendererPackageName:Zr.rendererPackageName,rendererConfig:Zr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Xt.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=Vf(t),t===null?null:t.stateNode},findFiberByHostInstance:Zr.findFiberByHostInstance||fv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ks=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ks.isDisabled&&ks.supportsFiber)try{Vo=ks.inject(gv),Ct=ks}catch{}}Ze.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=pv;Ze.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!nu(e))throw Error(P(200));return hv(t,e,null,n)};Ze.createRoot=function(t,e){if(!nu(t))throw Error(P(299));var n=!1,r="",i=_p;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(r=e.identifierPrefix),e.onRecoverableError!==void 0&&(i=e.onRecoverableError)),e=Jc(t,1,!1,null,null,n,!1,r,i),t[Ht]=e.current,Oi(t.nodeType===8?t.parentNode:t),new tu(e)};Ze.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(P(188)):(t=Object.keys(t).join(","),Error(P(268,t)));return t=Vf(e),t=t===null?null:t.stateNode,t};Ze.flushSync=function(t){return Yn(t)};Ze.hydrate=function(t,e,n){if(!ia(e))throw Error(P(200));return sa(null,t,e,!0,n)};Ze.hydrateRoot=function(t,e,n){if(!nu(t))throw Error(P(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=_p;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),e=Sp(e,null,t,1,n??null,i,!1,s,o),t[Ht]=e.current,Oi(t),r)for(t=0;t<r.length;t++)n=r[t],i=n._getVersion,i=i(n._source),e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[n,i]:e.mutableSourceEagerHydrationData.push(n,i);return new ra(e)};Ze.render=function(t,e,n){if(!ia(e))throw Error(P(200));return sa(null,t,e,!1,n)};Ze.unmountComponentAtNode=function(t){if(!ia(t))throw Error(P(40));return t._reactRootContainer?(Yn(function(){sa(null,null,t,!1,function(){t._reactRootContainer=null,t[Ht]=null})}),!0):!1};Ze.unstable_batchedUpdates=Qc;Ze.unstable_renderSubtreeIntoContainer=function(t,e,n,r){if(!ia(n))throw Error(P(200));if(t==null||t._reactInternals===void 0)throw Error(P(38));return sa(t,e,n,!1,r)};Ze.version="18.3.1-next-f1338f8080-20240426";function kp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(kp)}catch(t){console.error(t)}}kp(),kf.exports=Ze;var xv=kf.exports,Pd=xv;el.createRoot=Pd.createRoot,el.hydrateRoot=Pd.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Wi(){return Wi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Wi.apply(this,arguments)}var rn;(function(t){t.Pop="POP",t.Push="PUSH",t.Replace="REPLACE"})(rn||(rn={}));const Ed="popstate";function vv(t){t===void 0&&(t={});function e(r,i){let{pathname:s,search:o,hash:a}=r.location;return Xl("",{pathname:s,search:o,hash:a},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:Co(i)}return bv(e,n,null,t)}function le(t,e){if(t===!1||t===null||typeof t>"u")throw new Error(e)}function Np(t,e){if(!t){typeof console<"u"&&console.warn(e);try{throw new Error(e)}catch{}}}function yv(){return Math.random().toString(36).substr(2,8)}function Md(t,e){return{usr:t.state,key:t.key,idx:e}}function Xl(t,e,n,r){return n===void 0&&(n=null),Wi({pathname:typeof t=="string"?t:t.pathname,search:"",hash:""},typeof e=="string"?Br(e):e,{state:n,key:e&&e.key||r||yv()})}function Co(t){let{pathname:e="/",search:n="",hash:r=""}=t;return n&&n!=="?"&&(e+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Br(t){let e={};if(t){let n=t.indexOf("#");n>=0&&(e.hash=t.substr(n),t=t.substr(0,n));let r=t.indexOf("?");r>=0&&(e.search=t.substr(r),t=t.substr(0,r)),t&&(e.pathname=t)}return e}function bv(t,e,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:s=!1}=r,o=i.history,a=rn.Pop,l=null,u=d();u==null&&(u=0,o.replaceState(Wi({},o.state,{idx:u}),""));function d(){return(o.state||{idx:null}).idx}function h(){a=rn.Pop;let b=d(),g=b==null?null:b-u;u=b,l&&l({action:a,location:v.location,delta:g})}function f(b,g){a=rn.Push;let p=Xl(v.location,b,g);u=d()+1;let y=Md(p,u),w=v.createHref(p);try{o.pushState(y,"",w)}catch(_){if(_ instanceof DOMException&&_.name==="DataCloneError")throw _;i.location.assign(w)}s&&l&&l({action:a,location:v.location,delta:1})}function m(b,g){a=rn.Replace;let p=Xl(v.location,b,g);u=d();let y=Md(p,u),w=v.createHref(p);o.replaceState(y,"",w),s&&l&&l({action:a,location:v.location,delta:0})}function x(b){let g=i.location.origin!=="null"?i.location.origin:i.location.href,p=typeof b=="string"?b:Co(b);return p=p.replace(/ $/,"%20"),le(g,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,g)}let v={get action(){return a},get location(){return t(i,o)},listen(b){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(Ed,h),l=b,()=>{i.removeEventListener(Ed,h),l=null}},createHref(b){return e(i,b)},createURL:x,encodeLocation(b){let g=x(b);return{pathname:g.pathname,search:g.search,hash:g.hash}},push:f,replace:m,go(b){return o.go(b)}};return v}var $d;(function(t){t.data="data",t.deferred="deferred",t.redirect="redirect",t.error="error"})($d||($d={}));function wv(t,e,n){return n===void 0&&(n="/"),Sv(t,e,n)}function Sv(t,e,n,r){let i=typeof e=="string"?Br(e):e,s=Dr(i.pathname||"/",n);if(s==null)return null;let o=jp(t);_v(o);let a=null;for(let l=0;a==null&&l<o.length;++l){let u=Dv(s);a=Tv(o[l],u)}return a}function jp(t,e,n,r){e===void 0&&(e=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(s,o,a)=>{let l={relativePath:a===void 0?s.path||"":a,caseSensitive:s.caseSensitive===!0,childrenIndex:o,route:s};l.relativePath.startsWith("/")&&(le(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=pn([r,l.relativePath]),d=n.concat(l);s.children&&s.children.length>0&&(le(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),jp(s.children,e,d,u)),!(s.path==null&&!s.index)&&e.push({path:u,score:Mv(u,s.index),routesMeta:d})};return t.forEach((s,o)=>{var a;if(s.path===""||!((a=s.path)!=null&&a.includes("?")))i(s,o);else for(let l of Cp(s.path))i(s,o,l)}),e}function Cp(t){let e=t.split("/");if(e.length===0)return[];let[n,...r]=e,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let o=Cp(r.join("/")),a=[];return a.push(...o.map(l=>l===""?s:[s,l].join("/"))),i&&a.push(...o),a.map(l=>t.startsWith("/")&&l===""?"/":l)}function _v(t){t.sort((e,n)=>e.score!==n.score?n.score-e.score:$v(e.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const kv=/^:[\w-]+$/,Nv=3,jv=2,Cv=1,Pv=10,Ev=-2,Td=t=>t==="*";function Mv(t,e){let n=t.split("/"),r=n.length;return n.some(Td)&&(r+=Ev),e&&(r+=jv),n.filter(i=>!Td(i)).reduce((i,s)=>i+(kv.test(s)?Nv:s===""?Cv:Pv),r)}function $v(t,e){return t.length===e.length&&t.slice(0,-1).every((r,i)=>r===e[i])?t[t.length-1]-e[e.length-1]:0}function Tv(t,e,n){let{routesMeta:r}=t,i={},s="/",o=[];for(let a=0;a<r.length;++a){let l=r[a],u=a===r.length-1,d=s==="/"?e:e.slice(s.length)||"/",h=Kl({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},d),f=l.route;if(!h)return null;Object.assign(i,h.params),o.push({params:i,pathname:pn([s,h.pathname]),pathnameBase:Fv(pn([s,h.pathnameBase])),route:f}),h.pathnameBase!=="/"&&(s=pn([s,h.pathnameBase]))}return o}function Kl(t,e){typeof t=="string"&&(t={path:t,caseSensitive:!1,end:!0});let[n,r]=Rv(t.path,t.caseSensitive,t.end),i=e.match(n);if(!i)return null;let s=i[0],o=s.replace(/(.)\/+$/,"$1"),a=i.slice(1);return{params:r.reduce((u,d,h)=>{let{paramName:f,isOptional:m}=d;if(f==="*"){let v=a[h]||"";o=s.slice(0,s.length-v.length).replace(/(.)\/+$/,"$1")}const x=a[h];return m&&!x?u[f]=void 0:u[f]=(x||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:o,pattern:t}}function Rv(t,e,n){e===void 0&&(e=!1),n===void 0&&(n=!0),Np(t==="*"||!t.endsWith("*")||t.endsWith("/*"),'Route path "'+t+'" will be treated as if it were '+('"'+t.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+t.replace(/\*$/,"/*")+'".'));let r=[],i="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return t.endsWith("*")?(r.push({paramName:"*"}),i+=t==="*"||t==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":t!==""&&t!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,e?void 0:"i"),r]}function Dv(t){try{return t.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(e){return Np(!1,'The URL path "'+t+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+e+").")),t}}function Dr(t,e){if(e==="/")return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let n=e.endsWith("/")?e.length-1:e.length,r=t.charAt(n);return r&&r!=="/"?null:t.slice(n)||"/"}function Ov(t,e){e===void 0&&(e="/");let{pathname:n,search:r="",hash:i=""}=typeof t=="string"?Br(t):t;return{pathname:n?n.startsWith("/")?n:Lv(n,e):e,search:zv(r),hash:Iv(i)}}function Lv(t,e){let n=e.replace(/\/+$/,"").split("/");return t.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function za(t,e,n,r){return"Cannot include a '"+t+"' character in a manually specified "+("`to."+e+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Av(t){return t.filter((e,n)=>n===0||e.route.path&&e.route.path.length>0)}function Pp(t,e){let n=Av(t);return e?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Ep(t,e,n,r){r===void 0&&(r=!1);let i;typeof t=="string"?i=Br(t):(i=Wi({},t),le(!i.pathname||!i.pathname.includes("?"),za("?","pathname","search",i)),le(!i.pathname||!i.pathname.includes("#"),za("#","pathname","hash",i)),le(!i.search||!i.search.includes("#"),za("#","search","hash",i)));let s=t===""||i.pathname==="",o=s?"/":i.pathname,a;if(o==null)a=n;else{let h=e.length-1;if(!r&&o.startsWith("..")){let f=o.split("/");for(;f[0]==="..";)f.shift(),h-=1;i.pathname=f.join("/")}a=h>=0?e[h]:"/"}let l=Ov(i,a),u=o&&o!=="/"&&o.endsWith("/"),d=(s||o===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||d)&&(l.pathname+="/"),l}const pn=t=>t.join("/").replace(/\/\/+/g,"/"),Fv=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),zv=t=>!t||t==="?"?"":t.startsWith("?")?t:"?"+t,Iv=t=>!t||t==="#"?"":t.startsWith("#")?t:"#"+t;function Uv(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.internal=="boolean"&&"data"in t}const Mp=["post","put","patch","delete"];new Set(Mp);const Bv=["get",...Mp];new Set(Bv);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Vi(){return Vi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Vi.apply(this,arguments)}const oa=N.createContext(null),$p=N.createContext(null),Nn=N.createContext(null),aa=N.createContext(null),Zn=N.createContext({outlet:null,matches:[],isDataRoute:!1}),Tp=N.createContext(null);function Hv(t,e){let{relative:n}=e===void 0?{}:e;rs()||le(!1);let{basename:r,navigator:i}=N.useContext(Nn),{hash:s,pathname:o,search:a}=la(t,{relative:n}),l=o;return r!=="/"&&(l=o==="/"?r:pn([r,o])),i.createHref({pathname:l,search:a,hash:s})}function rs(){return N.useContext(aa)!=null}function Et(){return rs()||le(!1),N.useContext(aa).location}function Rp(t){N.useContext(Nn).static||N.useLayoutEffect(t)}function Jn(){let{isDataRoute:t}=N.useContext(Zn);return t?ny():Wv()}function Wv(){rs()||le(!1);let t=N.useContext(oa),{basename:e,future:n,navigator:r}=N.useContext(Nn),{matches:i}=N.useContext(Zn),{pathname:s}=Et(),o=JSON.stringify(Pp(i,n.v7_relativeSplatPath)),a=N.useRef(!1);return Rp(()=>{a.current=!0}),N.useCallback(function(u,d){if(d===void 0&&(d={}),!a.current)return;if(typeof u=="number"){r.go(u);return}let h=Ep(u,JSON.parse(o),s,d.relative==="path");t==null&&e!=="/"&&(h.pathname=h.pathname==="/"?e:pn([e,h.pathname])),(d.replace?r.replace:r.push)(h,d.state,d)},[e,r,o,s,t])}function la(t,e){let{relative:n}=e===void 0?{}:e,{future:r}=N.useContext(Nn),{matches:i}=N.useContext(Zn),{pathname:s}=Et(),o=JSON.stringify(Pp(i,r.v7_relativeSplatPath));return N.useMemo(()=>Ep(t,JSON.parse(o),s,n==="path"),[t,o,s,n])}function Vv(t,e){return Yv(t,e)}function Yv(t,e,n,r){rs()||le(!1);let{navigator:i}=N.useContext(Nn),{matches:s}=N.useContext(Zn),o=s[s.length-1],a=o?o.params:{};o&&o.pathname;let l=o?o.pathnameBase:"/";o&&o.route;let u=Et(),d;if(e){var h;let b=typeof e=="string"?Br(e):e;l==="/"||(h=b.pathname)!=null&&h.startsWith(l)||le(!1),d=b}else d=u;let f=d.pathname||"/",m=f;if(l!=="/"){let b=l.replace(/^\//,"").split("/");m="/"+f.replace(/^\//,"").split("/").slice(b.length).join("/")}let x=wv(t,{pathname:m}),v=qv(x&&x.map(b=>Object.assign({},b,{params:Object.assign({},a,b.params),pathname:pn([l,i.encodeLocation?i.encodeLocation(b.pathname).pathname:b.pathname]),pathnameBase:b.pathnameBase==="/"?l:pn([l,i.encodeLocation?i.encodeLocation(b.pathnameBase).pathname:b.pathnameBase])})),s,n,r);return e&&v?N.createElement(aa.Provider,{value:{location:Vi({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:rn.Pop}},v):v}function Xv(){let t=ty(),e=Uv(t)?t.status+" "+t.statusText:t instanceof Error?t.message:JSON.stringify(t),n=t instanceof Error?t.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return N.createElement(N.Fragment,null,N.createElement("h2",null,"Unexpected Application Error!"),N.createElement("h3",{style:{fontStyle:"italic"}},e),n?N.createElement("pre",{style:i},n):null,null)}const Kv=N.createElement(Xv,null);class Qv extends N.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,n){return n.location!==e.location||n.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:n.error,location:n.location,revalidation:e.revalidation||n.revalidation}}componentDidCatch(e,n){console.error("React Router caught the following error during render",e,n)}render(){return this.state.error!==void 0?N.createElement(Zn.Provider,{value:this.props.routeContext},N.createElement(Tp.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Gv(t){let{routeContext:e,match:n,children:r}=t,i=N.useContext(oa);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),N.createElement(Zn.Provider,{value:e},r)}function qv(t,e,n,r){var i;if(e===void 0&&(e=[]),n===void 0&&(n=null),r===void 0&&(r=null),t==null){var s;if(!n)return null;if(n.errors)t=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&e.length===0&&!n.initialized&&n.matches.length>0)t=n.matches;else return null}let o=t,a=(i=n)==null?void 0:i.errors;if(a!=null){let d=o.findIndex(h=>h.route.id&&(a==null?void 0:a[h.route.id])!==void 0);d>=0||le(!1),o=o.slice(0,Math.min(o.length,d+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<o.length;d++){let h=o[d];if((h.route.HydrateFallback||h.route.hydrateFallbackElement)&&(u=d),h.route.id){let{loaderData:f,errors:m}=n,x=h.route.loader&&f[h.route.id]===void 0&&(!m||m[h.route.id]===void 0);if(h.route.lazy||x){l=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((d,h,f)=>{let m,x=!1,v=null,b=null;n&&(m=a&&h.route.id?a[h.route.id]:void 0,v=h.route.errorElement||Kv,l&&(u<0&&f===0?(ry("route-fallback"),x=!0,b=null):u===f&&(x=!0,b=h.route.hydrateFallbackElement||null)));let g=e.concat(o.slice(0,f+1)),p=()=>{let y;return m?y=v:x?y=b:h.route.Component?y=N.createElement(h.route.Component,null):h.route.element?y=h.route.element:y=d,N.createElement(Gv,{match:h,routeContext:{outlet:d,matches:g,isDataRoute:n!=null},children:y})};return n&&(h.route.ErrorBoundary||h.route.errorElement||f===0)?N.createElement(Qv,{location:n.location,revalidation:n.revalidation,component:v,error:m,children:p(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):p()},null)}var Dp=function(t){return t.UseBlocker="useBlocker",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t}(Dp||{}),Op=function(t){return t.UseBlocker="useBlocker",t.UseLoaderData="useLoaderData",t.UseActionData="useActionData",t.UseRouteError="useRouteError",t.UseNavigation="useNavigation",t.UseRouteLoaderData="useRouteLoaderData",t.UseMatches="useMatches",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t.UseRouteId="useRouteId",t}(Op||{});function Zv(t){let e=N.useContext(oa);return e||le(!1),e}function Jv(t){let e=N.useContext($p);return e||le(!1),e}function ey(t){let e=N.useContext(Zn);return e||le(!1),e}function Lp(t){let e=ey(),n=e.matches[e.matches.length-1];return n.route.id||le(!1),n.route.id}function ty(){var t;let e=N.useContext(Tp),n=Jv(),r=Lp();return e!==void 0?e:(t=n.errors)==null?void 0:t[r]}function ny(){let{router:t}=Zv(Dp.UseNavigateStable),e=Lp(Op.UseNavigateStable),n=N.useRef(!1);return Rp(()=>{n.current=!0}),N.useCallback(function(i,s){s===void 0&&(s={}),n.current&&(typeof i=="number"?t.navigate(i):t.navigate(i,Vi({fromRouteId:e},s)))},[t,e])}const Rd={};function ry(t,e,n){Rd[t]||(Rd[t]=!0)}function iy(t,e){t==null||t.v7_startTransition,t==null||t.v7_relativeSplatPath}function Tn(t){le(!1)}function sy(t){let{basename:e="/",children:n=null,location:r,navigationType:i=rn.Pop,navigator:s,static:o=!1,future:a}=t;rs()&&le(!1);let l=e.replace(/^\/*/,"/"),u=N.useMemo(()=>({basename:l,navigator:s,static:o,future:Vi({v7_relativeSplatPath:!1},a)}),[l,a,s,o]);typeof r=="string"&&(r=Br(r));let{pathname:d="/",search:h="",hash:f="",state:m=null,key:x="default"}=r,v=N.useMemo(()=>{let b=Dr(d,l);return b==null?null:{location:{pathname:b,search:h,hash:f,state:m,key:x},navigationType:i}},[l,d,h,f,m,x,i]);return v==null?null:N.createElement(Nn.Provider,{value:u},N.createElement(aa.Provider,{children:n,value:v}))}function oy(t){let{children:e,location:n}=t;return Vv(Ql(e),n)}new Promise(()=>{});function Ql(t,e){e===void 0&&(e=[]);let n=[];return N.Children.forEach(t,(r,i)=>{if(!N.isValidElement(r))return;let s=[...e,i];if(r.type===N.Fragment){n.push.apply(n,Ql(r.props.children,s));return}r.type!==Tn&&le(!1),!r.props.index||!r.props.children||le(!1);let o={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=Ql(r.props.children,s)),n.push(o)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Po(){return Po=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Po.apply(this,arguments)}function Ap(t,e){if(t==null)return{};var n={},r=Object.keys(t),i,s;for(s=0;s<r.length;s++)i=r[s],!(e.indexOf(i)>=0)&&(n[i]=t[i]);return n}function ay(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}function ly(t,e){return t.button===0&&(!e||e==="_self")&&!ay(t)}function Gl(t){return t===void 0&&(t=""),new URLSearchParams(typeof t=="string"||Array.isArray(t)||t instanceof URLSearchParams?t:Object.keys(t).reduce((e,n)=>{let r=t[n];return e.concat(Array.isArray(r)?r.map(i=>[n,i]):[[n,r]])},[]))}function cy(t,e){let n=Gl(t);return e&&e.forEach((r,i)=>{n.has(i)||e.getAll(i).forEach(s=>{n.append(i,s)})}),n}const uy=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],dy=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],hy="6";try{window.__reactRouterVersion=hy}catch{}const fy=N.createContext({isTransitioning:!1}),my="startTransition",Dd=o0[my];function py(t){let{basename:e,children:n,future:r,window:i}=t,s=N.useRef();s.current==null&&(s.current=vv({window:i,v5Compat:!0}));let o=s.current,[a,l]=N.useState({action:o.action,location:o.location}),{v7_startTransition:u}=r||{},d=N.useCallback(h=>{u&&Dd?Dd(()=>l(h)):l(h)},[l,u]);return N.useLayoutEffect(()=>o.listen(d),[o,d]),N.useEffect(()=>iy(r),[r]),N.createElement(sy,{basename:e,children:n,location:a.location,navigationType:a.action,navigator:o,future:r})}const gy=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",xy=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,vy=N.forwardRef(function(e,n){let{onClick:r,relative:i,reloadDocument:s,replace:o,state:a,target:l,to:u,preventScrollReset:d,viewTransition:h}=e,f=Ap(e,uy),{basename:m}=N.useContext(Nn),x,v=!1;if(typeof u=="string"&&xy.test(u)&&(x=u,gy))try{let y=new URL(window.location.href),w=u.startsWith("//")?new URL(y.protocol+u):new URL(u),_=Dr(w.pathname,m);w.origin===y.origin&&_!=null?u=_+w.search+w.hash:v=!0}catch{}let b=Hv(u,{relative:i}),g=by(u,{replace:o,state:a,target:l,preventScrollReset:d,relative:i,viewTransition:h});function p(y){r&&r(y),y.defaultPrevented||g(y)}return N.createElement("a",Po({},f,{href:x||b,onClick:v||s?r:p,ref:n,target:l}))}),Ns=N.forwardRef(function(e,n){let{"aria-current":r="page",caseSensitive:i=!1,className:s="",end:o=!1,style:a,to:l,viewTransition:u,children:d}=e,h=Ap(e,dy),f=la(l,{relative:h.relative}),m=Et(),x=N.useContext($p),{navigator:v,basename:b}=N.useContext(Nn),g=x!=null&&Sy(f)&&u===!0,p=v.encodeLocation?v.encodeLocation(f).pathname:f.pathname,y=m.pathname,w=x&&x.navigation&&x.navigation.location?x.navigation.location.pathname:null;i||(y=y.toLowerCase(),w=w?w.toLowerCase():null,p=p.toLowerCase()),w&&b&&(w=Dr(w,b)||w);const _=p!=="/"&&p.endsWith("/")?p.length-1:p.length;let S=y===p||!o&&y.startsWith(p)&&y.charAt(_)==="/",k=w!=null&&(w===p||!o&&w.startsWith(p)&&w.charAt(p.length)==="/"),j={isActive:S,isPending:k,isTransitioning:g},E=S?r:void 0,C;typeof s=="function"?C=s(j):C=[s,S?"active":null,k?"pending":null,g?"transitioning":null].filter(Boolean).join(" ");let $=typeof a=="function"?a(j):a;return N.createElement(vy,Po({},h,{"aria-current":E,className:C,ref:n,style:$,to:l,viewTransition:u}),typeof d=="function"?d(j):d)});var ql;(function(t){t.UseScrollRestoration="useScrollRestoration",t.UseSubmit="useSubmit",t.UseSubmitFetcher="useSubmitFetcher",t.UseFetcher="useFetcher",t.useViewTransitionState="useViewTransitionState"})(ql||(ql={}));var Od;(function(t){t.UseFetcher="useFetcher",t.UseFetchers="useFetchers",t.UseScrollRestoration="useScrollRestoration"})(Od||(Od={}));function yy(t){let e=N.useContext(oa);return e||le(!1),e}function by(t,e){let{target:n,replace:r,state:i,preventScrollReset:s,relative:o,viewTransition:a}=e===void 0?{}:e,l=Jn(),u=Et(),d=la(t,{relative:o});return N.useCallback(h=>{if(ly(h,n)){h.preventDefault();let f=r!==void 0?r:Co(u)===Co(d);l(t,{replace:f,state:i,preventScrollReset:s,relative:o,viewTransition:a})}},[u,l,d,r,i,n,t,s,o,a])}function wy(t){let e=N.useRef(Gl(t)),n=N.useRef(!1),r=Et(),i=N.useMemo(()=>cy(r.search,n.current?null:e.current),[r.search]),s=Jn(),o=N.useCallback((a,l)=>{const u=Gl(typeof a=="function"?a(i):a);n.current=!0,s("?"+u,l)},[s,i]);return[i,o]}function Sy(t,e){e===void 0&&(e={});let n=N.useContext(fy);n==null&&le(!1);let{basename:r}=yy(ql.useViewTransitionState),i=la(t,{relative:e.relative});if(!n.isTransitioning)return!1;let s=Dr(n.currentLocation.pathname,r)||n.currentLocation.pathname,o=Dr(n.nextLocation.pathname,r)||n.nextLocation.pathname;return Kl(i.pathname,o)!=null||Kl(i.pathname,s)!=null}function Eo(t){"@babel/helpers - typeof";return Eo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Eo(t)}function Xn(t){if(t===null||t===!0||t===!1)return NaN;var e=Number(t);return isNaN(e)?e:e<0?Math.ceil(e):Math.floor(e)}function Le(t,e){if(e.length<t)throw new TypeError(t+" argument"+(t>1?"s":"")+" required, but only "+e.length+" present")}function Mt(t){Le(1,arguments);var e=Object.prototype.toString.call(t);return t instanceof Date||Eo(t)==="object"&&e==="[object Date]"?new Date(t.getTime()):typeof t=="number"||e==="[object Number]"?new Date(t):((typeof t=="string"||e==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}function _y(t,e){Le(2,arguments);var n=Mt(t).getTime(),r=Xn(e);return new Date(n+r)}var ky={};function ca(){return ky}function Ny(t){var e=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return e.setUTCFullYear(t.getFullYear()),t.getTime()-e.getTime()}function jy(t){return Le(1,arguments),t instanceof Date||Eo(t)==="object"&&Object.prototype.toString.call(t)==="[object Date]"}function Cy(t){if(Le(1,arguments),!jy(t)&&typeof t!="number")return!1;var e=Mt(t);return!isNaN(Number(e))}function Py(t,e){Le(2,arguments);var n=Xn(e);return _y(t,-n)}var Ey=864e5;function My(t){Le(1,arguments);var e=Mt(t),n=e.getTime();e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0);var r=e.getTime(),i=n-r;return Math.floor(i/Ey)+1}function Mo(t){Le(1,arguments);var e=1,n=Mt(t),r=n.getUTCDay(),i=(r<e?7:0)+r-e;return n.setUTCDate(n.getUTCDate()-i),n.setUTCHours(0,0,0,0),n}function Fp(t){Le(1,arguments);var e=Mt(t),n=e.getUTCFullYear(),r=new Date(0);r.setUTCFullYear(n+1,0,4),r.setUTCHours(0,0,0,0);var i=Mo(r),s=new Date(0);s.setUTCFullYear(n,0,4),s.setUTCHours(0,0,0,0);var o=Mo(s);return e.getTime()>=i.getTime()?n+1:e.getTime()>=o.getTime()?n:n-1}function $y(t){Le(1,arguments);var e=Fp(t),n=new Date(0);n.setUTCFullYear(e,0,4),n.setUTCHours(0,0,0,0);var r=Mo(n);return r}var Ty=6048e5;function Ry(t){Le(1,arguments);var e=Mt(t),n=Mo(e).getTime()-$y(e).getTime();return Math.round(n/Ty)+1}function $o(t,e){var n,r,i,s,o,a,l,u;Le(1,arguments);var d=ca(),h=Xn((n=(r=(i=(s=e==null?void 0:e.weekStartsOn)!==null&&s!==void 0?s:e==null||(o=e.locale)===null||o===void 0||(a=o.options)===null||a===void 0?void 0:a.weekStartsOn)!==null&&i!==void 0?i:d.weekStartsOn)!==null&&r!==void 0?r:(l=d.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.weekStartsOn)!==null&&n!==void 0?n:0);if(!(h>=0&&h<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var f=Mt(t),m=f.getUTCDay(),x=(m<h?7:0)+m-h;return f.setUTCDate(f.getUTCDate()-x),f.setUTCHours(0,0,0,0),f}function zp(t,e){var n,r,i,s,o,a,l,u;Le(1,arguments);var d=Mt(t),h=d.getUTCFullYear(),f=ca(),m=Xn((n=(r=(i=(s=e==null?void 0:e.firstWeekContainsDate)!==null&&s!==void 0?s:e==null||(o=e.locale)===null||o===void 0||(a=o.options)===null||a===void 0?void 0:a.firstWeekContainsDate)!==null&&i!==void 0?i:f.firstWeekContainsDate)!==null&&r!==void 0?r:(l=f.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&n!==void 0?n:1);if(!(m>=1&&m<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var x=new Date(0);x.setUTCFullYear(h+1,0,m),x.setUTCHours(0,0,0,0);var v=$o(x,e),b=new Date(0);b.setUTCFullYear(h,0,m),b.setUTCHours(0,0,0,0);var g=$o(b,e);return d.getTime()>=v.getTime()?h+1:d.getTime()>=g.getTime()?h:h-1}function Dy(t,e){var n,r,i,s,o,a,l,u;Le(1,arguments);var d=ca(),h=Xn((n=(r=(i=(s=e==null?void 0:e.firstWeekContainsDate)!==null&&s!==void 0?s:e==null||(o=e.locale)===null||o===void 0||(a=o.options)===null||a===void 0?void 0:a.firstWeekContainsDate)!==null&&i!==void 0?i:d.firstWeekContainsDate)!==null&&r!==void 0?r:(l=d.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&n!==void 0?n:1),f=zp(t,e),m=new Date(0);m.setUTCFullYear(f,0,h),m.setUTCHours(0,0,0,0);var x=$o(m,e);return x}var Oy=6048e5;function Ly(t,e){Le(1,arguments);var n=Mt(t),r=$o(n,e).getTime()-Dy(n,e).getTime();return Math.round(r/Oy)+1}function Y(t,e){for(var n=t<0?"-":"",r=Math.abs(t).toString();r.length<e;)r="0"+r;return n+r}var Qt={y:function(e,n){var r=e.getUTCFullYear(),i=r>0?r:1-r;return Y(n==="yy"?i%100:i,n.length)},M:function(e,n){var r=e.getUTCMonth();return n==="M"?String(r+1):Y(r+1,2)},d:function(e,n){return Y(e.getUTCDate(),n.length)},a:function(e,n){var r=e.getUTCHours()/12>=1?"pm":"am";switch(n){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];case"aaaa":default:return r==="am"?"a.m.":"p.m."}},h:function(e,n){return Y(e.getUTCHours()%12||12,n.length)},H:function(e,n){return Y(e.getUTCHours(),n.length)},m:function(e,n){return Y(e.getUTCMinutes(),n.length)},s:function(e,n){return Y(e.getUTCSeconds(),n.length)},S:function(e,n){var r=n.length,i=e.getUTCMilliseconds(),s=Math.floor(i*Math.pow(10,r-3));return Y(s,n.length)}},tr={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},Ay={G:function(e,n,r){var i=e.getUTCFullYear()>0?1:0;switch(n){case"G":case"GG":case"GGG":return r.era(i,{width:"abbreviated"});case"GGGGG":return r.era(i,{width:"narrow"});case"GGGG":default:return r.era(i,{width:"wide"})}},y:function(e,n,r){if(n==="yo"){var i=e.getUTCFullYear(),s=i>0?i:1-i;return r.ordinalNumber(s,{unit:"year"})}return Qt.y(e,n)},Y:function(e,n,r,i){var s=zp(e,i),o=s>0?s:1-s;if(n==="YY"){var a=o%100;return Y(a,2)}return n==="Yo"?r.ordinalNumber(o,{unit:"year"}):Y(o,n.length)},R:function(e,n){var r=Fp(e);return Y(r,n.length)},u:function(e,n){var r=e.getUTCFullYear();return Y(r,n.length)},Q:function(e,n,r){var i=Math.ceil((e.getUTCMonth()+1)/3);switch(n){case"Q":return String(i);case"QQ":return Y(i,2);case"Qo":return r.ordinalNumber(i,{unit:"quarter"});case"QQQ":return r.quarter(i,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(i,{width:"narrow",context:"formatting"});case"QQQQ":default:return r.quarter(i,{width:"wide",context:"formatting"})}},q:function(e,n,r){var i=Math.ceil((e.getUTCMonth()+1)/3);switch(n){case"q":return String(i);case"qq":return Y(i,2);case"qo":return r.ordinalNumber(i,{unit:"quarter"});case"qqq":return r.quarter(i,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(i,{width:"narrow",context:"standalone"});case"qqqq":default:return r.quarter(i,{width:"wide",context:"standalone"})}},M:function(e,n,r){var i=e.getUTCMonth();switch(n){case"M":case"MM":return Qt.M(e,n);case"Mo":return r.ordinalNumber(i+1,{unit:"month"});case"MMM":return r.month(i,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(i,{width:"narrow",context:"formatting"});case"MMMM":default:return r.month(i,{width:"wide",context:"formatting"})}},L:function(e,n,r){var i=e.getUTCMonth();switch(n){case"L":return String(i+1);case"LL":return Y(i+1,2);case"Lo":return r.ordinalNumber(i+1,{unit:"month"});case"LLL":return r.month(i,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(i,{width:"narrow",context:"standalone"});case"LLLL":default:return r.month(i,{width:"wide",context:"standalone"})}},w:function(e,n,r,i){var s=Ly(e,i);return n==="wo"?r.ordinalNumber(s,{unit:"week"}):Y(s,n.length)},I:function(e,n,r){var i=Ry(e);return n==="Io"?r.ordinalNumber(i,{unit:"week"}):Y(i,n.length)},d:function(e,n,r){return n==="do"?r.ordinalNumber(e.getUTCDate(),{unit:"date"}):Qt.d(e,n)},D:function(e,n,r){var i=My(e);return n==="Do"?r.ordinalNumber(i,{unit:"dayOfYear"}):Y(i,n.length)},E:function(e,n,r){var i=e.getUTCDay();switch(n){case"E":case"EE":case"EEE":return r.day(i,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(i,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(i,{width:"short",context:"formatting"});case"EEEE":default:return r.day(i,{width:"wide",context:"formatting"})}},e:function(e,n,r,i){var s=e.getUTCDay(),o=(s-i.weekStartsOn+8)%7||7;switch(n){case"e":return String(o);case"ee":return Y(o,2);case"eo":return r.ordinalNumber(o,{unit:"day"});case"eee":return r.day(s,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(s,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(s,{width:"short",context:"formatting"});case"eeee":default:return r.day(s,{width:"wide",context:"formatting"})}},c:function(e,n,r,i){var s=e.getUTCDay(),o=(s-i.weekStartsOn+8)%7||7;switch(n){case"c":return String(o);case"cc":return Y(o,n.length);case"co":return r.ordinalNumber(o,{unit:"day"});case"ccc":return r.day(s,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(s,{width:"narrow",context:"standalone"});case"cccccc":return r.day(s,{width:"short",context:"standalone"});case"cccc":default:return r.day(s,{width:"wide",context:"standalone"})}},i:function(e,n,r){var i=e.getUTCDay(),s=i===0?7:i;switch(n){case"i":return String(s);case"ii":return Y(s,n.length);case"io":return r.ordinalNumber(s,{unit:"day"});case"iii":return r.day(i,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(i,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(i,{width:"short",context:"formatting"});case"iiii":default:return r.day(i,{width:"wide",context:"formatting"})}},a:function(e,n,r){var i=e.getUTCHours(),s=i/12>=1?"pm":"am";switch(n){case"a":case"aa":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(s,{width:"narrow",context:"formatting"});case"aaaa":default:return r.dayPeriod(s,{width:"wide",context:"formatting"})}},b:function(e,n,r){var i=e.getUTCHours(),s;switch(i===12?s=tr.noon:i===0?s=tr.midnight:s=i/12>=1?"pm":"am",n){case"b":case"bb":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(s,{width:"narrow",context:"formatting"});case"bbbb":default:return r.dayPeriod(s,{width:"wide",context:"formatting"})}},B:function(e,n,r){var i=e.getUTCHours(),s;switch(i>=17?s=tr.evening:i>=12?s=tr.afternoon:i>=4?s=tr.morning:s=tr.night,n){case"B":case"BB":case"BBB":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(s,{width:"narrow",context:"formatting"});case"BBBB":default:return r.dayPeriod(s,{width:"wide",context:"formatting"})}},h:function(e,n,r){if(n==="ho"){var i=e.getUTCHours()%12;return i===0&&(i=12),r.ordinalNumber(i,{unit:"hour"})}return Qt.h(e,n)},H:function(e,n,r){return n==="Ho"?r.ordinalNumber(e.getUTCHours(),{unit:"hour"}):Qt.H(e,n)},K:function(e,n,r){var i=e.getUTCHours()%12;return n==="Ko"?r.ordinalNumber(i,{unit:"hour"}):Y(i,n.length)},k:function(e,n,r){var i=e.getUTCHours();return i===0&&(i=24),n==="ko"?r.ordinalNumber(i,{unit:"hour"}):Y(i,n.length)},m:function(e,n,r){return n==="mo"?r.ordinalNumber(e.getUTCMinutes(),{unit:"minute"}):Qt.m(e,n)},s:function(e,n,r){return n==="so"?r.ordinalNumber(e.getUTCSeconds(),{unit:"second"}):Qt.s(e,n)},S:function(e,n){return Qt.S(e,n)},X:function(e,n,r,i){var s=i._originalDate||e,o=s.getTimezoneOffset();if(o===0)return"Z";switch(n){case"X":return Ad(o);case"XXXX":case"XX":return Rn(o);case"XXXXX":case"XXX":default:return Rn(o,":")}},x:function(e,n,r,i){var s=i._originalDate||e,o=s.getTimezoneOffset();switch(n){case"x":return Ad(o);case"xxxx":case"xx":return Rn(o);case"xxxxx":case"xxx":default:return Rn(o,":")}},O:function(e,n,r,i){var s=i._originalDate||e,o=s.getTimezoneOffset();switch(n){case"O":case"OO":case"OOO":return"GMT"+Ld(o,":");case"OOOO":default:return"GMT"+Rn(o,":")}},z:function(e,n,r,i){var s=i._originalDate||e,o=s.getTimezoneOffset();switch(n){case"z":case"zz":case"zzz":return"GMT"+Ld(o,":");case"zzzz":default:return"GMT"+Rn(o,":")}},t:function(e,n,r,i){var s=i._originalDate||e,o=Math.floor(s.getTime()/1e3);return Y(o,n.length)},T:function(e,n,r,i){var s=i._originalDate||e,o=s.getTime();return Y(o,n.length)}};function Ld(t,e){var n=t>0?"-":"+",r=Math.abs(t),i=Math.floor(r/60),s=r%60;if(s===0)return n+String(i);var o=e;return n+String(i)+o+Y(s,2)}function Ad(t,e){if(t%60===0){var n=t>0?"-":"+";return n+Y(Math.abs(t)/60,2)}return Rn(t,e)}function Rn(t,e){var n=e||"",r=t>0?"-":"+",i=Math.abs(t),s=Y(Math.floor(i/60),2),o=Y(i%60,2);return r+s+n+o}var Fd=function(e,n){switch(e){case"P":return n.date({width:"short"});case"PP":return n.date({width:"medium"});case"PPP":return n.date({width:"long"});case"PPPP":default:return n.date({width:"full"})}},Ip=function(e,n){switch(e){case"p":return n.time({width:"short"});case"pp":return n.time({width:"medium"});case"ppp":return n.time({width:"long"});case"pppp":default:return n.time({width:"full"})}},Fy=function(e,n){var r=e.match(/(P+)(p+)?/)||[],i=r[1],s=r[2];if(!s)return Fd(e,n);var o;switch(i){case"P":o=n.dateTime({width:"short"});break;case"PP":o=n.dateTime({width:"medium"});break;case"PPP":o=n.dateTime({width:"long"});break;case"PPPP":default:o=n.dateTime({width:"full"});break}return o.replace("{{date}}",Fd(i,n)).replace("{{time}}",Ip(s,n))},zy={p:Ip,P:Fy},Iy=["D","DD"],Uy=["YY","YYYY"];function By(t){return Iy.indexOf(t)!==-1}function Hy(t){return Uy.indexOf(t)!==-1}function zd(t,e,n){if(t==="YYYY")throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="YY")throw new RangeError("Use `yy` instead of `YY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="D")throw new RangeError("Use `d` instead of `D` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="DD")throw new RangeError("Use `dd` instead of `DD` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var Wy={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Vy=function(e,n,r){var i,s=Wy[e];return typeof s=="string"?i=s:n===1?i=s.one:i=s.other.replace("{{count}}",n.toString()),r!=null&&r.addSuffix?r.comparison&&r.comparison>0?"in "+i:i+" ago":i};function Ia(t){return function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=e.width?String(e.width):t.defaultWidth,r=t.formats[n]||t.formats[t.defaultWidth];return r}}var Yy={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Xy={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Ky={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Qy={date:Ia({formats:Yy,defaultWidth:"full"}),time:Ia({formats:Xy,defaultWidth:"full"}),dateTime:Ia({formats:Ky,defaultWidth:"full"})},Gy={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},qy=function(e,n,r,i){return Gy[e]};function Jr(t){return function(e,n){var r=n!=null&&n.context?String(n.context):"standalone",i;if(r==="formatting"&&t.formattingValues){var s=t.defaultFormattingWidth||t.defaultWidth,o=n!=null&&n.width?String(n.width):s;i=t.formattingValues[o]||t.formattingValues[s]}else{var a=t.defaultWidth,l=n!=null&&n.width?String(n.width):t.defaultWidth;i=t.values[l]||t.values[a]}var u=t.argumentCallback?t.argumentCallback(e):e;return i[u]}}var Zy={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Jy={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},e1={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},t1={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},n1={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},r1={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},i1=function(e,n){var r=Number(e),i=r%100;if(i>20||i<10)switch(i%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},s1={ordinalNumber:i1,era:Jr({values:Zy,defaultWidth:"wide"}),quarter:Jr({values:Jy,defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:Jr({values:e1,defaultWidth:"wide"}),day:Jr({values:t1,defaultWidth:"wide"}),dayPeriod:Jr({values:n1,defaultWidth:"wide",formattingValues:r1,defaultFormattingWidth:"wide"})};function ei(t){return function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=n.width,i=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],s=e.match(i);if(!s)return null;var o=s[0],a=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],l=Array.isArray(a)?a1(a,function(h){return h.test(o)}):o1(a,function(h){return h.test(o)}),u;u=t.valueCallback?t.valueCallback(l):l,u=n.valueCallback?n.valueCallback(u):u;var d=e.slice(o.length);return{value:u,rest:d}}}function o1(t,e){for(var n in t)if(t.hasOwnProperty(n)&&e(t[n]))return n}function a1(t,e){for(var n=0;n<t.length;n++)if(e(t[n]))return n}function l1(t){return function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=e.match(t.matchPattern);if(!r)return null;var i=r[0],s=e.match(t.parsePattern);if(!s)return null;var o=t.valueCallback?t.valueCallback(s[0]):s[0];o=n.valueCallback?n.valueCallback(o):o;var a=e.slice(i.length);return{value:o,rest:a}}}var c1=/^(\d+)(th|st|nd|rd)?/i,u1=/\d+/i,d1={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},h1={any:[/^b/i,/^(a|c)/i]},f1={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},m1={any:[/1/i,/2/i,/3/i,/4/i]},p1={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},g1={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},x1={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},v1={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},y1={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},b1={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},w1={ordinalNumber:l1({matchPattern:c1,parsePattern:u1,valueCallback:function(e){return parseInt(e,10)}}),era:ei({matchPatterns:d1,defaultMatchWidth:"wide",parsePatterns:h1,defaultParseWidth:"any"}),quarter:ei({matchPatterns:f1,defaultMatchWidth:"wide",parsePatterns:m1,defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:ei({matchPatterns:p1,defaultMatchWidth:"wide",parsePatterns:g1,defaultParseWidth:"any"}),day:ei({matchPatterns:x1,defaultMatchWidth:"wide",parsePatterns:v1,defaultParseWidth:"any"}),dayPeriod:ei({matchPatterns:y1,defaultMatchWidth:"any",parsePatterns:b1,defaultParseWidth:"any"})},S1={code:"en-US",formatDistance:Vy,formatLong:Qy,formatRelative:qy,localize:s1,match:w1,options:{weekStartsOn:0,firstWeekContainsDate:1}},_1=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,k1=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,N1=/^'([^]*?)'?$/,j1=/''/g,C1=/[a-zA-Z]/;function Id(t,e,n){var r,i,s,o,a,l,u,d,h,f,m,x,v,b;Le(2,arguments);var g=String(e),p=ca(),y=(r=(i=void 0)!==null&&i!==void 0?i:p.locale)!==null&&r!==void 0?r:S1,w=Xn((s=(o=(a=(l=void 0)!==null&&l!==void 0?l:void 0)!==null&&a!==void 0?a:p.firstWeekContainsDate)!==null&&o!==void 0?o:(u=p.locale)===null||u===void 0||(d=u.options)===null||d===void 0?void 0:d.firstWeekContainsDate)!==null&&s!==void 0?s:1);if(!(w>=1&&w<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var _=Xn((h=(f=(m=(x=void 0)!==null&&x!==void 0?x:void 0)!==null&&m!==void 0?m:p.weekStartsOn)!==null&&f!==void 0?f:(v=p.locale)===null||v===void 0||(b=v.options)===null||b===void 0?void 0:b.weekStartsOn)!==null&&h!==void 0?h:0);if(!(_>=0&&_<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!y.localize)throw new RangeError("locale must contain localize property");if(!y.formatLong)throw new RangeError("locale must contain formatLong property");var S=Mt(t);if(!Cy(S))throw new RangeError("Invalid time value");var k=Ny(S),j=Py(S,k),E={firstWeekContainsDate:w,weekStartsOn:_,locale:y,_originalDate:S},C=g.match(k1).map(function($){var D=$[0];if(D==="p"||D==="P"){var T=zy[D];return T($,y.formatLong)}return $}).join("").match(_1).map(function($){if($==="''")return"'";var D=$[0];if(D==="'")return P1($);var T=Ay[D];if(T)return Hy($)&&zd($,e,String(t)),By($)&&zd($,e,String(t)),T(j,$,y.localize,E);if(D.match(C1))throw new RangeError("Format string contains an unescaped latin alphabet character `"+D+"`");return $}).join("");return C}function P1(t){var e=t.match(N1);return e?e[1].replace(j1,"'"):t}const Up=N.createContext(),Zl={version:"1.1.0",currentUnits:0,previousUnits:0,unitCost:0,thresholdLimit:0,currency:"ZAR",currencySymbol:"R",customCurrencyName:"",customCurrencySymbol:"",unitName:"kWh",customUnitName:"",purchases:[],usageHistory:[],isInitialized:!1,lastResetDate:null,lastMonthlyReset:null,notificationsEnabled:!1,notificationTime:"18:00",lastNotificationDate:null};function E1(t,e){switch(e.type){case"INITIALIZE_APP":return{...t,currentUnits:e.payload.initialUnits,previousUnits:e.payload.initialUnits,unitCost:e.payload.unitCost,isInitialized:!0,lastResetDate:new Date().toISOString()};case"ADD_PURCHASE":const n={id:Date.now(),date:new Date().toISOString(),currency:e.payload.currency,units:e.payload.units,unitCost:t.unitCost,timestamp:Id(new Date,"yyyy-MM-dd HH:mm:ss")};return{...t,purchases:[n,...t.purchases],currentUnits:t.currentUnits+e.payload.units};case"UPDATE_USAGE":const r={id:Date.now(),date:new Date().toISOString(),previousUnits:t.currentUnits,currentUnits:e.payload.currentUnits,usage:t.currentUnits-e.payload.currentUnits,timestamp:Id(new Date,"yyyy-MM-dd HH:mm:ss")};return{...t,previousUnits:t.currentUnits,currentUnits:e.payload.currentUnits,usageHistory:[r,...t.usageHistory]};case"UPDATE_SETTINGS":return{...t,...e.payload};case"FACTORY_RESET":return{...Zl};case"DASHBOARD_RESET":return{...t,currentUnits:0,previousUnits:0,lastResetDate:new Date().toISOString()};case"MONTHLY_RESET":return{...t,usageHistory:[],lastMonthlyReset:new Date().toISOString()};case"LOAD_STATE":return{...t,...e.payload};default:return t}}function M1({children:t}){const[e,n]=N.useReducer(E1,Zl);N.useEffect(()=>{const S="prepaid-meter-app-v1.1",k=localStorage.getItem(S);if(localStorage.removeItem("prepaid-meter-app"),k)try{const j=JSON.parse(k);if(!j.version||j.version!==Zl.version){console.log("Version mismatch detected, clearing old data"),localStorage.removeItem(S);return}n({type:"LOAD_STATE",payload:j})}catch(j){console.error("Error loading saved state:",j),localStorage.removeItem(S)}},[]),N.useEffect(()=>{localStorage.setItem("prepaid-meter-app-v1.1",JSON.stringify(e))},[e]),N.useEffect(()=>{const S=new Date,k=S.getMonth(),j=S.getFullYear();if(e.lastMonthlyReset){const E=new Date(e.lastMonthlyReset),C=E.getMonth(),$=E.getFullYear();(j>$||j===$&&k>C)&&n({type:"MONTHLY_RESET"})}else e.isInitialized&&n({type:"UPDATE_SETTINGS",payload:{lastMonthlyReset:S.toISOString()}})},[e.lastMonthlyReset,e.isInitialized]),N.useEffect(()=>{if(!e.notificationsEnabled||!e.isInitialized)return;const S=()=>{const j=new Date,[E,C]=e.notificationTime.split(":").map(Number),$=new Date;$.setHours(E,C,0,0);const D=j.toDateString(),T=e.lastNotificationDate?new Date(e.lastNotificationDate).toDateString():null;j>=$&&T!==D&&("Notification"in window&&Notification.permission==="default"&&Notification.requestPermission(),"Notification"in window&&Notification.permission==="granted"&&(new Notification("⚡ Prepaid Meter Reminder",{body:"Don't forget to record your electricity usage today!",icon:"/favicon.ico",badge:"/favicon.ico"}),n({type:"UPDATE_SETTINGS",payload:{lastNotificationDate:j.toISOString()}})))};S();const k=setInterval(S,6e4);return()=>clearInterval(k)},[e.notificationsEnabled,e.notificationTime,e.lastNotificationDate,e.isInitialized]);const r=e.previousUnits-e.currentUnits,i=e.currentUnits<=e.thresholdLimit&&e.thresholdLimit>0,s=e.purchases.reduce((S,k)=>S+k.currency,0),o=e.usageHistory.reduce((S,k)=>S+k.usage,0),a=new Date,l=new Date(a.getFullYear(),a.getMonth(),a.getDate()-a.getDay()),u=new Date(a.getFullYear(),a.getMonth(),1),d=e.purchases.filter(S=>new Date(S.date)>=l),h=e.purchases.filter(S=>new Date(S.date)>=u),f=e.usageHistory.filter(S=>new Date(S.date)>=l),m=e.usageHistory.filter(S=>new Date(S.date)>=u),x=d.reduce((S,k)=>S+k.currency,0),v=h.reduce((S,k)=>S+k.currency,0),b=f.reduce((S,k)=>S+k.usage,0),g=m.reduce((S,k)=>S+k.usage,0),_={state:e,dispatch:n,initializeApp:(S,k)=>{n({type:"INITIALIZE_APP",payload:{initialUnits:S,unitCost:k}})},addPurchase:(S,k)=>{n({type:"ADD_PURCHASE",payload:{currency:S,units:k}})},updateUsage:S=>{n({type:"UPDATE_USAGE",payload:{currentUnits:S}})},updateSettings:S=>{n({type:"UPDATE_SETTINGS",payload:S})},factoryReset:()=>{n({type:"FACTORY_RESET"})},dashboardReset:()=>{n({type:"DASHBOARD_RESET"})},usageSinceLastRecording:r,isThresholdExceeded:i,totalPurchases:s,totalUnitsUsed:o,getDisplayUnitName:()=>e.unitName==="custom"?e.customUnitName||"Units":e.unitName,getDisplayCurrencySymbol:()=>e.currency==="CUSTOM"?e.customCurrencySymbol||"C":e.currencySymbol||"R",getDisplayCurrencyName:()=>{var k;return e.currency==="CUSTOM"?e.customCurrencyName||"Custom Currency":((k=[{code:"ZAR",name:"South African Rand"},{code:"USD",name:"US Dollar"},{code:"EUR",name:"Euro"},{code:"GBP",name:"British Pound"},{code:"JPY",name:"Japanese Yen"}].find(j=>j.code===e.currency))==null?void 0:k.name)||"Unknown Currency"},weeklyPurchaseTotal:x,monthlyPurchaseTotal:v,weeklyUsageTotal:b,monthlyUsageTotal:g,weeklyPurchases:d,monthlyPurchases:h,weeklyUsage:f,monthlyUsage:m};return c.jsx(Up.Provider,{value:_,children:t})}function Xe(){const t=N.useContext(Up);if(!t)throw new Error("useApp must be used within an AppProvider");return t}const Bp=N.createContext(),mt={electric:{name:"Electric Blue",primary:"bg-blue-600",secondary:"bg-blue-50",accent:"bg-blue-500",background:"bg-blue-25",text:"text-blue-900",textSecondary:"text-blue-700",border:"border-blue-200",card:"bg-blue-50",gradient:"from-blue-500 to-blue-700",light:"bg-blue-100",lighter:"bg-blue-50",dark:"bg-blue-700",darker:"bg-blue-800"},dark:{name:"Dark Mode",primary:"bg-gray-600",secondary:"bg-gray-700",accent:"bg-gray-500",background:"bg-gray-900",text:"text-white",textSecondary:"text-gray-300",border:"border-gray-600",card:"bg-gray-800",gradient:"from-gray-600 to-gray-800",light:"bg-gray-700",lighter:"bg-gray-600",dark:"bg-gray-800",darker:"bg-gray-900"},green:{name:"Eco Green",primary:"bg-green-600",secondary:"bg-green-50",accent:"bg-green-500",background:"bg-green-25",text:"text-green-900",textSecondary:"text-green-700",border:"border-green-200",card:"bg-green-50",gradient:"from-green-500 to-green-700",light:"bg-green-100",lighter:"bg-green-50",dark:"bg-green-700",darker:"bg-green-800"},teal:{name:"Ocean Teal",primary:"bg-teal-600",secondary:"bg-teal-50",accent:"bg-teal-500",background:"bg-teal-25",text:"text-teal-900",textSecondary:"text-teal-700",border:"border-teal-200",card:"bg-teal-50",gradient:"from-teal-500 to-teal-700",light:"bg-teal-100",lighter:"bg-teal-50",dark:"bg-teal-700",darker:"bg-teal-800"},pink:{name:"Rose Pink",primary:"bg-pink-600",secondary:"bg-pink-50",accent:"bg-pink-500",background:"bg-pink-25",text:"text-pink-900",textSecondary:"text-pink-700",border:"border-pink-200",card:"bg-pink-50",gradient:"from-pink-500 to-pink-700",light:"bg-pink-100",lighter:"bg-pink-50",dark:"bg-pink-700",darker:"bg-pink-800"}};function $1({children:t}){const[e,n]=N.useState("electric");N.useEffect(()=>{const i=localStorage.getItem("prepaid-meter-theme");i&&mt[i]&&n(i)},[]),N.useEffect(()=>{localStorage.setItem("prepaid-meter-theme",e)},[e]);const r={currentTheme:e,setCurrentTheme:n,theme:mt[e],themes:mt};return c.jsx(Bp.Provider,{value:r,children:c.jsx("div",{className:`${mt[e].background} ${mt[e].text} text-base min-h-screen transition-all duration-300`,style:{fontFamily:"Inter, system-ui, -apple-system, sans-serif",fontSize:"16px"},children:t})})}function me(){const t=N.useContext(Bp);if(!t)throw new Error("useTheme must be used within a ThemeProvider");return t}var Hp={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},Ud=pt.createContext&&pt.createContext(Hp),gn=function(){return gn=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++){e=arguments[n];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])}return t},gn.apply(this,arguments)},T1=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]]);return n};function Wp(t){return t&&t.map(function(e,n){return pt.createElement(e.tag,gn({key:n},e.attr),Wp(e.child))})}function ne(t){return function(e){return pt.createElement(R1,gn({attr:gn({},t.attr)},e),Wp(t.child))}}function R1(t){var e=function(n){var r=t.attr,i=t.size,s=t.title,o=T1(t,["attr","size","title"]),a=i||n.size||"1em",l;return n.className&&(l=n.className),t.className&&(l=(l?l+" ":"")+t.className),pt.createElement("svg",gn({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},n.attr,r,o,{className:l,style:gn(gn({color:t.color||n.color},n.style),t.style),height:a,width:a,xmlns:"http://www.w3.org/2000/svg"}),s&&pt.createElement("title",null,s),t.children)};return Ud!==void 0?pt.createElement(Ud.Consumer,null,function(n){return e(n)}):e(Hp)}function D1(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z",clipRule:"evenodd"}}]})(t)}function O1(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z",clipRule:"evenodd"}}]})(t)}function Vp(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13zM9 9a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zM7 8a1 1 0 000 2h.01a1 1 0 000-2H7z",clipRule:"evenodd"}}]})(t)}function To(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",clipRule:"evenodd"}}]})(t)}function ru(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"}}]})(t)}function L1(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"}}]})(t)}function Jl(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(t)}function Bd(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"}}]})(t)}function Ro(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z",clipRule:"evenodd"}}]})(t)}function Hd(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"}}]})(t)}function Yi(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"}}]})(t)}function Yp(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z",clipRule:"evenodd"}}]})(t)}function Ve(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z",clipRule:"evenodd"}}]})(t)}function _r(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"}}]})(t)}function A1(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z",clipRule:"evenodd"}}]})(t)}function F1(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z",clipRule:"evenodd"}}]})(t)}function Xp(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"}}]})(t)}function Ye(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z",clipRule:"evenodd"}}]})(t)}function z1(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"}}]})(t)}function Kp(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"}}]})(t)}function I1(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"}}]})(t)}function U1(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z",clipRule:"evenodd"}}]})(t)}function B1(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z",clipRule:"evenodd"}}]})(t)}function jt(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z",clipRule:"evenodd"}}]})(t)}function H1(t){return ne({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(t)}function W1({size:t="md",animated:e=!0,showText:n=!0}){const{theme:r}=me(),[i,s]=N.useState(!1),o={sm:{logo:"h-12 w-12",text:"text-sm",icon:"h-6 w-6"},md:{logo:"h-16 w-16",text:"text-lg",icon:"h-8 w-8"},lg:{logo:"h-20 w-20",text:"text-xl",icon:"h-10 w-10"},xl:{logo:"h-24 w-24",text:"text-2xl",icon:"h-12 w-12"}},a=o[t]||o.md;return c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx("div",{className:`${a.logo} flex items-center justify-center`,children:i?c.jsx("div",{className:`${a.logo} rounded-2xl bg-gradient-to-br ${r.gradient} shadow-lg flex items-center justify-center ${e?"animate-pulse":""}`,children:c.jsx(Ye,{className:`${a.icon} text-white`})}):c.jsx("img",{src:"/oie_transparent (1).png",alt:"Prepaid User Electricity Logo",className:`${a.logo} object-contain`,onError:()=>s(!0),onLoad:()=>s(!1)})}),n&&c.jsxs("div",{className:"flex flex-col",children:[c.jsx("h1",{className:`${a.text} font-black ${r.text} leading-tight`,children:"Prepaid User"}),c.jsx("p",{className:`text-base font-bold ${r.textSecondary} tracking-wider leading-tight`,children:"Electricity"})]})]})}function V1({onMenuClick:t}){const{theme:e,currentTheme:n}=me(),{state:r}=Xe(),i=()=>n==="dark"?"bg-gray-900/95 backdrop-blur-xl border-gray-700/50":{electric:"bg-blue-500/90 backdrop-blur-xl border-white/30",green:"bg-green-500/90 backdrop-blur-xl border-white/30",teal:"bg-teal-500/90 backdrop-blur-xl border-white/30",pink:"bg-pink-500/90 backdrop-blur-xl border-white/30"}[n]||"bg-blue-500/90 backdrop-blur-xl border-white/30",s=()=>n==="dark"?e.text:"text-white drop-shadow-sm";return c.jsxs("header",{className:`${i()} border-b px-4 py-0.5 flex items-center justify-between shadow-xl`,children:[c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx("button",{onClick:t,className:"hamburger-menu lg:hidden p-1.5 rounded-lg bg-white/15 backdrop-blur-sm text-white hover:bg-white/25 transition-all duration-300 shadow-lg",children:c.jsx(z1,{className:"h-6 w-6"})}),c.jsx("div",{className:"flex items-center",children:c.jsx(W1,{size:"sm",animated:!0,showText:!1})})]}),c.jsxs("div",{className:"flex items-center space-x-1.5 bg-black/30 backdrop-blur-md rounded-lg px-2 py-0.5 border border-white/30 shadow-lg",children:[c.jsxs("div",{className:"text-right",children:[c.jsx("p",{className:`text-xs ${s()}`,children:"Current Units"}),c.jsx("p",{className:`text-sm font-bold ${s()}`,children:r.currentUnits.toFixed(2)})]}),c.jsx("div",{className:"w-1.5 h-1.5 rounded-full bg-white/80 pulse-glow shadow-sm"})]})]})}const Wd=[{name:"Dashboard",href:"/dashboard",icon:Xp},{name:"Purchases",href:"/purchases",icon:I1},{name:"Usage",href:"/usage",icon:ru},{name:"History",href:"/history",icon:Ro}],Vd=[{name:"General Settings",href:"/settings?section=general",icon:Yi},{name:"Appearance",href:"/settings?section=appearance",icon:Yp},{name:"Reset Options",href:"/settings?section=reset",icon:Kp}];function Y1({isOpen:t,onClose:e}){const{theme:n,currentTheme:r}=me(),i=Et(),[s,o]=N.useState(!1),a=()=>r==="dark"?"bg-gray-900/95 backdrop-blur-xl border-gray-700/50":{electric:"bg-blue-500/90 backdrop-blur-xl border-white/30",green:"bg-green-500/90 backdrop-blur-xl border-white/30",teal:"bg-teal-500/90 backdrop-blur-xl border-white/30",pink:"bg-pink-500/90 backdrop-blur-xl border-white/30"}[r]||"bg-blue-500/90 backdrop-blur-xl border-white/30",l=()=>r==="dark"?n.text:"text-white drop-shadow-sm",u=()=>r==="dark"?"bg-white/15 backdrop-blur-sm":"bg-white/25 backdrop-blur-sm",d=()=>r==="dark"?"bg-white/20 backdrop-blur-md":"bg-white/35 backdrop-blur-md";return c.jsxs(c.Fragment,{children:[c.jsx("div",{className:`hidden lg:flex lg:flex-shrink-0 lg:w-64 ${a()} border-r shadow-2xl`,children:c.jsxs("div",{className:"flex flex-col w-full",children:[c.jsx("div",{className:"flex items-center justify-center h-16 px-4 border-b border-white/15",children:c.jsx("h2",{className:`text-xl font-bold ${l()} tracking-wider drop-shadow-sm`,children:"MENU"})}),c.jsxs("nav",{className:"flex-1 px-4 py-6 space-y-2",children:[Wd.map(h=>c.jsxs(Ns,{to:h.href,className:({isActive:f})=>`flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${f?`${d()} ${l()} shadow-lg border border-white/30`:`${l()} hover:${u()} hover:shadow-md hover:border hover:border-white/20`}`,children:[c.jsx(h.icon,{className:"mr-3 h-5 w-5"}),h.name]},h.name)),c.jsxs("div",{className:"space-y-1",children:[c.jsxs("button",{onClick:()=>o(!s),className:`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${i.pathname.includes("/settings")?`${d()} ${l()} shadow-lg border border-white/30`:`${l()} hover:${u()} hover:shadow-md hover:border hover:border-white/20`}`,children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx(Yi,{className:"mr-3 h-5 w-5"}),"Settings"]}),s?c.jsx(Jl,{className:"h-4 w-4"}):c.jsx(Bd,{className:"h-4 w-4"})]}),s&&c.jsx("div",{className:"ml-4 space-y-1 mt-2",children:Vd.map(h=>c.jsxs(Ns,{to:h.href,className:({isActive:f})=>`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${f?`bg-white/30 backdrop-blur-sm ${l()} shadow-md border border-white/25`:`${l()} hover:bg-white/20 hover:backdrop-blur-sm hover:shadow-sm`}`,children:[c.jsx(h.icon,{className:"mr-3 h-4 w-4"}),h.name]},h.name))})]})]})]})}),t&&c.jsx("div",{className:`sidebar-container lg:hidden fixed inset-y-0 left-0 z-50 w-64 ${a()} transform transition-transform duration-300 ease-in-out shadow-2xl translate-x-0`,style:{paddingTop:"env(safe-area-inset-top, 0px)"},children:c.jsxs("div",{className:"flex flex-col h-full",children:[c.jsxs("div",{className:"flex items-center justify-between px-4 border-b border-white/15",style:{minHeight:"calc(4rem + env(safe-area-inset-top, 0px))",paddingTop:"calc(env(safe-area-inset-top, 0px) + 1rem)",paddingBottom:"1rem"},children:[c.jsx("div",{className:"flex items-center",children:c.jsx("h2",{className:`text-xl font-bold ${l()} tracking-wider drop-shadow-sm`,children:"MENU"})}),c.jsx("button",{onClick:e,className:`p-2 rounded-xl ${l()} hover:bg-white/15 hover:backdrop-blur-sm transition-all duration-300 shadow-sm`,children:c.jsx(H1,{className:"h-6 w-6"})})]}),c.jsxs("nav",{className:"flex-1 px-4 py-6 space-y-2",children:[Wd.map(h=>c.jsxs(Ns,{to:h.href,onClick:e,className:({isActive:f})=>`flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${f?`${d()} ${l()} shadow-lg border border-white/30`:`${l()} hover:${u()} hover:shadow-md hover:border hover:border-white/20`}`,children:[c.jsx(h.icon,{className:"mr-3 h-5 w-5"}),h.name]},h.name)),c.jsxs("div",{className:"space-y-1",children:[c.jsxs("button",{onClick:()=>o(!s),className:`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${i.pathname.includes("/settings")?`${d()} ${l()} shadow-lg border border-white/30`:`${l()} hover:${u()} hover:shadow-md hover:border hover:border-white/20`}`,children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx(Yi,{className:"mr-3 h-5 w-5"}),"Settings"]}),s?c.jsx(Jl,{className:"h-4 w-4"}):c.jsx(Bd,{className:"h-4 w-4"})]}),s&&c.jsx("div",{className:"ml-4 space-y-1 mt-2",children:Vd.map(h=>c.jsxs(Ns,{to:h.href,onClick:e,className:({isActive:f})=>`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${f?`bg-white/30 backdrop-blur-sm ${l()} shadow-md border border-white/25`:`${l()} hover:bg-white/20 hover:backdrop-blur-sm hover:shadow-sm`}`,children:[c.jsx(h.icon,{className:"mr-3 h-4 w-4"}),h.name]},h.name))})]})]})]})})]})}function X1(){const t=Et(),e=Jn(),{theme:n,currentTheme:r}=me(),i=()=>r==="dark"?"bg-gray-900/95 backdrop-blur-xl border-gray-700/50":{electric:"bg-blue-500/90 backdrop-blur-xl border-white/30",green:"bg-green-500/90 backdrop-blur-xl border-white/30",teal:"bg-teal-500/90 backdrop-blur-xl border-white/30",pink:"bg-pink-500/90 backdrop-blur-xl border-white/30"}[r]||"bg-blue-500/90 backdrop-blur-xl border-white/30",s=[{id:"dashboard",path:"/",icon:Xp,label:"Dashboard"},{id:"purchases",path:"/purchases",icon:Ve,label:"Purchases"},{id:"usage",path:"/usage",icon:jt,label:"Usage"},{id:"history",path:"/history",icon:Ro,label:"History"}],o=a=>a==="/"?t.pathname==="/"||t.pathname==="/dashboard":t.pathname===a;return c.jsx("div",{className:`md:hidden ${i()} border-t shadow-xl`,children:c.jsx("div",{className:"flex items-center justify-around px-1 py-0.5",children:s.map(a=>{const l=a.icon,u=o(a.path);return c.jsxs("button",{onClick:()=>e(a.path),className:`flex flex-col items-center justify-center p-1 rounded-lg transition-all duration-200 min-w-0 flex-1 ${u?"bg-white/20 text-white shadow-lg transform scale-105 border border-white/30":"text-white/80 hover:bg-white/10 hover:text-white"}`,children:[c.jsx(l,{className:`h-3.5 w-3.5 ${u?"text-white":"text-white/80"}`}),c.jsx("span",{className:`text-xs font-medium mt-0.5 truncate ${u?"text-white":"text-white/80"}`,children:a.label})]},a.id)})})})}/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function is(t){return t+.5|0}const sn=(t,e,n)=>Math.max(Math.min(t,n),e);function ui(t){return sn(is(t*2.55),0,255)}function xn(t){return sn(is(t*255),0,255)}function At(t){return sn(is(t/2.55)/100,0,1)}function Yd(t){return sn(is(t*100),0,100)}const et={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},ec=[..."0123456789ABCDEF"],K1=t=>ec[t&15],Q1=t=>ec[(t&240)>>4]+ec[t&15],js=t=>(t&240)>>4===(t&15),G1=t=>js(t.r)&&js(t.g)&&js(t.b)&&js(t.a);function q1(t){var e=t.length,n;return t[0]==="#"&&(e===4||e===5?n={r:255&et[t[1]]*17,g:255&et[t[2]]*17,b:255&et[t[3]]*17,a:e===5?et[t[4]]*17:255}:(e===7||e===9)&&(n={r:et[t[1]]<<4|et[t[2]],g:et[t[3]]<<4|et[t[4]],b:et[t[5]]<<4|et[t[6]],a:e===9?et[t[7]]<<4|et[t[8]]:255})),n}const Z1=(t,e)=>t<255?e(t):"";function J1(t){var e=G1(t)?K1:Q1;return t?"#"+e(t.r)+e(t.g)+e(t.b)+Z1(t.a,e):void 0}const eb=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Qp(t,e,n){const r=e*Math.min(n,1-n),i=(s,o=(s+t/30)%12)=>n-r*Math.max(Math.min(o-3,9-o,1),-1);return[i(0),i(8),i(4)]}function tb(t,e,n){const r=(i,s=(i+t/60)%6)=>n-n*e*Math.max(Math.min(s,4-s,1),0);return[r(5),r(3),r(1)]}function nb(t,e,n){const r=Qp(t,1,.5);let i;for(e+n>1&&(i=1/(e+n),e*=i,n*=i),i=0;i<3;i++)r[i]*=1-e-n,r[i]+=e;return r}function rb(t,e,n,r,i){return t===i?(e-n)/r+(e<n?6:0):e===i?(n-t)/r+2:(t-e)/r+4}function iu(t){const n=t.r/255,r=t.g/255,i=t.b/255,s=Math.max(n,r,i),o=Math.min(n,r,i),a=(s+o)/2;let l,u,d;return s!==o&&(d=s-o,u=a>.5?d/(2-s-o):d/(s+o),l=rb(n,r,i,d,s),l=l*60+.5),[l|0,u||0,a]}function su(t,e,n,r){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,n,r)).map(xn)}function ou(t,e,n){return su(Qp,t,e,n)}function ib(t,e,n){return su(nb,t,e,n)}function sb(t,e,n){return su(tb,t,e,n)}function Gp(t){return(t%360+360)%360}function ob(t){const e=eb.exec(t);let n=255,r;if(!e)return;e[5]!==r&&(n=e[6]?ui(+e[5]):xn(+e[5]));const i=Gp(+e[2]),s=+e[3]/100,o=+e[4]/100;return e[1]==="hwb"?r=ib(i,s,o):e[1]==="hsv"?r=sb(i,s,o):r=ou(i,s,o),{r:r[0],g:r[1],b:r[2],a:n}}function ab(t,e){var n=iu(t);n[0]=Gp(n[0]+e),n=ou(n),t.r=n[0],t.g=n[1],t.b=n[2]}function lb(t){if(!t)return;const e=iu(t),n=e[0],r=Yd(e[1]),i=Yd(e[2]);return t.a<255?`hsla(${n}, ${r}%, ${i}%, ${At(t.a)})`:`hsl(${n}, ${r}%, ${i}%)`}const Xd={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Kd={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function cb(){const t={},e=Object.keys(Kd),n=Object.keys(Xd);let r,i,s,o,a;for(r=0;r<e.length;r++){for(o=a=e[r],i=0;i<n.length;i++)s=n[i],a=a.replace(s,Xd[s]);s=parseInt(Kd[o],16),t[a]=[s>>16&255,s>>8&255,s&255]}return t}let Cs;function ub(t){Cs||(Cs=cb(),Cs.transparent=[0,0,0,0]);const e=Cs[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:e.length===4?e[3]:255}}const db=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function hb(t){const e=db.exec(t);let n=255,r,i,s;if(e){if(e[7]!==r){const o=+e[7];n=e[8]?ui(o):sn(o*255,0,255)}return r=+e[1],i=+e[3],s=+e[5],r=255&(e[2]?ui(r):sn(r,0,255)),i=255&(e[4]?ui(i):sn(i,0,255)),s=255&(e[6]?ui(s):sn(s,0,255)),{r,g:i,b:s,a:n}}}function fb(t){return t&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${At(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`)}const Ua=t=>t<=.0031308?t*12.92:Math.pow(t,1/2.4)*1.055-.055,nr=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function mb(t,e,n){const r=nr(At(t.r)),i=nr(At(t.g)),s=nr(At(t.b));return{r:xn(Ua(r+n*(nr(At(e.r))-r))),g:xn(Ua(i+n*(nr(At(e.g))-i))),b:xn(Ua(s+n*(nr(At(e.b))-s))),a:t.a+n*(e.a-t.a)}}function Ps(t,e,n){if(t){let r=iu(t);r[e]=Math.max(0,Math.min(r[e]+r[e]*n,e===0?360:1)),r=ou(r),t.r=r[0],t.g=r[1],t.b=r[2]}}function qp(t,e){return t&&Object.assign(e||{},t)}function Qd(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=xn(t[3]))):(e=qp(t,{r:0,g:0,b:0,a:1}),e.a=xn(e.a)),e}function pb(t){return t.charAt(0)==="r"?hb(t):ob(t)}class Xi{constructor(e){if(e instanceof Xi)return e;const n=typeof e;let r;n==="object"?r=Qd(e):n==="string"&&(r=q1(e)||ub(e)||pb(e)),this._rgb=r,this._valid=!!r}get valid(){return this._valid}get rgb(){var e=qp(this._rgb);return e&&(e.a=At(e.a)),e}set rgb(e){this._rgb=Qd(e)}rgbString(){return this._valid?fb(this._rgb):void 0}hexString(){return this._valid?J1(this._rgb):void 0}hslString(){return this._valid?lb(this._rgb):void 0}mix(e,n){if(e){const r=this.rgb,i=e.rgb;let s;const o=n===s?.5:n,a=2*o-1,l=r.a-i.a,u=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;s=1-u,r.r=255&u*r.r+s*i.r+.5,r.g=255&u*r.g+s*i.g+.5,r.b=255&u*r.b+s*i.b+.5,r.a=o*r.a+(1-o)*i.a,this.rgb=r}return this}interpolate(e,n){return e&&(this._rgb=mb(this._rgb,e._rgb,n)),this}clone(){return new Xi(this.rgb)}alpha(e){return this._rgb.a=xn(e),this}clearer(e){const n=this._rgb;return n.a*=1-e,this}greyscale(){const e=this._rgb,n=is(e.r*.3+e.g*.59+e.b*.11);return e.r=e.g=e.b=n,this}opaquer(e){const n=this._rgb;return n.a*=1+e,this}negate(){const e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return Ps(this._rgb,2,e),this}darken(e){return Ps(this._rgb,2,-e),this}saturate(e){return Ps(this._rgb,1,e),this}desaturate(e){return Ps(this._rgb,1,-e),this}rotate(e){return ab(this._rgb,e),this}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function Tt(){}const gb=(()=>{let t=0;return()=>t++})();function q(t){return t==null}function ge(t){if(Array.isArray&&Array.isArray(t))return!0;const e=Object.prototype.toString.call(t);return e.slice(0,7)==="[object"&&e.slice(-6)==="Array]"}function W(t){return t!==null&&Object.prototype.toString.call(t)==="[object Object]"}function lt(t){return(typeof t=="number"||t instanceof Number)&&isFinite(+t)}function wt(t,e){return lt(t)?t:e}function V(t,e){return typeof t>"u"?e:t}const xb=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100:+t/e,Zp=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100*e:+t;function ee(t,e,n){if(t&&typeof t.call=="function")return t.apply(n,e)}function X(t,e,n,r){let i,s,o;if(ge(t))for(s=t.length,i=0;i<s;i++)e.call(n,t[i],i);else if(W(t))for(o=Object.keys(t),s=o.length,i=0;i<s;i++)e.call(n,t[o[i]],o[i])}function Do(t,e){let n,r,i,s;if(!t||!e||t.length!==e.length)return!1;for(n=0,r=t.length;n<r;++n)if(i=t[n],s=e[n],i.datasetIndex!==s.datasetIndex||i.index!==s.index)return!1;return!0}function Oo(t){if(ge(t))return t.map(Oo);if(W(t)){const e=Object.create(null),n=Object.keys(t),r=n.length;let i=0;for(;i<r;++i)e[n[i]]=Oo(t[n[i]]);return e}return t}function Jp(t){return["__proto__","prototype","constructor"].indexOf(t)===-1}function vb(t,e,n,r){if(!Jp(t))return;const i=e[t],s=n[t];W(i)&&W(s)?Ki(i,s,r):e[t]=Oo(s)}function Ki(t,e,n){const r=ge(e)?e:[e],i=r.length;if(!W(t))return t;n=n||{};const s=n.merger||vb;let o;for(let a=0;a<i;++a){if(o=r[a],!W(o))continue;const l=Object.keys(o);for(let u=0,d=l.length;u<d;++u)s(l[u],t,o,n)}return t}function ki(t,e){return Ki(t,e,{merger:yb})}function yb(t,e,n){if(!Jp(t))return;const r=e[t],i=n[t];W(r)&&W(i)?ki(r,i):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=Oo(i))}const Gd={"":t=>t,x:t=>t.x,y:t=>t.y};function bb(t){const e=t.split("."),n=[];let r="";for(const i of e)r+=i,r.endsWith("\\")?r=r.slice(0,-1)+".":(n.push(r),r="");return n}function wb(t){const e=bb(t);return n=>{for(const r of e){if(r==="")break;n=n&&n[r]}return n}}function Kn(t,e){return(Gd[e]||(Gd[e]=wb(e)))(t)}function au(t){return t.charAt(0).toUpperCase()+t.slice(1)}const Qi=t=>typeof t<"u",wn=t=>typeof t=="function",qd=(t,e)=>{if(t.size!==e.size)return!1;for(const n of t)if(!e.has(n))return!1;return!0};function Sb(t){return t.type==="mouseup"||t.type==="click"||t.type==="contextmenu"}const fe=Math.PI,he=2*fe,Lo=Number.POSITIVE_INFINITY,_b=fe/180,ve=fe/2,jn=fe/4,Zd=fe*2/3,eg=Math.log10,vn=Math.sign;function qs(t,e,n){return Math.abs(t-e)<n}function Jd(t){const e=Math.round(t);t=qs(t,e,t/1e3)?e:t;const n=Math.pow(10,Math.floor(eg(t))),r=t/n;return(r<=1?1:r<=2?2:r<=5?5:10)*n}function kb(t){const e=[],n=Math.sqrt(t);let r;for(r=1;r<n;r++)t%r===0&&(e.push(r),e.push(t/r));return n===(n|0)&&e.push(n),e.sort((i,s)=>i-s).pop(),e}function Nb(t){return typeof t=="symbol"||typeof t=="object"&&t!==null&&!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t)}function Ao(t){return!Nb(t)&&!isNaN(parseFloat(t))&&isFinite(t)}function jb(t,e){const n=Math.round(t);return n-e<=t&&n+e>=t}function Cb(t,e,n){let r,i,s;for(r=0,i=t.length;r<i;r++)s=t[r][n],isNaN(s)||(e.min=Math.min(e.min,s),e.max=Math.max(e.max,s))}function It(t){return t*(fe/180)}function Pb(t){return t*(180/fe)}function eh(t){if(!lt(t))return;let e=1,n=0;for(;Math.round(t*e)/e!==t;)e*=10,n++;return n}function tg(t,e){const n=e.x-t.x,r=e.y-t.y,i=Math.sqrt(n*n+r*r);let s=Math.atan2(r,n);return s<-.5*fe&&(s+=he),{angle:s,distance:i}}function Eb(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function Cn(t){return(t%he+he)%he}function Fo(t,e,n,r){const i=Cn(t),s=Cn(e),o=Cn(n),a=Cn(s-i),l=Cn(o-i),u=Cn(i-s),d=Cn(i-o);return i===s||i===o||r&&s===o||a>l&&u<d}function Ue(t,e,n){return Math.max(e,Math.min(n,t))}function Mb(t){return Ue(t,-32768,32767)}function Fn(t,e,n,r=1e-6){return t>=Math.min(e,n)-r&&t<=Math.max(e,n)+r}function lu(t,e,n){n=n||(o=>t[o]<e);let r=t.length-1,i=0,s;for(;r-i>1;)s=i+r>>1,n(s)?i=s:r=s;return{lo:i,hi:r}}const tc=(t,e,n,r)=>lu(t,n,r?i=>{const s=t[i][e];return s<n||s===n&&t[i+1][e]===n}:i=>t[i][e]<n),$b=(t,e,n)=>lu(t,n,r=>t[r][e]>=n);function Tb(t,e,n){let r=0,i=t.length;for(;r<i&&t[r]<e;)r++;for(;i>r&&t[i-1]>n;)i--;return r>0||i<t.length?t.slice(r,i):t}const ng=["push","pop","shift","splice","unshift"];function Rb(t,e){if(t._chartjs){t._chartjs.listeners.push(e);return}Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),ng.forEach(n=>{const r="_onData"+au(n),i=t[n];Object.defineProperty(t,n,{configurable:!0,enumerable:!1,value(...s){const o=i.apply(this,s);return t._chartjs.listeners.forEach(a=>{typeof a[r]=="function"&&a[r](...s)}),o}})})}function th(t,e){const n=t._chartjs;if(!n)return;const r=n.listeners,i=r.indexOf(e);i!==-1&&r.splice(i,1),!(r.length>0)&&(ng.forEach(s=>{delete t[s]}),delete t._chartjs)}function rg(t){const e=new Set(t);return e.size===t.length?t:Array.from(e)}const ig=function(){return typeof window>"u"?function(t){return t()}:window.requestAnimationFrame}();function sg(t,e){let n=[],r=!1;return function(...i){n=i,r||(r=!0,ig.call(window,()=>{r=!1,t.apply(e,n)}))}}function Db(t,e){let n;return function(...r){return e?(clearTimeout(n),n=setTimeout(t,e,r)):t.apply(this,r),e}}const cu=t=>t==="start"?"left":t==="end"?"right":"center",je=(t,e,n)=>t==="start"?e:t==="end"?n:(e+n)/2,Ob=(t,e,n,r)=>t===(r?"left":"right")?n:t==="center"?(e+n)/2:e,Es=t=>t===0||t===1,nh=(t,e,n)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*he/n)),rh=(t,e,n)=>Math.pow(2,-10*t)*Math.sin((t-e)*he/n)+1,Ni={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*ve)+1,easeOutSine:t=>Math.sin(t*ve),easeInOutSine:t=>-.5*(Math.cos(fe*t)-1),easeInExpo:t=>t===0?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>t===1?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>Es(t)?t:t<.5?.5*Math.pow(2,10*(t*2-1)):.5*(-Math.pow(2,-10*(t*2-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>Es(t)?t:nh(t,.075,.3),easeOutElastic:t=>Es(t)?t:rh(t,.075,.3),easeInOutElastic(t){return Es(t)?t:t<.5?.5*nh(t*2,.1125,.45):.5+.5*rh(t*2-1,.1125,.45)},easeInBack(t){return t*t*((1.70158+1)*t-1.70158)},easeOutBack(t){return(t-=1)*t*((1.70158+1)*t********)+1},easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-Ni.easeOutBounce(1-t),easeOutBounce(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},easeInOutBounce:t=>t<.5?Ni.easeInBounce(t*2)*.5:Ni.easeOutBounce(t*2-1)*.5+.5};function og(t){if(t&&typeof t=="object"){const e=t.toString();return e==="[object CanvasPattern]"||e==="[object CanvasGradient]"}return!1}function ih(t){return og(t)?t:new Xi(t)}function Ba(t){return og(t)?t:new Xi(t).saturate(.5).darken(.1).hexString()}const Lb=["x","y","borderWidth","radius","tension"],Ab=["color","borderColor","backgroundColor"];function Fb(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:e=>e!=="onProgress"&&e!=="onComplete"&&e!=="fn"}),t.set("animations",{colors:{type:"color",properties:Ab},numbers:{type:"number",properties:Lb}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:e=>e|0}}}})}function zb(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const sh=new Map;function Ib(t,e){e=e||{};const n=t+JSON.stringify(e);let r=sh.get(n);return r||(r=new Intl.NumberFormat(t,e),sh.set(n,r)),r}function uu(t,e,n){return Ib(e,n).format(t)}const Ub={values(t){return ge(t)?t:""+t},numeric(t,e,n){if(t===0)return"0";const r=this.chart.options.locale;let i,s=t;if(n.length>1){const u=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(u<1e-4||u>1e15)&&(i="scientific"),s=Bb(t,n)}const o=eg(Math.abs(s)),a=isNaN(o)?1:Math.max(Math.min(-1*Math.floor(o),20),0),l={notation:i,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),uu(t,r,l)}};function Bb(t,e){let n=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;return Math.abs(n)>=1&&t!==Math.floor(t)&&(n=t-Math.floor(t)),n}var ag={formatters:Ub};function Hb(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,n)=>n.lineWidth,tickColor:(e,n)=>n.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:ag.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:e=>!e.startsWith("before")&&!e.startsWith("after")&&e!=="callback"&&e!=="parser",_indexable:e=>e!=="borderDash"&&e!=="tickBorderDash"&&e!=="dash"}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:e=>e!=="backdropPadding"&&e!=="callback",_indexable:e=>e!=="backdropPadding"})}const Qn=Object.create(null),nc=Object.create(null);function ji(t,e){if(!e)return t;const n=e.split(".");for(let r=0,i=n.length;r<i;++r){const s=n[r];t=t[s]||(t[s]=Object.create(null))}return t}function Ha(t,e,n){return typeof e=="string"?Ki(ji(t,e),n):Ki(ji(t,""),e)}class Wb{constructor(e,n){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=r=>r.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(r,i)=>Ba(i.backgroundColor),this.hoverBorderColor=(r,i)=>Ba(i.borderColor),this.hoverColor=(r,i)=>Ba(i.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(n)}set(e,n){return Ha(this,e,n)}get(e){return ji(this,e)}describe(e,n){return Ha(nc,e,n)}override(e,n){return Ha(Qn,e,n)}route(e,n,r,i){const s=ji(this,e),o=ji(this,r),a="_"+n;Object.defineProperties(s,{[a]:{value:s[n],writable:!0},[n]:{enumerable:!0,get(){const l=this[a],u=o[i];return W(l)?Object.assign({},u,l):V(l,u)},set(l){this[a]=l}}})}apply(e){e.forEach(n=>n(this))}}var ue=new Wb({_scriptable:t=>!t.startsWith("on"),_indexable:t=>t!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Fb,zb,Hb]);function Vb(t){return!t||q(t.size)||q(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}function oh(t,e,n,r,i){let s=e[i];return s||(s=e[i]=t.measureText(i).width,n.push(i)),s>r&&(r=s),r}function Pn(t,e,n){const r=t.currentDevicePixelRatio,i=n!==0?Math.max(n/2,.5):0;return Math.round((e-i)*r)/r+i}function ah(t,e){!e&&!t||(e=e||t.getContext("2d"),e.save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function lh(t,e,n,r){lg(t,e,n,r,null)}function lg(t,e,n,r,i){let s,o,a,l,u,d,h,f;const m=e.pointStyle,x=e.rotation,v=e.radius;let b=(x||0)*_b;if(m&&typeof m=="object"&&(s=m.toString(),s==="[object HTMLImageElement]"||s==="[object HTMLCanvasElement]")){t.save(),t.translate(n,r),t.rotate(b),t.drawImage(m,-m.width/2,-m.height/2,m.width,m.height),t.restore();return}if(!(isNaN(v)||v<=0)){switch(t.beginPath(),m){default:i?t.ellipse(n,r,i/2,v,0,0,he):t.arc(n,r,v,0,he),t.closePath();break;case"triangle":d=i?i/2:v,t.moveTo(n+Math.sin(b)*d,r-Math.cos(b)*v),b+=Zd,t.lineTo(n+Math.sin(b)*d,r-Math.cos(b)*v),b+=Zd,t.lineTo(n+Math.sin(b)*d,r-Math.cos(b)*v),t.closePath();break;case"rectRounded":u=v*.516,l=v-u,o=Math.cos(b+jn)*l,h=Math.cos(b+jn)*(i?i/2-u:l),a=Math.sin(b+jn)*l,f=Math.sin(b+jn)*(i?i/2-u:l),t.arc(n-h,r-a,u,b-fe,b-ve),t.arc(n+f,r-o,u,b-ve,b),t.arc(n+h,r+a,u,b,b+ve),t.arc(n-f,r+o,u,b+ve,b+fe),t.closePath();break;case"rect":if(!x){l=Math.SQRT1_2*v,d=i?i/2:l,t.rect(n-d,r-l,2*d,2*l);break}b+=jn;case"rectRot":h=Math.cos(b)*(i?i/2:v),o=Math.cos(b)*v,a=Math.sin(b)*v,f=Math.sin(b)*(i?i/2:v),t.moveTo(n-h,r-a),t.lineTo(n+f,r-o),t.lineTo(n+h,r+a),t.lineTo(n-f,r+o),t.closePath();break;case"crossRot":b+=jn;case"cross":h=Math.cos(b)*(i?i/2:v),o=Math.cos(b)*v,a=Math.sin(b)*v,f=Math.sin(b)*(i?i/2:v),t.moveTo(n-h,r-a),t.lineTo(n+h,r+a),t.moveTo(n+f,r-o),t.lineTo(n-f,r+o);break;case"star":h=Math.cos(b)*(i?i/2:v),o=Math.cos(b)*v,a=Math.sin(b)*v,f=Math.sin(b)*(i?i/2:v),t.moveTo(n-h,r-a),t.lineTo(n+h,r+a),t.moveTo(n+f,r-o),t.lineTo(n-f,r+o),b+=jn,h=Math.cos(b)*(i?i/2:v),o=Math.cos(b)*v,a=Math.sin(b)*v,f=Math.sin(b)*(i?i/2:v),t.moveTo(n-h,r-a),t.lineTo(n+h,r+a),t.moveTo(n+f,r-o),t.lineTo(n-f,r+o);break;case"line":o=i?i/2:Math.cos(b)*v,a=Math.sin(b)*v,t.moveTo(n-o,r-a),t.lineTo(n+o,r+a);break;case"dash":t.moveTo(n,r),t.lineTo(n+Math.cos(b)*(i?i/2:v),r+Math.sin(b)*v);break;case!1:t.closePath();break}t.fill(),e.borderWidth>0&&t.stroke()}}function cg(t,e,n){return n=n||.5,!e||t&&t.x>e.left-n&&t.x<e.right+n&&t.y>e.top-n&&t.y<e.bottom+n}function du(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function hu(t){t.restore()}function Yb(t,e){e.translation&&t.translate(e.translation[0],e.translation[1]),q(e.rotation)||t.rotate(e.rotation),e.color&&(t.fillStyle=e.color),e.textAlign&&(t.textAlign=e.textAlign),e.textBaseline&&(t.textBaseline=e.textBaseline)}function Xb(t,e,n,r,i){if(i.strikethrough||i.underline){const s=t.measureText(r),o=e-s.actualBoundingBoxLeft,a=e+s.actualBoundingBoxRight,l=n-s.actualBoundingBoxAscent,u=n+s.actualBoundingBoxDescent,d=i.strikethrough?(l+u)/2:u;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=i.decorationWidth||2,t.moveTo(o,d),t.lineTo(a,d),t.stroke()}}function Kb(t,e){const n=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=n}function Gi(t,e,n,r,i,s={}){const o=ge(e)?e:[e],a=s.strokeWidth>0&&s.strokeColor!=="";let l,u;for(t.save(),t.font=i.string,Yb(t,s),l=0;l<o.length;++l)u=o[l],s.backdrop&&Kb(t,s.backdrop),a&&(s.strokeColor&&(t.strokeStyle=s.strokeColor),q(s.strokeWidth)||(t.lineWidth=s.strokeWidth),t.strokeText(u,n,r,s.maxWidth)),t.fillText(u,n,r,s.maxWidth),Xb(t,n,r,u,s),r+=Number(i.lineHeight);t.restore()}function zo(t,e){const{x:n,y:r,w:i,h:s,radius:o}=e;t.arc(n+o.topLeft,r+o.topLeft,o.topLeft,1.5*fe,fe,!0),t.lineTo(n,r+s-o.bottomLeft),t.arc(n+o.bottomLeft,r+s-o.bottomLeft,o.bottomLeft,fe,ve,!0),t.lineTo(n+i-o.bottomRight,r+s),t.arc(n+i-o.bottomRight,r+s-o.bottomRight,o.bottomRight,ve,0,!0),t.lineTo(n+i,r+o.topRight),t.arc(n+i-o.topRight,r+o.topRight,o.topRight,0,-ve,!0),t.lineTo(n+o.topLeft,r)}const Qb=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Gb=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function qb(t,e){const n=(""+t).match(Qb);if(!n||n[1]==="normal")return e*1.2;switch(t=+n[2],n[3]){case"px":return t;case"%":t/=100;break}return e*t}const Zb=t=>+t||0;function fu(t,e){const n={},r=W(e),i=r?Object.keys(e):e,s=W(t)?r?o=>V(t[o],t[e[o]]):o=>t[o]:()=>t;for(const o of i)n[o]=Zb(s(o));return n}function ug(t){return fu(t,{top:"y",right:"x",bottom:"y",left:"x"})}function kr(t){return fu(t,["topLeft","topRight","bottomLeft","bottomRight"])}function ct(t){const e=ug(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function Pe(t,e){t=t||{},e=e||ue.font;let n=V(t.size,e.size);typeof n=="string"&&(n=parseInt(n,10));let r=V(t.style,e.style);r&&!(""+r).match(Gb)&&(console.warn('Invalid font style specified: "'+r+'"'),r=void 0);const i={family:V(t.family,e.family),lineHeight:qb(V(t.lineHeight,e.lineHeight),n),size:n,style:r,weight:V(t.weight,e.weight),string:""};return i.string=Vb(i),i}function Ms(t,e,n,r){let i,s,o;for(i=0,s=t.length;i<s;++i)if(o=t[i],o!==void 0&&o!==void 0)return o}function Jb(t,e,n){const{min:r,max:i}=t,s=Zp(e,(i-r)/2),o=(a,l)=>n&&a===0?0:a+l;return{min:o(r,-Math.abs(s)),max:o(i,s)}}function Hr(t,e){return Object.assign(Object.create(t),e)}function mu(t,e=[""],n,r,i=()=>t[0]){const s=n||t;typeof r>"u"&&(r=mg("_fallback",t));const o={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:s,_fallback:r,_getTarget:i,override:a=>mu([a,...t],e,s,r)};return new Proxy(o,{deleteProperty(a,l){return delete a[l],delete a._keys,delete t[0][l],!0},get(a,l){return hg(a,l,()=>aw(l,e,t,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(t[0])},has(a,l){return uh(a).includes(l)},ownKeys(a){return uh(a)},set(a,l,u){const d=a._storage||(a._storage=i());return a[l]=d[l]=u,delete a._keys,!0}})}function Or(t,e,n,r){const i={_cacheable:!1,_proxy:t,_context:e,_subProxy:n,_stack:new Set,_descriptors:dg(t,r),setContext:s=>Or(t,s,n,r),override:s=>Or(t.override(s),e,n,r)};return new Proxy(i,{deleteProperty(s,o){return delete s[o],delete t[o],!0},get(s,o,a){return hg(s,o,()=>tw(s,o,a))},getOwnPropertyDescriptor(s,o){return s._descriptors.allKeys?Reflect.has(t,o)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,o)},getPrototypeOf(){return Reflect.getPrototypeOf(t)},has(s,o){return Reflect.has(t,o)},ownKeys(){return Reflect.ownKeys(t)},set(s,o,a){return t[o]=a,delete s[o],!0}})}function dg(t,e={scriptable:!0,indexable:!0}){const{_scriptable:n=e.scriptable,_indexable:r=e.indexable,_allKeys:i=e.allKeys}=t;return{allKeys:i,scriptable:n,indexable:r,isScriptable:wn(n)?n:()=>n,isIndexable:wn(r)?r:()=>r}}const ew=(t,e)=>t?t+au(e):e,pu=(t,e)=>W(e)&&t!=="adapters"&&(Object.getPrototypeOf(e)===null||e.constructor===Object);function hg(t,e,n){if(Object.prototype.hasOwnProperty.call(t,e)||e==="constructor")return t[e];const r=n();return t[e]=r,r}function tw(t,e,n){const{_proxy:r,_context:i,_subProxy:s,_descriptors:o}=t;let a=r[e];return wn(a)&&o.isScriptable(e)&&(a=nw(e,a,t,n)),ge(a)&&a.length&&(a=rw(e,a,t,o.isIndexable)),pu(e,a)&&(a=Or(a,i,s&&s[e],o)),a}function nw(t,e,n,r){const{_proxy:i,_context:s,_subProxy:o,_stack:a}=n;if(a.has(t))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+t);a.add(t);let l=e(s,o||r);return a.delete(t),pu(t,l)&&(l=gu(i._scopes,i,t,l)),l}function rw(t,e,n,r){const{_proxy:i,_context:s,_subProxy:o,_descriptors:a}=n;if(typeof s.index<"u"&&r(t))return e[s.index%e.length];if(W(e[0])){const l=e,u=i._scopes.filter(d=>d!==l);e=[];for(const d of l){const h=gu(u,i,t,d);e.push(Or(h,s,o&&o[t],a))}}return e}function fg(t,e,n){return wn(t)?t(e,n):t}const iw=(t,e)=>t===!0?e:typeof t=="string"?Kn(e,t):void 0;function sw(t,e,n,r,i){for(const s of e){const o=iw(n,s);if(o){t.add(o);const a=fg(o._fallback,n,i);if(typeof a<"u"&&a!==n&&a!==r)return a}else if(o===!1&&typeof r<"u"&&n!==r)return null}return!1}function gu(t,e,n,r){const i=e._rootScopes,s=fg(e._fallback,n,r),o=[...t,...i],a=new Set;a.add(r);let l=ch(a,o,n,s||n,r);return l===null||typeof s<"u"&&s!==n&&(l=ch(a,o,s,l,r),l===null)?!1:mu(Array.from(a),[""],i,s,()=>ow(e,n,r))}function ch(t,e,n,r,i){for(;n;)n=sw(t,e,n,r,i);return n}function ow(t,e,n){const r=t._getTarget();e in r||(r[e]={});const i=r[e];return ge(i)&&W(n)?n:i||{}}function aw(t,e,n,r){let i;for(const s of e)if(i=mg(ew(s,t),n),typeof i<"u")return pu(t,i)?gu(n,r,t,i):i}function mg(t,e){for(const n of e){if(!n)continue;const r=n[t];if(typeof r<"u")return r}}function uh(t){let e=t._keys;return e||(e=t._keys=lw(t._scopes)),e}function lw(t){const e=new Set;for(const n of t)for(const r of Object.keys(n).filter(i=>!i.startsWith("_")))e.add(r);return Array.from(e)}function xu(){return typeof window<"u"&&typeof document<"u"}function vu(t){let e=t.parentNode;return e&&e.toString()==="[object ShadowRoot]"&&(e=e.host),e}function Io(t,e,n){let r;return typeof t=="string"?(r=parseInt(t,10),t.indexOf("%")!==-1&&(r=r/100*e.parentNode[n])):r=t,r}const ua=t=>t.ownerDocument.defaultView.getComputedStyle(t,null);function cw(t,e){return ua(t).getPropertyValue(e)}const uw=["top","right","bottom","left"];function Un(t,e,n){const r={};n=n?"-"+n:"";for(let i=0;i<4;i++){const s=uw[i];r[s]=parseFloat(t[e+"-"+s+n])||0}return r.width=r.left+r.right,r.height=r.top+r.bottom,r}const dw=(t,e,n)=>(t>0||e>0)&&(!n||!n.shadowRoot);function hw(t,e){const n=t.touches,r=n&&n.length?n[0]:t,{offsetX:i,offsetY:s}=r;let o=!1,a,l;if(dw(i,s,t.target))a=i,l=s;else{const u=e.getBoundingClientRect();a=r.clientX-u.left,l=r.clientY-u.top,o=!0}return{x:a,y:l,box:o}}function Dn(t,e){if("native"in t)return t;const{canvas:n,currentDevicePixelRatio:r}=e,i=ua(n),s=i.boxSizing==="border-box",o=Un(i,"padding"),a=Un(i,"border","width"),{x:l,y:u,box:d}=hw(t,n),h=o.left+(d&&a.left),f=o.top+(d&&a.top);let{width:m,height:x}=e;return s&&(m-=o.width+a.width,x-=o.height+a.height),{x:Math.round((l-h)/m*n.width/r),y:Math.round((u-f)/x*n.height/r)}}function fw(t,e,n){let r,i;if(e===void 0||n===void 0){const s=t&&vu(t);if(!s)e=t.clientWidth,n=t.clientHeight;else{const o=s.getBoundingClientRect(),a=ua(s),l=Un(a,"border","width"),u=Un(a,"padding");e=o.width-u.width-l.width,n=o.height-u.height-l.height,r=Io(a.maxWidth,s,"clientWidth"),i=Io(a.maxHeight,s,"clientHeight")}}return{width:e,height:n,maxWidth:r||Lo,maxHeight:i||Lo}}const $s=t=>Math.round(t*10)/10;function mw(t,e,n,r){const i=ua(t),s=Un(i,"margin"),o=Io(i.maxWidth,t,"clientWidth")||Lo,a=Io(i.maxHeight,t,"clientHeight")||Lo,l=fw(t,e,n);let{width:u,height:d}=l;if(i.boxSizing==="content-box"){const f=Un(i,"border","width"),m=Un(i,"padding");u-=m.width+f.width,d-=m.height+f.height}return u=Math.max(0,u-s.width),d=Math.max(0,r?u/r:d-s.height),u=$s(Math.min(u,o,l.maxWidth)),d=$s(Math.min(d,a,l.maxHeight)),u&&!d&&(d=$s(u/2)),(e!==void 0||n!==void 0)&&r&&l.height&&d>l.height&&(d=l.height,u=$s(Math.floor(d*r))),{width:u,height:d}}function dh(t,e,n){const r=e||1,i=Math.floor(t.height*r),s=Math.floor(t.width*r);t.height=Math.floor(t.height),t.width=Math.floor(t.width);const o=t.canvas;return o.style&&(n||!o.style.height&&!o.style.width)&&(o.style.height=`${t.height}px`,o.style.width=`${t.width}px`),t.currentDevicePixelRatio!==r||o.height!==i||o.width!==s?(t.currentDevicePixelRatio=r,o.height=i,o.width=s,t.ctx.setTransform(r,0,0,r,0,0),!0):!1}const pw=function(){let t=!1;try{const e={get passive(){return t=!0,!1}};xu()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch{}return t}();function hh(t,e){const n=cw(t,e),r=n&&n.match(/^(\d+)(\.\d+)?px$/);return r?+r[1]:void 0}const gw=function(t,e){return{x(n){return t+t+e-n},setWidth(n){e=n},textAlign(n){return n==="center"?n:n==="right"?"left":"right"},xPlus(n,r){return n-r},leftForLtr(n,r){return n-r}}},xw=function(){return{x(t){return t},setWidth(t){},textAlign(t){return t},xPlus(t,e){return t+e},leftForLtr(t,e){return t}}};function Nr(t,e,n){return t?gw(e,n):xw()}function pg(t,e){let n,r;(e==="ltr"||e==="rtl")&&(n=t.canvas.style,r=[n.getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",e,"important"),t.prevTextDirection=r)}function gg(t,e){e!==void 0&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function Ts(t,e,n){return t.options.clip?t[n]:e[n]}function vw(t,e){const{xScale:n,yScale:r}=t;return n&&r?{left:Ts(n,e,"left"),right:Ts(n,e,"right"),top:Ts(r,e,"top"),bottom:Ts(r,e,"bottom")}:e}function yw(t,e){const n=e._clip;if(n.disabled)return!1;const r=vw(e,t.chartArea);return{left:n.left===!1?0:r.left-(n.left===!0?0:n.left),right:n.right===!1?t.width:r.right+(n.right===!0?0:n.right),top:n.top===!1?0:r.top-(n.top===!0?0:n.top),bottom:n.bottom===!1?t.height:r.bottom+(n.bottom===!0?0:n.bottom)}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class bw{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(e,n,r,i){const s=n.listeners[i],o=n.duration;s.forEach(a=>a({chart:e,initial:n.initial,numSteps:o,currentStep:Math.min(r-n.start,o)}))}_refresh(){this._request||(this._running=!0,this._request=ig.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(e=Date.now()){let n=0;this._charts.forEach((r,i)=>{if(!r.running||!r.items.length)return;const s=r.items;let o=s.length-1,a=!1,l;for(;o>=0;--o)l=s[o],l._active?(l._total>r.duration&&(r.duration=l._total),l.tick(e),a=!0):(s[o]=s[s.length-1],s.pop());a&&(i.draw(),this._notify(i,r,e,"progress")),s.length||(r.running=!1,this._notify(i,r,e,"complete"),r.initial=!1),n+=s.length}),this._lastDate=e,n===0&&(this._running=!1)}_getAnims(e){const n=this._charts;let r=n.get(e);return r||(r={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},n.set(e,r)),r}listen(e,n,r){this._getAnims(e).listeners[n].push(r)}add(e,n){!n||!n.length||this._getAnims(e).items.push(...n)}has(e){return this._getAnims(e).items.length>0}start(e){const n=this._charts.get(e);n&&(n.running=!0,n.start=Date.now(),n.duration=n.items.reduce((r,i)=>Math.max(r,i._duration),0),this._refresh())}running(e){if(!this._running)return!1;const n=this._charts.get(e);return!(!n||!n.running||!n.items.length)}stop(e){const n=this._charts.get(e);if(!n||!n.items.length)return;const r=n.items;let i=r.length-1;for(;i>=0;--i)r[i].cancel();n.items=[],this._notify(e,n,Date.now(),"complete")}remove(e){return this._charts.delete(e)}}var Rt=new bw;const fh="transparent",ww={boolean(t,e,n){return n>.5?e:t},color(t,e,n){const r=ih(t||fh),i=r.valid&&ih(e||fh);return i&&i.valid?i.mix(r,n).hexString():e},number(t,e,n){return t+(e-t)*n}};let Sw=class{constructor(e,n,r,i){const s=n[r];i=Ms([e.to,i,s,e.from]);const o=Ms([e.from,s,i]);this._active=!0,this._fn=e.fn||ww[e.type||typeof o],this._easing=Ni[e.easing]||Ni.linear,this._start=Math.floor(Date.now()+(e.delay||0)),this._duration=this._total=Math.floor(e.duration),this._loop=!!e.loop,this._target=n,this._prop=r,this._from=o,this._to=i,this._promises=void 0}active(){return this._active}update(e,n,r){if(this._active){this._notify(!1);const i=this._target[this._prop],s=r-this._start,o=this._duration-s;this._start=r,this._duration=Math.floor(Math.max(o,e.duration)),this._total+=s,this._loop=!!e.loop,this._to=Ms([e.to,n,i,e.from]),this._from=Ms([e.from,i,n])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(e){const n=e-this._start,r=this._duration,i=this._prop,s=this._from,o=this._loop,a=this._to;let l;if(this._active=s!==a&&(o||n<r),!this._active){this._target[i]=a,this._notify(!0);return}if(n<0){this._target[i]=s;return}l=n/r%2,l=o&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[i]=this._fn(s,a,l)}wait(){const e=this._promises||(this._promises=[]);return new Promise((n,r)=>{e.push({res:n,rej:r})})}_notify(e){const n=e?"res":"rej",r=this._promises||[];for(let i=0;i<r.length;i++)r[i][n]()}};class xg{constructor(e,n){this._chart=e,this._properties=new Map,this.configure(n)}configure(e){if(!W(e))return;const n=Object.keys(ue.animation),r=this._properties;Object.getOwnPropertyNames(e).forEach(i=>{const s=e[i];if(!W(s))return;const o={};for(const a of n)o[a]=s[a];(ge(s.properties)&&s.properties||[i]).forEach(a=>{(a===i||!r.has(a))&&r.set(a,o)})})}_animateOptions(e,n){const r=n.options,i=kw(e,r);if(!i)return[];const s=this._createAnimations(i,r);return r.$shared&&_w(e.options.$animations,r).then(()=>{e.options=r},()=>{}),s}_createAnimations(e,n){const r=this._properties,i=[],s=e.$animations||(e.$animations={}),o=Object.keys(n),a=Date.now();let l;for(l=o.length-1;l>=0;--l){const u=o[l];if(u.charAt(0)==="$")continue;if(u==="options"){i.push(...this._animateOptions(e,n));continue}const d=n[u];let h=s[u];const f=r.get(u);if(h)if(f&&h.active()){h.update(f,d,a);continue}else h.cancel();if(!f||!f.duration){e[u]=d;continue}s[u]=h=new Sw(f,e,u,d),i.push(h)}return i}update(e,n){if(this._properties.size===0){Object.assign(e,n);return}const r=this._createAnimations(e,n);if(r.length)return Rt.add(this._chart,r),!0}}function _w(t,e){const n=[],r=Object.keys(e);for(let i=0;i<r.length;i++){const s=t[r[i]];s&&s.active()&&n.push(s.wait())}return Promise.all(n)}function kw(t,e){if(!e)return;let n=t.options;if(!n){t.options=e;return}return n.$shared&&(t.options=n=Object.assign({},n,{$shared:!1,$animations:{}})),n}function mh(t,e){const n=t&&t.options||{},r=n.reverse,i=n.min===void 0?e:0,s=n.max===void 0?e:0;return{start:r?s:i,end:r?i:s}}function Nw(t,e,n){if(n===!1)return!1;const r=mh(t,n),i=mh(e,n);return{top:i.end,right:r.end,bottom:i.start,left:r.start}}function jw(t){let e,n,r,i;return W(t)?(e=t.top,n=t.right,r=t.bottom,i=t.left):e=n=r=i=t,{top:e,right:n,bottom:r,left:i,disabled:t===!1}}function vg(t,e){const n=[],r=t._getSortedDatasetMetas(e);let i,s;for(i=0,s=r.length;i<s;++i)n.push(r[i].index);return n}function ph(t,e,n,r={}){const i=t.keys,s=r.mode==="single";let o,a,l,u;if(e===null)return;let d=!1;for(o=0,a=i.length;o<a;++o){if(l=+i[o],l===n){if(d=!0,r.all)continue;break}u=t.values[l],lt(u)&&(s||e===0||vn(e)===vn(u))&&(e+=u)}return!d&&!r.all?0:e}function Cw(t,e){const{iScale:n,vScale:r}=e,i=n.axis==="x"?"x":"y",s=r.axis==="x"?"x":"y",o=Object.keys(t),a=new Array(o.length);let l,u,d;for(l=0,u=o.length;l<u;++l)d=o[l],a[l]={[i]:d,[s]:t[d]};return a}function Wa(t,e){const n=t&&t.options.stacked;return n||n===void 0&&e.stack!==void 0}function Pw(t,e,n){return`${t.id}.${e.id}.${n.stack||n.type}`}function Ew(t){const{min:e,max:n,minDefined:r,maxDefined:i}=t.getUserBounds();return{min:r?e:Number.NEGATIVE_INFINITY,max:i?n:Number.POSITIVE_INFINITY}}function Mw(t,e,n){const r=t[e]||(t[e]={});return r[n]||(r[n]={})}function gh(t,e,n,r){for(const i of e.getMatchingVisibleMetas(r).reverse()){const s=t[i.index];if(n&&s>0||!n&&s<0)return i.index}return null}function xh(t,e){const{chart:n,_cachedMeta:r}=t,i=n._stacks||(n._stacks={}),{iScale:s,vScale:o,index:a}=r,l=s.axis,u=o.axis,d=Pw(s,o,r),h=e.length;let f;for(let m=0;m<h;++m){const x=e[m],{[l]:v,[u]:b}=x,g=x._stacks||(x._stacks={});f=g[u]=Mw(i,d,v),f[a]=b,f._top=gh(f,o,!0,r.type),f._bottom=gh(f,o,!1,r.type);const p=f._visualValues||(f._visualValues={});p[a]=b}}function Va(t,e){const n=t.scales;return Object.keys(n).filter(r=>n[r].axis===e).shift()}function $w(t,e){return Hr(t,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}function Tw(t,e,n){return Hr(t,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:n,index:e,mode:"default",type:"data"})}function ti(t,e){const n=t.controller.index,r=t.vScale&&t.vScale.axis;if(r){e=e||t._parsed;for(const i of e){const s=i._stacks;if(!s||s[r]===void 0||s[r][n]===void 0)return;delete s[r][n],s[r]._visualValues!==void 0&&s[r]._visualValues[n]!==void 0&&delete s[r]._visualValues[n]}}}const Ya=t=>t==="reset"||t==="none",vh=(t,e)=>e?t:Object.assign({},t),Rw=(t,e,n)=>t&&!e.hidden&&e._stacked&&{keys:vg(n,!0),values:null};class jr{constructor(e,n){this.chart=e,this._ctx=e.ctx,this.index=n,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const e=this._cachedMeta;this.configure(),this.linkScales(),e._stacked=Wa(e.vScale,e),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(e){this.index!==e&&ti(this._cachedMeta),this.index=e}linkScales(){const e=this.chart,n=this._cachedMeta,r=this.getDataset(),i=(h,f,m,x)=>h==="x"?f:h==="r"?x:m,s=n.xAxisID=V(r.xAxisID,Va(e,"x")),o=n.yAxisID=V(r.yAxisID,Va(e,"y")),a=n.rAxisID=V(r.rAxisID,Va(e,"r")),l=n.indexAxis,u=n.iAxisID=i(l,s,o,a),d=n.vAxisID=i(l,o,s,a);n.xScale=this.getScaleForId(s),n.yScale=this.getScaleForId(o),n.rScale=this.getScaleForId(a),n.iScale=this.getScaleForId(u),n.vScale=this.getScaleForId(d)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(e){return this.chart.scales[e]}_getOtherScale(e){const n=this._cachedMeta;return e===n.iScale?n.vScale:n.iScale}reset(){this._update("reset")}_destroy(){const e=this._cachedMeta;this._data&&th(this._data,this),e._stacked&&ti(e)}_dataCheck(){const e=this.getDataset(),n=e.data||(e.data=[]),r=this._data;if(W(n)){const i=this._cachedMeta;this._data=Cw(n,i)}else if(r!==n){if(r){th(r,this);const i=this._cachedMeta;ti(i),i._parsed=[]}n&&Object.isExtensible(n)&&Rb(n,this),this._syncList=[],this._data=n}}addElements(){const e=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(e.dataset=new this.datasetElementType)}buildOrUpdateElements(e){const n=this._cachedMeta,r=this.getDataset();let i=!1;this._dataCheck();const s=n._stacked;n._stacked=Wa(n.vScale,n),n.stack!==r.stack&&(i=!0,ti(n),n.stack=r.stack),this._resyncElements(e),(i||s!==n._stacked)&&(xh(this,n._parsed),n._stacked=Wa(n.vScale,n))}configure(){const e=this.chart.config,n=e.datasetScopeKeys(this._type),r=e.getOptionScopes(this.getDataset(),n,!0);this.options=e.createResolver(r,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(e,n){const{_cachedMeta:r,_data:i}=this,{iScale:s,_stacked:o}=r,a=s.axis;let l=e===0&&n===i.length?!0:r._sorted,u=e>0&&r._parsed[e-1],d,h,f;if(this._parsing===!1)r._parsed=i,r._sorted=!0,f=i;else{ge(i[e])?f=this.parseArrayData(r,i,e,n):W(i[e])?f=this.parseObjectData(r,i,e,n):f=this.parsePrimitiveData(r,i,e,n);const m=()=>h[a]===null||u&&h[a]<u[a];for(d=0;d<n;++d)r._parsed[d+e]=h=f[d],l&&(m()&&(l=!1),u=h);r._sorted=l}o&&xh(this,f)}parsePrimitiveData(e,n,r,i){const{iScale:s,vScale:o}=e,a=s.axis,l=o.axis,u=s.getLabels(),d=s===o,h=new Array(i);let f,m,x;for(f=0,m=i;f<m;++f)x=f+r,h[f]={[a]:d||s.parse(u[x],x),[l]:o.parse(n[x],x)};return h}parseArrayData(e,n,r,i){const{xScale:s,yScale:o}=e,a=new Array(i);let l,u,d,h;for(l=0,u=i;l<u;++l)d=l+r,h=n[d],a[l]={x:s.parse(h[0],d),y:o.parse(h[1],d)};return a}parseObjectData(e,n,r,i){const{xScale:s,yScale:o}=e,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,u=new Array(i);let d,h,f,m;for(d=0,h=i;d<h;++d)f=d+r,m=n[f],u[d]={x:s.parse(Kn(m,a),f),y:o.parse(Kn(m,l),f)};return u}getParsed(e){return this._cachedMeta._parsed[e]}getDataElement(e){return this._cachedMeta.data[e]}applyStack(e,n,r){const i=this.chart,s=this._cachedMeta,o=n[e.axis],a={keys:vg(i,!0),values:n._stacks[e.axis]._visualValues};return ph(a,o,s.index,{mode:r})}updateRangeFromParsed(e,n,r,i){const s=r[n.axis];let o=s===null?NaN:s;const a=i&&r._stacks[n.axis];i&&a&&(i.values=a,o=ph(i,s,this._cachedMeta.index)),e.min=Math.min(e.min,o),e.max=Math.max(e.max,o)}getMinMax(e,n){const r=this._cachedMeta,i=r._parsed,s=r._sorted&&e===r.iScale,o=i.length,a=this._getOtherScale(e),l=Rw(n,r,this.chart),u={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:d,max:h}=Ew(a);let f,m;function x(){m=i[f];const v=m[a.axis];return!lt(m[e.axis])||d>v||h<v}for(f=0;f<o&&!(!x()&&(this.updateRangeFromParsed(u,e,m,l),s));++f);if(s){for(f=o-1;f>=0;--f)if(!x()){this.updateRangeFromParsed(u,e,m,l);break}}return u}getAllParsedValues(e){const n=this._cachedMeta._parsed,r=[];let i,s,o;for(i=0,s=n.length;i<s;++i)o=n[i][e.axis],lt(o)&&r.push(o);return r}getMaxOverflow(){return!1}getLabelAndValue(e){const n=this._cachedMeta,r=n.iScale,i=n.vScale,s=this.getParsed(e);return{label:r?""+r.getLabelForValue(s[r.axis]):"",value:i?""+i.getLabelForValue(s[i.axis]):""}}_update(e){const n=this._cachedMeta;this.update(e||"default"),n._clip=jw(V(this.options.clip,Nw(n.xScale,n.yScale,this.getMaxOverflow())))}update(e){}draw(){const e=this._ctx,n=this.chart,r=this._cachedMeta,i=r.data||[],s=n.chartArea,o=[],a=this._drawStart||0,l=this._drawCount||i.length-a,u=this.options.drawActiveElementsOnTop;let d;for(r.dataset&&r.dataset.draw(e,s,a,l),d=a;d<a+l;++d){const h=i[d];h.hidden||(h.active&&u?o.push(h):h.draw(e,s))}for(d=0;d<o.length;++d)o[d].draw(e,s)}getStyle(e,n){const r=n?"active":"default";return e===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(r):this.resolveDataElementOptions(e||0,r)}getContext(e,n,r){const i=this.getDataset();let s;if(e>=0&&e<this._cachedMeta.data.length){const o=this._cachedMeta.data[e];s=o.$context||(o.$context=Tw(this.getContext(),e,o)),s.parsed=this.getParsed(e),s.raw=i.data[e],s.index=s.dataIndex=e}else s=this.$context||(this.$context=$w(this.chart.getContext(),this.index)),s.dataset=i,s.index=s.datasetIndex=this.index;return s.active=!!n,s.mode=r,s}resolveDatasetElementOptions(e){return this._resolveElementOptions(this.datasetElementType.id,e)}resolveDataElementOptions(e,n){return this._resolveElementOptions(this.dataElementType.id,n,e)}_resolveElementOptions(e,n="default",r){const i=n==="active",s=this._cachedDataOpts,o=e+"-"+n,a=s[o],l=this.enableOptionSharing&&Qi(r);if(a)return vh(a,l);const u=this.chart.config,d=u.datasetElementScopeKeys(this._type,e),h=i?[`${e}Hover`,"hover",e,""]:[e,""],f=u.getOptionScopes(this.getDataset(),d),m=Object.keys(ue.elements[e]),x=()=>this.getContext(r,i,n),v=u.resolveNamedOptions(f,m,x,h);return v.$shared&&(v.$shared=l,s[o]=Object.freeze(vh(v,l))),v}_resolveAnimations(e,n,r){const i=this.chart,s=this._cachedDataOpts,o=`animation-${n}`,a=s[o];if(a)return a;let l;if(i.options.animation!==!1){const d=this.chart.config,h=d.datasetAnimationScopeKeys(this._type,n),f=d.getOptionScopes(this.getDataset(),h);l=d.createResolver(f,this.getContext(e,r,n))}const u=new xg(i,l&&l.animations);return l&&l._cacheable&&(s[o]=Object.freeze(u)),u}getSharedOptions(e){if(e.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},e))}includeOptions(e,n){return!n||Ya(e)||this.chart._animationsDisabled}_getSharedOptions(e,n){const r=this.resolveDataElementOptions(e,n),i=this._sharedOptions,s=this.getSharedOptions(r),o=this.includeOptions(n,s)||s!==i;return this.updateSharedOptions(s,n,r),{sharedOptions:s,includeOptions:o}}updateElement(e,n,r,i){Ya(i)?Object.assign(e,r):this._resolveAnimations(n,i).update(e,r)}updateSharedOptions(e,n,r){e&&!Ya(n)&&this._resolveAnimations(void 0,n).update(e,r)}_setStyle(e,n,r,i){e.active=i;const s=this.getStyle(n,i);this._resolveAnimations(n,r,i).update(e,{options:!i&&this.getSharedOptions(s)||s})}removeHoverStyle(e,n,r){this._setStyle(e,r,"active",!1)}setHoverStyle(e,n,r){this._setStyle(e,r,"active",!0)}_removeDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!1)}_setDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!0)}_resyncElements(e){const n=this._data,r=this._cachedMeta.data;for(const[a,l,u]of this._syncList)this[a](l,u);this._syncList=[];const i=r.length,s=n.length,o=Math.min(s,i);o&&this.parse(0,o),s>i?this._insertElements(i,s-i,e):s<i&&this._removeElements(s,i-s)}_insertElements(e,n,r=!0){const i=this._cachedMeta,s=i.data,o=e+n;let a;const l=u=>{for(u.length+=n,a=u.length-1;a>=o;a--)u[a]=u[a-n]};for(l(s),a=e;a<o;++a)s[a]=new this.dataElementType;this._parsing&&l(i._parsed),this.parse(e,n),r&&this.updateElements(s,e,n,"reset")}updateElements(e,n,r,i){}_removeElements(e,n){const r=this._cachedMeta;if(this._parsing){const i=r._parsed.splice(e,n);r._stacked&&ti(r,i)}r.data.splice(e,n)}_sync(e){if(this._parsing)this._syncList.push(e);else{const[n,r,i]=e;this[n](r,i)}this.chart._dataChanges.push([this.index,...e])}_onDataPush(){const e=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-e,e])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(e,n){n&&this._sync(["_removeElements",e,n]);const r=arguments.length-2;r&&this._sync(["_insertElements",e,r])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}F(jr,"defaults",{}),F(jr,"datasetElementType",null),F(jr,"dataElementType",null);function Dw(t,e){if(!t._cache.$bar){const n=t.getMatchingVisibleMetas(e);let r=[];for(let i=0,s=n.length;i<s;i++)r=r.concat(n[i].controller.getAllParsedValues(t));t._cache.$bar=rg(r.sort((i,s)=>i-s))}return t._cache.$bar}function Ow(t){const e=t.iScale,n=Dw(e,t.type);let r=e._length,i,s,o,a;const l=()=>{o===32767||o===-32768||(Qi(a)&&(r=Math.min(r,Math.abs(o-a)||r)),a=o)};for(i=0,s=n.length;i<s;++i)o=e.getPixelForValue(n[i]),l();for(a=void 0,i=0,s=e.ticks.length;i<s;++i)o=e.getPixelForTick(i),l();return r}function Lw(t,e,n,r){const i=n.barThickness;let s,o;return q(i)?(s=e.min*n.categoryPercentage,o=n.barPercentage):(s=i*r,o=1),{chunk:s/r,ratio:o,start:e.pixels[t]-s/2}}function Aw(t,e,n,r){const i=e.pixels,s=i[t];let o=t>0?i[t-1]:null,a=t<i.length-1?i[t+1]:null;const l=n.categoryPercentage;o===null&&(o=s-(a===null?e.end-e.start:a-s)),a===null&&(a=s+s-o);const u=s-(s-Math.min(o,a))/2*l;return{chunk:Math.abs(a-o)/2*l/r,ratio:n.barPercentage,start:u}}function Fw(t,e,n,r){const i=n.parse(t[0],r),s=n.parse(t[1],r),o=Math.min(i,s),a=Math.max(i,s);let l=o,u=a;Math.abs(o)>Math.abs(a)&&(l=a,u=o),e[n.axis]=u,e._custom={barStart:l,barEnd:u,start:i,end:s,min:o,max:a}}function yg(t,e,n,r){return ge(t)?Fw(t,e,n,r):e[n.axis]=n.parse(t,r),e}function yh(t,e,n,r){const i=t.iScale,s=t.vScale,o=i.getLabels(),a=i===s,l=[];let u,d,h,f;for(u=n,d=n+r;u<d;++u)f=e[u],h={},h[i.axis]=a||i.parse(o[u],u),l.push(yg(f,h,s,u));return l}function Xa(t){return t&&t.barStart!==void 0&&t.barEnd!==void 0}function zw(t,e,n){return t!==0?vn(t):(e.isHorizontal()?1:-1)*(e.min>=n?1:-1)}function Iw(t){let e,n,r,i,s;return t.horizontal?(e=t.base>t.x,n="left",r="right"):(e=t.base<t.y,n="bottom",r="top"),e?(i="end",s="start"):(i="start",s="end"),{start:n,end:r,reverse:e,top:i,bottom:s}}function Uw(t,e,n,r){let i=e.borderSkipped;const s={};if(!i){t.borderSkipped=s;return}if(i===!0){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:o,end:a,reverse:l,top:u,bottom:d}=Iw(t);i==="middle"&&n&&(t.enableBorderRadius=!0,(n._top||0)===r?i=u:(n._bottom||0)===r?i=d:(s[bh(d,o,a,l)]=!0,i=u)),s[bh(i,o,a,l)]=!0,t.borderSkipped=s}function bh(t,e,n,r){return r?(t=Bw(t,e,n),t=wh(t,n,e)):t=wh(t,e,n),t}function Bw(t,e,n){return t===e?n:t===n?e:t}function wh(t,e,n){return t==="start"?e:t==="end"?n:t}function Hw(t,{inflateAmount:e},n){t.inflateAmount=e==="auto"?n===1?.33:0:e}class Zs extends jr{parsePrimitiveData(e,n,r,i){return yh(e,n,r,i)}parseArrayData(e,n,r,i){return yh(e,n,r,i)}parseObjectData(e,n,r,i){const{iScale:s,vScale:o}=e,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,u=s.axis==="x"?a:l,d=o.axis==="x"?a:l,h=[];let f,m,x,v;for(f=r,m=r+i;f<m;++f)v=n[f],x={},x[s.axis]=s.parse(Kn(v,u),f),h.push(yg(Kn(v,d),x,o,f));return h}updateRangeFromParsed(e,n,r,i){super.updateRangeFromParsed(e,n,r,i);const s=r._custom;s&&n===this._cachedMeta.vScale&&(e.min=Math.min(e.min,s.min),e.max=Math.max(e.max,s.max))}getMaxOverflow(){return 0}getLabelAndValue(e){const n=this._cachedMeta,{iScale:r,vScale:i}=n,s=this.getParsed(e),o=s._custom,a=Xa(o)?"["+o.start+", "+o.end+"]":""+i.getLabelForValue(s[i.axis]);return{label:""+r.getLabelForValue(s[r.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const e=this._cachedMeta;e.stack=this.getDataset().stack}update(e){const n=this._cachedMeta;this.updateElements(n.data,0,n.data.length,e)}updateElements(e,n,r,i){const s=i==="reset",{index:o,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),u=a.isHorizontal(),d=this._getRuler(),{sharedOptions:h,includeOptions:f}=this._getSharedOptions(n,i);for(let m=n;m<n+r;m++){const x=this.getParsed(m),v=s||q(x[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(m),b=this._calculateBarIndexPixels(m,d),g=(x._stacks||{})[a.axis],p={horizontal:u,base:v.base,enableBorderRadius:!g||Xa(x._custom)||o===g._top||o===g._bottom,x:u?v.head:b.center,y:u?b.center:v.head,height:u?b.size:Math.abs(v.size),width:u?Math.abs(v.size):b.size};f&&(p.options=h||this.resolveDataElementOptions(m,e[m].active?"active":i));const y=p.options||e[m].options;Uw(p,y,g,o),Hw(p,y,d.ratio),this.updateElement(e[m],m,p,i)}}_getStacks(e,n){const{iScale:r}=this._cachedMeta,i=r.getMatchingVisibleMetas(this._type).filter(d=>d.controller.options.grouped),s=r.options.stacked,o=[],a=this._cachedMeta.controller.getParsed(n),l=a&&a[r.axis],u=d=>{const h=d._parsed.find(m=>m[r.axis]===l),f=h&&h[d.vScale.axis];if(q(f)||isNaN(f))return!0};for(const d of i)if(!(n!==void 0&&u(d))&&((s===!1||o.indexOf(d.stack)===-1||s===void 0&&d.stack===void 0)&&o.push(d.stack),d.index===e))break;return o.length||o.push(void 0),o}_getStackCount(e){return this._getStacks(void 0,e).length}_getStackIndex(e,n,r){const i=this._getStacks(e,r),s=n!==void 0?i.indexOf(n):-1;return s===-1?i.length-1:s}_getRuler(){const e=this.options,n=this._cachedMeta,r=n.iScale,i=[];let s,o;for(s=0,o=n.data.length;s<o;++s)i.push(r.getPixelForValue(this.getParsed(s)[r.axis],s));const a=e.barThickness;return{min:a||Ow(n),pixels:i,start:r._startPixel,end:r._endPixel,stackCount:this._getStackCount(),scale:r,grouped:e.grouped,ratio:a?1:e.categoryPercentage*e.barPercentage}}_calculateBarValuePixels(e){const{_cachedMeta:{vScale:n,_stacked:r,index:i},options:{base:s,minBarLength:o}}=this,a=s||0,l=this.getParsed(e),u=l._custom,d=Xa(u);let h=l[n.axis],f=0,m=r?this.applyStack(n,l,r):h,x,v;m!==h&&(f=m-h,m=h),d&&(h=u.barStart,m=u.barEnd-u.barStart,h!==0&&vn(h)!==vn(u.barEnd)&&(f=0),f+=h);const b=!q(s)&&!d?s:f;let g=n.getPixelForValue(b);if(this.chart.getDataVisibility(e)?x=n.getPixelForValue(f+m):x=g,v=x-g,Math.abs(v)<o){v=zw(v,n,a)*o,h===a&&(g-=v/2);const p=n.getPixelForDecimal(0),y=n.getPixelForDecimal(1),w=Math.min(p,y),_=Math.max(p,y);g=Math.max(Math.min(g,_),w),x=g+v,r&&!d&&(l._stacks[n.axis]._visualValues[i]=n.getValueForPixel(x)-n.getValueForPixel(g))}if(g===n.getPixelForValue(a)){const p=vn(v)*n.getLineWidthForValue(a)/2;g+=p,v-=p}return{size:v,base:g,head:x,center:x+v/2}}_calculateBarIndexPixels(e,n){const r=n.scale,i=this.options,s=i.skipNull,o=V(i.maxBarThickness,1/0);let a,l;if(n.grouped){const u=s?this._getStackCount(e):n.stackCount,d=i.barThickness==="flex"?Aw(e,n,i,u):Lw(e,n,i,u),h=this._getStackIndex(this.index,this._cachedMeta.stack,s?e:void 0);a=d.start+d.chunk*h+d.chunk/2,l=Math.min(o,d.chunk*d.ratio)}else a=r.getPixelForValue(this.getParsed(e)[r.axis],e),l=Math.min(o,n.min*n.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const e=this._cachedMeta,n=e.vScale,r=e.data,i=r.length;let s=0;for(;s<i;++s)this.getParsed(s)[n.axis]!==null&&!r[s].hidden&&r[s].draw(this._ctx)}}F(Zs,"id","bar"),F(Zs,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),F(Zs,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});function Ww(t,e,n){let r=1,i=1,s=0,o=0;if(e<he){const a=t,l=a+e,u=Math.cos(a),d=Math.sin(a),h=Math.cos(l),f=Math.sin(l),m=(y,w,_)=>Fo(y,a,l,!0)?1:Math.max(w,w*n,_,_*n),x=(y,w,_)=>Fo(y,a,l,!0)?-1:Math.min(w,w*n,_,_*n),v=m(0,u,h),b=m(ve,d,f),g=x(fe,u,h),p=x(fe+ve,d,f);r=(v-g)/2,i=(b-p)/2,s=-(v+g)/2,o=-(b+p)/2}return{ratioX:r,ratioY:i,offsetX:s,offsetY:o}}class di extends jr{constructor(e,n){super(e,n),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(e,n){const r=this.getDataset().data,i=this._cachedMeta;if(this._parsing===!1)i._parsed=r;else{let s=l=>+r[l];if(W(r[e])){const{key:l="value"}=this._parsing;s=u=>+Kn(r[u],l)}let o,a;for(o=e,a=e+n;o<a;++o)i._parsed[o]=s(o)}}_getRotation(){return It(this.options.rotation-90)}_getCircumference(){return It(this.options.circumference)}_getRotationExtents(){let e=he,n=-he;for(let r=0;r<this.chart.data.datasets.length;++r)if(this.chart.isDatasetVisible(r)&&this.chart.getDatasetMeta(r).type===this._type){const i=this.chart.getDatasetMeta(r).controller,s=i._getRotation(),o=i._getCircumference();e=Math.min(e,s),n=Math.max(n,s+o)}return{rotation:e,circumference:n-e}}update(e){const n=this.chart,{chartArea:r}=n,i=this._cachedMeta,s=i.data,o=this.getMaxBorderWidth()+this.getMaxOffset(s)+this.options.spacing,a=Math.max((Math.min(r.width,r.height)-o)/2,0),l=Math.min(xb(this.options.cutout,a),1),u=this._getRingWeight(this.index),{circumference:d,rotation:h}=this._getRotationExtents(),{ratioX:f,ratioY:m,offsetX:x,offsetY:v}=Ww(h,d,l),b=(r.width-o)/f,g=(r.height-o)/m,p=Math.max(Math.min(b,g)/2,0),y=Zp(this.options.radius,p),w=Math.max(y*l,0),_=(y-w)/this._getVisibleDatasetWeightTotal();this.offsetX=x*y,this.offsetY=v*y,i.total=this.calculateTotal(),this.outerRadius=y-_*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-_*u,0),this.updateElements(s,0,s.length,e)}_circumference(e,n){const r=this.options,i=this._cachedMeta,s=this._getCircumference();return n&&r.animation.animateRotate||!this.chart.getDataVisibility(e)||i._parsed[e]===null||i.data[e].hidden?0:this.calculateCircumference(i._parsed[e]*s/he)}updateElements(e,n,r,i){const s=i==="reset",o=this.chart,a=o.chartArea,u=o.options.animation,d=(a.left+a.right)/2,h=(a.top+a.bottom)/2,f=s&&u.animateScale,m=f?0:this.innerRadius,x=f?0:this.outerRadius,{sharedOptions:v,includeOptions:b}=this._getSharedOptions(n,i);let g=this._getRotation(),p;for(p=0;p<n;++p)g+=this._circumference(p,s);for(p=n;p<n+r;++p){const y=this._circumference(p,s),w=e[p],_={x:d+this.offsetX,y:h+this.offsetY,startAngle:g,endAngle:g+y,circumference:y,outerRadius:x,innerRadius:m};b&&(_.options=v||this.resolveDataElementOptions(p,w.active?"active":i)),g+=y,this.updateElement(w,p,_,i)}}calculateTotal(){const e=this._cachedMeta,n=e.data;let r=0,i;for(i=0;i<n.length;i++){const s=e._parsed[i];s!==null&&!isNaN(s)&&this.chart.getDataVisibility(i)&&!n[i].hidden&&(r+=Math.abs(s))}return r}calculateCircumference(e){const n=this._cachedMeta.total;return n>0&&!isNaN(e)?he*(Math.abs(e)/n):0}getLabelAndValue(e){const n=this._cachedMeta,r=this.chart,i=r.data.labels||[],s=uu(n._parsed[e],r.options.locale);return{label:i[e]||"",value:s}}getMaxBorderWidth(e){let n=0;const r=this.chart;let i,s,o,a,l;if(!e){for(i=0,s=r.data.datasets.length;i<s;++i)if(r.isDatasetVisible(i)){o=r.getDatasetMeta(i),e=o.data,a=o.controller;break}}if(!e)return 0;for(i=0,s=e.length;i<s;++i)l=a.resolveDataElementOptions(i),l.borderAlign!=="inner"&&(n=Math.max(n,l.borderWidth||0,l.hoverBorderWidth||0));return n}getMaxOffset(e){let n=0;for(let r=0,i=e.length;r<i;++r){const s=this.resolveDataElementOptions(r);n=Math.max(n,s.offset||0,s.hoverOffset||0)}return n}_getRingWeightOffset(e){let n=0;for(let r=0;r<e;++r)this.chart.isDatasetVisible(r)&&(n+=this._getRingWeight(r));return n}_getRingWeight(e){return Math.max(V(this.chart.data.datasets[e].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}F(di,"id","doughnut"),F(di,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),F(di,"descriptors",{_scriptable:e=>e!=="spacing",_indexable:e=>e!=="spacing"&&!e.startsWith("borderDash")&&!e.startsWith("hoverBorderDash")}),F(di,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(e){const n=e.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:r,color:i}}=e.legend.options;return n.labels.map((s,o)=>{const l=e.getDatasetMeta(0).controller.getStyle(o);return{text:s,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:i,lineWidth:l.borderWidth,pointStyle:r,hidden:!e.getDataVisibility(o),index:o}})}return[]}},onClick(e,n,r){r.chart.toggleDataVisibility(n.index),r.chart.update()}}}});function En(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class yu{constructor(e){F(this,"options");this.options=e||{}}static override(e){Object.assign(yu.prototype,e)}init(){}formats(){return En()}parse(){return En()}format(){return En()}add(){return En()}diff(){return En()}startOf(){return En()}endOf(){return En()}}var Vw={_date:yu};function Yw(t,e,n,r){const{controller:i,data:s,_sorted:o}=t,a=i._cachedMeta.iScale,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(a&&e===a.axis&&e!=="r"&&o&&s.length){const u=a._reversePixels?$b:tc;if(r){if(i._sharedOptions){const d=s[0],h=typeof d.getRange=="function"&&d.getRange(e);if(h){const f=u(s,e,n-h),m=u(s,e,n+h);return{lo:f.lo,hi:m.hi}}}}else{const d=u(s,e,n);if(l){const{vScale:h}=i._cachedMeta,{_parsed:f}=t,m=f.slice(0,d.lo+1).reverse().findIndex(v=>!q(v[h.axis]));d.lo-=Math.max(0,m);const x=f.slice(d.hi).findIndex(v=>!q(v[h.axis]));d.hi+=Math.max(0,x)}return d}}return{lo:0,hi:s.length-1}}function da(t,e,n,r,i){const s=t.getSortedVisibleDatasetMetas(),o=n[e];for(let a=0,l=s.length;a<l;++a){const{index:u,data:d}=s[a],{lo:h,hi:f}=Yw(s[a],e,o,i);for(let m=h;m<=f;++m){const x=d[m];x.skip||r(x,u,m)}}}function Xw(t){const e=t.indexOf("x")!==-1,n=t.indexOf("y")!==-1;return function(r,i){const s=e?Math.abs(r.x-i.x):0,o=n?Math.abs(r.y-i.y):0;return Math.sqrt(Math.pow(s,2)+Math.pow(o,2))}}function Ka(t,e,n,r,i){const s=[];return!i&&!t.isPointInArea(e)||da(t,n,e,function(a,l,u){!i&&!cg(a,t.chartArea,0)||a.inRange(e.x,e.y,r)&&s.push({element:a,datasetIndex:l,index:u})},!0),s}function Kw(t,e,n,r){let i=[];function s(o,a,l){const{startAngle:u,endAngle:d}=o.getProps(["startAngle","endAngle"],r),{angle:h}=tg(o,{x:e.x,y:e.y});Fo(h,u,d)&&i.push({element:o,datasetIndex:a,index:l})}return da(t,n,e,s),i}function Qw(t,e,n,r,i,s){let o=[];const a=Xw(n);let l=Number.POSITIVE_INFINITY;function u(d,h,f){const m=d.inRange(e.x,e.y,i);if(r&&!m)return;const x=d.getCenterPoint(i);if(!(!!s||t.isPointInArea(x))&&!m)return;const b=a(e,x);b<l?(o=[{element:d,datasetIndex:h,index:f}],l=b):b===l&&o.push({element:d,datasetIndex:h,index:f})}return da(t,n,e,u),o}function Qa(t,e,n,r,i,s){return!s&&!t.isPointInArea(e)?[]:n==="r"&&!r?Kw(t,e,n,i):Qw(t,e,n,r,i,s)}function Sh(t,e,n,r,i){const s=[],o=n==="x"?"inXRange":"inYRange";let a=!1;return da(t,n,e,(l,u,d)=>{l[o]&&l[o](e[n],i)&&(s.push({element:l,datasetIndex:u,index:d}),a=a||l.inRange(e.x,e.y,i))}),r&&!a?[]:s}var Gw={modes:{index(t,e,n,r){const i=Dn(e,t),s=n.axis||"x",o=n.includeInvisible||!1,a=n.intersect?Ka(t,i,s,r,o):Qa(t,i,s,!1,r,o),l=[];return a.length?(t.getSortedVisibleDatasetMetas().forEach(u=>{const d=a[0].index,h=u.data[d];h&&!h.skip&&l.push({element:h,datasetIndex:u.index,index:d})}),l):[]},dataset(t,e,n,r){const i=Dn(e,t),s=n.axis||"xy",o=n.includeInvisible||!1;let a=n.intersect?Ka(t,i,s,r,o):Qa(t,i,s,!1,r,o);if(a.length>0){const l=a[0].datasetIndex,u=t.getDatasetMeta(l).data;a=[];for(let d=0;d<u.length;++d)a.push({element:u[d],datasetIndex:l,index:d})}return a},point(t,e,n,r){const i=Dn(e,t),s=n.axis||"xy",o=n.includeInvisible||!1;return Ka(t,i,s,r,o)},nearest(t,e,n,r){const i=Dn(e,t),s=n.axis||"xy",o=n.includeInvisible||!1;return Qa(t,i,s,n.intersect,r,o)},x(t,e,n,r){const i=Dn(e,t);return Sh(t,i,"x",n.intersect,r)},y(t,e,n,r){const i=Dn(e,t);return Sh(t,i,"y",n.intersect,r)}}};const bg=["left","top","right","bottom"];function ni(t,e){return t.filter(n=>n.pos===e)}function _h(t,e){return t.filter(n=>bg.indexOf(n.pos)===-1&&n.box.axis===e)}function ri(t,e){return t.sort((n,r)=>{const i=e?r:n,s=e?n:r;return i.weight===s.weight?i.index-s.index:i.weight-s.weight})}function qw(t){const e=[];let n,r,i,s,o,a;for(n=0,r=(t||[]).length;n<r;++n)i=t[n],{position:s,options:{stack:o,stackWeight:a=1}}=i,e.push({index:n,box:i,pos:s,horizontal:i.isHorizontal(),weight:i.weight,stack:o&&s+o,stackWeight:a});return e}function Zw(t){const e={};for(const n of t){const{stack:r,pos:i,stackWeight:s}=n;if(!r||!bg.includes(i))continue;const o=e[r]||(e[r]={count:0,placed:0,weight:0,size:0});o.count++,o.weight+=s}return e}function Jw(t,e){const n=Zw(t),{vBoxMaxWidth:r,hBoxMaxHeight:i}=e;let s,o,a;for(s=0,o=t.length;s<o;++s){a=t[s];const{fullSize:l}=a.box,u=n[a.stack],d=u&&a.stackWeight/u.weight;a.horizontal?(a.width=d?d*r:l&&e.availableWidth,a.height=i):(a.width=r,a.height=d?d*i:l&&e.availableHeight)}return n}function e2(t){const e=qw(t),n=ri(e.filter(u=>u.box.fullSize),!0),r=ri(ni(e,"left"),!0),i=ri(ni(e,"right")),s=ri(ni(e,"top"),!0),o=ri(ni(e,"bottom")),a=_h(e,"x"),l=_h(e,"y");return{fullSize:n,leftAndTop:r.concat(s),rightAndBottom:i.concat(l).concat(o).concat(a),chartArea:ni(e,"chartArea"),vertical:r.concat(i).concat(l),horizontal:s.concat(o).concat(a)}}function kh(t,e,n,r){return Math.max(t[n],e[n])+Math.max(t[r],e[r])}function wg(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function t2(t,e,n,r){const{pos:i,box:s}=n,o=t.maxPadding;if(!W(i)){n.size&&(t[i]-=n.size);const h=r[n.stack]||{size:0,count:1};h.size=Math.max(h.size,n.horizontal?s.height:s.width),n.size=h.size/h.count,t[i]+=n.size}s.getPadding&&wg(o,s.getPadding());const a=Math.max(0,e.outerWidth-kh(o,t,"left","right")),l=Math.max(0,e.outerHeight-kh(o,t,"top","bottom")),u=a!==t.w,d=l!==t.h;return t.w=a,t.h=l,n.horizontal?{same:u,other:d}:{same:d,other:u}}function n2(t){const e=t.maxPadding;function n(r){const i=Math.max(e[r]-t[r],0);return t[r]+=i,i}t.y+=n("top"),t.x+=n("left"),n("right"),n("bottom")}function r2(t,e){const n=e.maxPadding;function r(i){const s={left:0,top:0,right:0,bottom:0};return i.forEach(o=>{s[o]=Math.max(e[o],n[o])}),s}return r(t?["left","right"]:["top","bottom"])}function hi(t,e,n,r){const i=[];let s,o,a,l,u,d;for(s=0,o=t.length,u=0;s<o;++s){a=t[s],l=a.box,l.update(a.width||e.w,a.height||e.h,r2(a.horizontal,e));const{same:h,other:f}=t2(e,n,a,r);u|=h&&i.length,d=d||f,l.fullSize||i.push(a)}return u&&hi(i,e,n,r)||d}function Rs(t,e,n,r,i){t.top=n,t.left=e,t.right=e+r,t.bottom=n+i,t.width=r,t.height=i}function Nh(t,e,n,r){const i=n.padding;let{x:s,y:o}=e;for(const a of t){const l=a.box,u=r[a.stack]||{placed:0,weight:1},d=a.stackWeight/u.weight||1;if(a.horizontal){const h=e.w*d,f=u.size||l.height;Qi(u.start)&&(o=u.start),l.fullSize?Rs(l,i.left,o,n.outerWidth-i.right-i.left,f):Rs(l,e.left+u.placed,o,h,f),u.start=o,u.placed+=h,o=l.bottom}else{const h=e.h*d,f=u.size||l.width;Qi(u.start)&&(s=u.start),l.fullSize?Rs(l,s,i.top,f,n.outerHeight-i.bottom-i.top):Rs(l,s,e.top+u.placed,f,h),u.start=s,u.placed+=h,s=l.right}}e.x=s,e.y=o}var it={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(n){e.draw(n)}}]},t.boxes.push(e)},removeBox(t,e){const n=t.boxes?t.boxes.indexOf(e):-1;n!==-1&&t.boxes.splice(n,1)},configure(t,e,n){e.fullSize=n.fullSize,e.position=n.position,e.weight=n.weight},update(t,e,n,r){if(!t)return;const i=ct(t.options.layout.padding),s=Math.max(e-i.width,0),o=Math.max(n-i.height,0),a=e2(t.boxes),l=a.vertical,u=a.horizontal;X(t.boxes,v=>{typeof v.beforeLayout=="function"&&v.beforeLayout()});const d=l.reduce((v,b)=>b.box.options&&b.box.options.display===!1?v:v+1,0)||1,h=Object.freeze({outerWidth:e,outerHeight:n,padding:i,availableWidth:s,availableHeight:o,vBoxMaxWidth:s/2/d,hBoxMaxHeight:o/2}),f=Object.assign({},i);wg(f,ct(r));const m=Object.assign({maxPadding:f,w:s,h:o,x:i.left,y:i.top},i),x=Jw(l.concat(u),h);hi(a.fullSize,m,h,x),hi(l,m,h,x),hi(u,m,h,x)&&hi(l,m,h,x),n2(m),Nh(a.leftAndTop,m,h,x),m.x+=m.w,m.y+=m.h,Nh(a.rightAndBottom,m,h,x),t.chartArea={left:m.left,top:m.top,right:m.left+m.w,bottom:m.top+m.h,height:m.h,width:m.w},X(a.chartArea,v=>{const b=v.box;Object.assign(b,t.chartArea),b.update(m.w,m.h,{left:0,top:0,right:0,bottom:0})})}};class Sg{acquireContext(e,n){}releaseContext(e){return!1}addEventListener(e,n,r){}removeEventListener(e,n,r){}getDevicePixelRatio(){return 1}getMaximumSize(e,n,r,i){return n=Math.max(0,n||e.width),r=r||e.height,{width:n,height:Math.max(0,i?Math.floor(n/i):r)}}isAttached(e){return!0}updateConfig(e){}}class i2 extends Sg{acquireContext(e){return e&&e.getContext&&e.getContext("2d")||null}updateConfig(e){e.options.animation=!1}}const Js="$chartjs",s2={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},jh=t=>t===null||t==="";function o2(t,e){const n=t.style,r=t.getAttribute("height"),i=t.getAttribute("width");if(t[Js]={initial:{height:r,width:i,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",n.boxSizing=n.boxSizing||"border-box",jh(i)){const s=hh(t,"width");s!==void 0&&(t.width=s)}if(jh(r))if(t.style.height==="")t.height=t.width/(e||2);else{const s=hh(t,"height");s!==void 0&&(t.height=s)}return t}const _g=pw?{passive:!0}:!1;function a2(t,e,n){t&&t.addEventListener(e,n,_g)}function l2(t,e,n){t&&t.canvas&&t.canvas.removeEventListener(e,n,_g)}function c2(t,e){const n=s2[t.type]||t.type,{x:r,y:i}=Dn(t,e);return{type:n,chart:e,native:t,x:r!==void 0?r:null,y:i!==void 0?i:null}}function Uo(t,e){for(const n of t)if(n===e||n.contains(e))return!0}function u2(t,e,n){const r=t.canvas,i=new MutationObserver(s=>{let o=!1;for(const a of s)o=o||Uo(a.addedNodes,r),o=o&&!Uo(a.removedNodes,r);o&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}function d2(t,e,n){const r=t.canvas,i=new MutationObserver(s=>{let o=!1;for(const a of s)o=o||Uo(a.removedNodes,r),o=o&&!Uo(a.addedNodes,r);o&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}const qi=new Map;let Ch=0;function kg(){const t=window.devicePixelRatio;t!==Ch&&(Ch=t,qi.forEach((e,n)=>{n.currentDevicePixelRatio!==t&&e()}))}function h2(t,e){qi.size||window.addEventListener("resize",kg),qi.set(t,e)}function f2(t){qi.delete(t),qi.size||window.removeEventListener("resize",kg)}function m2(t,e,n){const r=t.canvas,i=r&&vu(r);if(!i)return;const s=sg((a,l)=>{const u=i.clientWidth;n(a,l),u<i.clientWidth&&n()},window),o=new ResizeObserver(a=>{const l=a[0],u=l.contentRect.width,d=l.contentRect.height;u===0&&d===0||s(u,d)});return o.observe(i),h2(t,s),o}function Ga(t,e,n){n&&n.disconnect(),e==="resize"&&f2(t)}function p2(t,e,n){const r=t.canvas,i=sg(s=>{t.ctx!==null&&n(c2(s,t))},t);return a2(r,e,i),i}class g2 extends Sg{acquireContext(e,n){const r=e&&e.getContext&&e.getContext("2d");return r&&r.canvas===e?(o2(e,n),r):null}releaseContext(e){const n=e.canvas;if(!n[Js])return!1;const r=n[Js].initial;["height","width"].forEach(s=>{const o=r[s];q(o)?n.removeAttribute(s):n.setAttribute(s,o)});const i=r.style||{};return Object.keys(i).forEach(s=>{n.style[s]=i[s]}),n.width=n.width,delete n[Js],!0}addEventListener(e,n,r){this.removeEventListener(e,n);const i=e.$proxies||(e.$proxies={}),o={attach:u2,detach:d2,resize:m2}[n]||p2;i[n]=o(e,n,r)}removeEventListener(e,n){const r=e.$proxies||(e.$proxies={}),i=r[n];if(!i)return;({attach:Ga,detach:Ga,resize:Ga}[n]||l2)(e,n,i),r[n]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(e,n,r,i){return mw(e,n,r,i)}isAttached(e){const n=e&&vu(e);return!!(n&&n.isConnected)}}function x2(t){return!xu()||typeof OffscreenCanvas<"u"&&t instanceof OffscreenCanvas?i2:g2}class Yt{constructor(){F(this,"x");F(this,"y");F(this,"active",!1);F(this,"options");F(this,"$animations")}tooltipPosition(e){const{x:n,y:r}=this.getProps(["x","y"],e);return{x:n,y:r}}hasValue(){return Ao(this.x)&&Ao(this.y)}getProps(e,n){const r=this.$animations;if(!n||!r)return this;const i={};return e.forEach(s=>{i[s]=r[s]&&r[s].active()?r[s]._to:this[s]}),i}}F(Yt,"defaults",{}),F(Yt,"defaultRoutes");function v2(t,e){const n=t.options.ticks,r=y2(t),i=Math.min(n.maxTicksLimit||r,r),s=n.major.enabled?w2(e):[],o=s.length,a=s[0],l=s[o-1],u=[];if(o>i)return S2(e,u,s,o/i),u;const d=b2(s,e,i);if(o>0){let h,f;const m=o>1?Math.round((l-a)/(o-1)):null;for(Ds(e,u,d,q(m)?0:a-m,a),h=0,f=o-1;h<f;h++)Ds(e,u,d,s[h],s[h+1]);return Ds(e,u,d,l,q(m)?e.length:l+m),u}return Ds(e,u,d),u}function y2(t){const e=t.options.offset,n=t._tickSize(),r=t._length/n+(e?0:1),i=t._maxLength/n;return Math.floor(Math.min(r,i))}function b2(t,e,n){const r=_2(t),i=e.length/n;if(!r)return Math.max(i,1);const s=kb(r);for(let o=0,a=s.length-1;o<a;o++){const l=s[o];if(l>i)return l}return Math.max(i,1)}function w2(t){const e=[];let n,r;for(n=0,r=t.length;n<r;n++)t[n].major&&e.push(n);return e}function S2(t,e,n,r){let i=0,s=n[0],o;for(r=Math.ceil(r),o=0;o<t.length;o++)o===s&&(e.push(t[o]),i++,s=n[i*r])}function Ds(t,e,n,r,i){const s=V(r,0),o=Math.min(V(i,t.length),t.length);let a=0,l,u,d;for(n=Math.ceil(n),i&&(l=i-r,n=l/Math.floor(l/n)),d=s;d<0;)a++,d=Math.round(s+a*n);for(u=Math.max(s,0);u<o;u++)u===d&&(e.push(t[u]),a++,d=Math.round(s+a*n))}function _2(t){const e=t.length;let n,r;if(e<2)return!1;for(r=t[0],n=1;n<e;++n)if(t[n]-t[n-1]!==r)return!1;return r}const k2=t=>t==="left"?"right":t==="right"?"left":t,Ph=(t,e,n)=>e==="top"||e==="left"?t[e]+n:t[e]-n,Eh=(t,e)=>Math.min(e||t,t);function Mh(t,e){const n=[],r=t.length/e,i=t.length;let s=0;for(;s<i;s+=r)n.push(t[Math.floor(s)]);return n}function N2(t,e,n){const r=t.ticks.length,i=Math.min(e,r-1),s=t._startPixel,o=t._endPixel,a=1e-6;let l=t.getPixelForTick(i),u;if(!(n&&(r===1?u=Math.max(l-s,o-l):e===0?u=(t.getPixelForTick(1)-l)/2:u=(l-t.getPixelForTick(i-1))/2,l+=i<e?u:-u,l<s-a||l>o+a)))return l}function j2(t,e){X(t,n=>{const r=n.gc,i=r.length/2;let s;if(i>e){for(s=0;s<i;++s)delete n.data[r[s]];r.splice(0,i)}})}function ii(t){return t.drawTicks?t.tickLength:0}function $h(t,e){if(!t.display)return 0;const n=Pe(t.font,e),r=ct(t.padding);return(ge(t.text)?t.text.length:1)*n.lineHeight+r.height}function C2(t,e){return Hr(t,{scale:e,type:"scale"})}function P2(t,e,n){return Hr(t,{tick:n,index:e,type:"tick"})}function E2(t,e,n){let r=cu(t);return(n&&e!=="right"||!n&&e==="right")&&(r=k2(r)),r}function M2(t,e,n,r){const{top:i,left:s,bottom:o,right:a,chart:l}=t,{chartArea:u,scales:d}=l;let h=0,f,m,x;const v=o-i,b=a-s;if(t.isHorizontal()){if(m=je(r,s,a),W(n)){const g=Object.keys(n)[0],p=n[g];x=d[g].getPixelForValue(p)+v-e}else n==="center"?x=(u.bottom+u.top)/2+v-e:x=Ph(t,n,e);f=a-s}else{if(W(n)){const g=Object.keys(n)[0],p=n[g];m=d[g].getPixelForValue(p)-b+e}else n==="center"?m=(u.left+u.right)/2-b+e:m=Ph(t,n,e);x=je(r,o,i),h=n==="left"?-ve:ve}return{titleX:m,titleY:x,maxWidth:f,rotation:h}}class Wr extends Yt{constructor(e){super(),this.id=e.id,this.type=e.type,this.options=void 0,this.ctx=e.ctx,this.chart=e.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(e){this.options=e.setContext(this.getContext()),this.axis=e.axis,this._userMin=this.parse(e.min),this._userMax=this.parse(e.max),this._suggestedMin=this.parse(e.suggestedMin),this._suggestedMax=this.parse(e.suggestedMax)}parse(e,n){return e}getUserBounds(){let{_userMin:e,_userMax:n,_suggestedMin:r,_suggestedMax:i}=this;return e=wt(e,Number.POSITIVE_INFINITY),n=wt(n,Number.NEGATIVE_INFINITY),r=wt(r,Number.POSITIVE_INFINITY),i=wt(i,Number.NEGATIVE_INFINITY),{min:wt(e,r),max:wt(n,i),minDefined:lt(e),maxDefined:lt(n)}}getMinMax(e){let{min:n,max:r,minDefined:i,maxDefined:s}=this.getUserBounds(),o;if(i&&s)return{min:n,max:r};const a=this.getMatchingVisibleMetas();for(let l=0,u=a.length;l<u;++l)o=a[l].controller.getMinMax(this,e),i||(n=Math.min(n,o.min)),s||(r=Math.max(r,o.max));return n=s&&n>r?r:n,r=i&&n>r?n:r,{min:wt(n,wt(r,n)),max:wt(r,wt(n,r))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]}getLabelItems(e=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(e))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){ee(this.options.beforeUpdate,[this])}update(e,n,r){const{beginAtZero:i,grace:s,ticks:o}=this.options,a=o.sampleSize;this.beforeUpdate(),this.maxWidth=e,this.maxHeight=n,this._margins=r=Object.assign({left:0,right:0,top:0,bottom:0},r),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+r.left+r.right:this.height+r.top+r.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Jb(this,s,i),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?Mh(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),o.display&&(o.autoSkip||o.source==="auto")&&(this.ticks=v2(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let e=this.options.reverse,n,r;this.isHorizontal()?(n=this.left,r=this.right):(n=this.top,r=this.bottom,e=!e),this._startPixel=n,this._endPixel=r,this._reversePixels=e,this._length=r-n,this._alignToPixels=this.options.alignToPixels}afterUpdate(){ee(this.options.afterUpdate,[this])}beforeSetDimensions(){ee(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){ee(this.options.afterSetDimensions,[this])}_callHooks(e){this.chart.notifyPlugins(e,this.getContext()),ee(this.options[e],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){ee(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(e){const n=this.options.ticks;let r,i,s;for(r=0,i=e.length;r<i;r++)s=e[r],s.label=ee(n.callback,[s.value,r,e],this)}afterTickToLabelConversion(){ee(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){ee(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const e=this.options,n=e.ticks,r=Eh(this.ticks.length,e.ticks.maxTicksLimit),i=n.minRotation||0,s=n.maxRotation;let o=i,a,l,u;if(!this._isVisible()||!n.display||i>=s||r<=1||!this.isHorizontal()){this.labelRotation=i;return}const d=this._getLabelSizes(),h=d.widest.width,f=d.highest.height,m=Ue(this.chart.width-h,0,this.maxWidth);a=e.offset?this.maxWidth/r:m/(r-1),h+6>a&&(a=m/(r-(e.offset?.5:1)),l=this.maxHeight-ii(e.grid)-n.padding-$h(e.title,this.chart.options.font),u=Math.sqrt(h*h+f*f),o=Pb(Math.min(Math.asin(Ue((d.highest.height+6)/a,-1,1)),Math.asin(Ue(l/u,-1,1))-Math.asin(Ue(f/u,-1,1)))),o=Math.max(i,Math.min(s,o))),this.labelRotation=o}afterCalculateLabelRotation(){ee(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){ee(this.options.beforeFit,[this])}fit(){const e={width:0,height:0},{chart:n,options:{ticks:r,title:i,grid:s}}=this,o=this._isVisible(),a=this.isHorizontal();if(o){const l=$h(i,n.options.font);if(a?(e.width=this.maxWidth,e.height=ii(s)+l):(e.height=this.maxHeight,e.width=ii(s)+l),r.display&&this.ticks.length){const{first:u,last:d,widest:h,highest:f}=this._getLabelSizes(),m=r.padding*2,x=It(this.labelRotation),v=Math.cos(x),b=Math.sin(x);if(a){const g=r.mirror?0:b*h.width+v*f.height;e.height=Math.min(this.maxHeight,e.height+g+m)}else{const g=r.mirror?0:v*h.width+b*f.height;e.width=Math.min(this.maxWidth,e.width+g+m)}this._calculatePadding(u,d,b,v)}}this._handleMargins(),a?(this.width=this._length=n.width-this._margins.left-this._margins.right,this.height=e.height):(this.width=e.width,this.height=this._length=n.height-this._margins.top-this._margins.bottom)}_calculatePadding(e,n,r,i){const{ticks:{align:s,padding:o},position:a}=this.options,l=this.labelRotation!==0,u=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const d=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1);let f=0,m=0;l?u?(f=i*e.width,m=r*n.height):(f=r*e.height,m=i*n.width):s==="start"?m=n.width:s==="end"?f=e.width:s!=="inner"&&(f=e.width/2,m=n.width/2),this.paddingLeft=Math.max((f-d+o)*this.width/(this.width-d),0),this.paddingRight=Math.max((m-h+o)*this.width/(this.width-h),0)}else{let d=n.height/2,h=e.height/2;s==="start"?(d=0,h=e.height):s==="end"&&(d=n.height,h=0),this.paddingTop=d+o,this.paddingBottom=h+o}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){ee(this.options.afterFit,[this])}isHorizontal(){const{axis:e,position:n}=this.options;return n==="top"||n==="bottom"||e==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(e){this.beforeTickToLabelConversion(),this.generateTickLabels(e);let n,r;for(n=0,r=e.length;n<r;n++)q(e[n].label)&&(e.splice(n,1),r--,n--);this.afterTickToLabelConversion()}_getLabelSizes(){let e=this._labelSizes;if(!e){const n=this.options.ticks.sampleSize;let r=this.ticks;n<r.length&&(r=Mh(r,n)),this._labelSizes=e=this._computeLabelSizes(r,r.length,this.options.ticks.maxTicksLimit)}return e}_computeLabelSizes(e,n,r){const{ctx:i,_longestTextCache:s}=this,o=[],a=[],l=Math.floor(n/Eh(n,r));let u=0,d=0,h,f,m,x,v,b,g,p,y,w,_;for(h=0;h<n;h+=l){if(x=e[h].label,v=this._resolveTickFontOptions(h),i.font=b=v.string,g=s[b]=s[b]||{data:{},gc:[]},p=v.lineHeight,y=w=0,!q(x)&&!ge(x))y=oh(i,g.data,g.gc,y,x),w=p;else if(ge(x))for(f=0,m=x.length;f<m;++f)_=x[f],!q(_)&&!ge(_)&&(y=oh(i,g.data,g.gc,y,_),w+=p);o.push(y),a.push(w),u=Math.max(y,u),d=Math.max(w,d)}j2(s,n);const S=o.indexOf(u),k=a.indexOf(d),j=E=>({width:o[E]||0,height:a[E]||0});return{first:j(0),last:j(n-1),widest:j(S),highest:j(k),widths:o,heights:a}}getLabelForValue(e){return e}getPixelForValue(e,n){return NaN}getValueForPixel(e){}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getPixelForDecimal(e){this._reversePixels&&(e=1-e);const n=this._startPixel+e*this._length;return Mb(this._alignToPixels?Pn(this.chart,n,0):n)}getDecimalForPixel(e){const n=(e-this._startPixel)/this._length;return this._reversePixels?1-n:n}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:e,max:n}=this;return e<0&&n<0?n:e>0&&n>0?e:0}getContext(e){const n=this.ticks||[];if(e>=0&&e<n.length){const r=n[e];return r.$context||(r.$context=P2(this.getContext(),e,r))}return this.$context||(this.$context=C2(this.chart.getContext(),this))}_tickSize(){const e=this.options.ticks,n=It(this.labelRotation),r=Math.abs(Math.cos(n)),i=Math.abs(Math.sin(n)),s=this._getLabelSizes(),o=e.autoSkipPadding||0,a=s?s.widest.width+o:0,l=s?s.highest.height+o:0;return this.isHorizontal()?l*r>a*i?a/r:l/i:l*i<a*r?l/r:a/i}_isVisible(){const e=this.options.display;return e!=="auto"?!!e:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(e){const n=this.axis,r=this.chart,i=this.options,{grid:s,position:o,border:a}=i,l=s.offset,u=this.isHorizontal(),h=this.ticks.length+(l?1:0),f=ii(s),m=[],x=a.setContext(this.getContext()),v=x.display?x.width:0,b=v/2,g=function(B){return Pn(r,B,v)};let p,y,w,_,S,k,j,E,C,$,D,T;if(o==="top")p=g(this.bottom),k=this.bottom-f,E=p-b,$=g(e.top)+b,T=e.bottom;else if(o==="bottom")p=g(this.top),$=e.top,T=g(e.bottom)-b,k=p+b,E=this.top+f;else if(o==="left")p=g(this.right),S=this.right-f,j=p-b,C=g(e.left)+b,D=e.right;else if(o==="right")p=g(this.left),C=e.left,D=g(e.right)-b,S=p+b,j=this.left+f;else if(n==="x"){if(o==="center")p=g((e.top+e.bottom)/2+.5);else if(W(o)){const B=Object.keys(o)[0],R=o[B];p=g(this.chart.scales[B].getPixelForValue(R))}$=e.top,T=e.bottom,k=p+b,E=k+f}else if(n==="y"){if(o==="center")p=g((e.left+e.right)/2);else if(W(o)){const B=Object.keys(o)[0],R=o[B];p=g(this.chart.scales[B].getPixelForValue(R))}S=p-b,j=S-f,C=e.left,D=e.right}const U=V(i.ticks.maxTicksLimit,h),z=Math.max(1,Math.ceil(h/U));for(y=0;y<h;y+=z){const B=this.getContext(y),R=s.setContext(B),M=a.setContext(B),L=R.lineWidth,A=R.color,Q=M.dash||[],G=M.dashOffset,yt=R.tickWidth,Me=R.tickColor,$t=R.tickBorderDash||[],$e=R.tickBorderDashOffset;w=N2(this,y,l),w!==void 0&&(_=Pn(r,w,L),u?S=j=C=D=_:k=E=$=T=_,m.push({tx1:S,ty1:k,tx2:j,ty2:E,x1:C,y1:$,x2:D,y2:T,width:L,color:A,borderDash:Q,borderDashOffset:G,tickWidth:yt,tickColor:Me,tickBorderDash:$t,tickBorderDashOffset:$e}))}return this._ticksLength=h,this._borderValue=p,m}_computeLabelItems(e){const n=this.axis,r=this.options,{position:i,ticks:s}=r,o=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:u,padding:d,mirror:h}=s,f=ii(r.grid),m=f+d,x=h?-d:m,v=-It(this.labelRotation),b=[];let g,p,y,w,_,S,k,j,E,C,$,D,T="middle";if(i==="top")S=this.bottom-x,k=this._getXAxisLabelAlignment();else if(i==="bottom")S=this.top+x,k=this._getXAxisLabelAlignment();else if(i==="left"){const z=this._getYAxisLabelAlignment(f);k=z.textAlign,_=z.x}else if(i==="right"){const z=this._getYAxisLabelAlignment(f);k=z.textAlign,_=z.x}else if(n==="x"){if(i==="center")S=(e.top+e.bottom)/2+m;else if(W(i)){const z=Object.keys(i)[0],B=i[z];S=this.chart.scales[z].getPixelForValue(B)+m}k=this._getXAxisLabelAlignment()}else if(n==="y"){if(i==="center")_=(e.left+e.right)/2-m;else if(W(i)){const z=Object.keys(i)[0],B=i[z];_=this.chart.scales[z].getPixelForValue(B)}k=this._getYAxisLabelAlignment(f).textAlign}n==="y"&&(l==="start"?T="top":l==="end"&&(T="bottom"));const U=this._getLabelSizes();for(g=0,p=a.length;g<p;++g){y=a[g],w=y.label;const z=s.setContext(this.getContext(g));j=this.getPixelForTick(g)+s.labelOffset,E=this._resolveTickFontOptions(g),C=E.lineHeight,$=ge(w)?w.length:1;const B=$/2,R=z.color,M=z.textStrokeColor,L=z.textStrokeWidth;let A=k;o?(_=j,k==="inner"&&(g===p-1?A=this.options.reverse?"left":"right":g===0?A=this.options.reverse?"right":"left":A="center"),i==="top"?u==="near"||v!==0?D=-$*C+C/2:u==="center"?D=-U.highest.height/2-B*C+C:D=-U.highest.height+C/2:u==="near"||v!==0?D=C/2:u==="center"?D=U.highest.height/2-B*C:D=U.highest.height-$*C,h&&(D*=-1),v!==0&&!z.showLabelBackdrop&&(_+=C/2*Math.sin(v))):(S=j,D=(1-$)*C/2);let Q;if(z.showLabelBackdrop){const G=ct(z.backdropPadding),yt=U.heights[g],Me=U.widths[g];let $t=D-G.top,$e=0-G.left;switch(T){case"middle":$t-=yt/2;break;case"bottom":$t-=yt;break}switch(k){case"center":$e-=Me/2;break;case"right":$e-=Me;break;case"inner":g===p-1?$e-=Me:g>0&&($e-=Me/2);break}Q={left:$e,top:$t,width:Me+G.width,height:yt+G.height,color:z.backdropColor}}b.push({label:w,font:E,textOffset:D,options:{rotation:v,color:R,strokeColor:M,strokeWidth:L,textAlign:A,textBaseline:T,translation:[_,S],backdrop:Q}})}return b}_getXAxisLabelAlignment(){const{position:e,ticks:n}=this.options;if(-It(this.labelRotation))return e==="top"?"left":"right";let i="center";return n.align==="start"?i="left":n.align==="end"?i="right":n.align==="inner"&&(i="inner"),i}_getYAxisLabelAlignment(e){const{position:n,ticks:{crossAlign:r,mirror:i,padding:s}}=this.options,o=this._getLabelSizes(),a=e+s,l=o.widest.width;let u,d;return n==="left"?i?(d=this.right+s,r==="near"?u="left":r==="center"?(u="center",d+=l/2):(u="right",d+=l)):(d=this.right-a,r==="near"?u="right":r==="center"?(u="center",d-=l/2):(u="left",d=this.left)):n==="right"?i?(d=this.left+s,r==="near"?u="right":r==="center"?(u="center",d-=l/2):(u="left",d-=l)):(d=this.left+a,r==="near"?u="left":r==="center"?(u="center",d+=l/2):(u="right",d=this.right)):u="right",{textAlign:u,x:d}}_computeLabelArea(){if(this.options.ticks.mirror)return;const e=this.chart,n=this.options.position;if(n==="left"||n==="right")return{top:0,left:this.left,bottom:e.height,right:this.right};if(n==="top"||n==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:e.width}}drawBackground(){const{ctx:e,options:{backgroundColor:n},left:r,top:i,width:s,height:o}=this;n&&(e.save(),e.fillStyle=n,e.fillRect(r,i,s,o),e.restore())}getLineWidthForValue(e){const n=this.options.grid;if(!this._isVisible()||!n.display)return 0;const i=this.ticks.findIndex(s=>s.value===e);return i>=0?n.setContext(this.getContext(i)).lineWidth:0}drawGrid(e){const n=this.options.grid,r=this.ctx,i=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(e));let s,o;const a=(l,u,d)=>{!d.width||!d.color||(r.save(),r.lineWidth=d.width,r.strokeStyle=d.color,r.setLineDash(d.borderDash||[]),r.lineDashOffset=d.borderDashOffset,r.beginPath(),r.moveTo(l.x,l.y),r.lineTo(u.x,u.y),r.stroke(),r.restore())};if(n.display)for(s=0,o=i.length;s<o;++s){const l=i[s];n.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),n.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:e,ctx:n,options:{border:r,grid:i}}=this,s=r.setContext(this.getContext()),o=r.display?s.width:0;if(!o)return;const a=i.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let u,d,h,f;this.isHorizontal()?(u=Pn(e,this.left,o)-o/2,d=Pn(e,this.right,a)+a/2,h=f=l):(h=Pn(e,this.top,o)-o/2,f=Pn(e,this.bottom,a)+a/2,u=d=l),n.save(),n.lineWidth=s.width,n.strokeStyle=s.color,n.beginPath(),n.moveTo(u,h),n.lineTo(d,f),n.stroke(),n.restore()}drawLabels(e){if(!this.options.ticks.display)return;const r=this.ctx,i=this._computeLabelArea();i&&du(r,i);const s=this.getLabelItems(e);for(const o of s){const a=o.options,l=o.font,u=o.label,d=o.textOffset;Gi(r,u,0,d,l,a)}i&&hu(r)}drawTitle(){const{ctx:e,options:{position:n,title:r,reverse:i}}=this;if(!r.display)return;const s=Pe(r.font),o=ct(r.padding),a=r.align;let l=s.lineHeight/2;n==="bottom"||n==="center"||W(n)?(l+=o.bottom,ge(r.text)&&(l+=s.lineHeight*(r.text.length-1))):l+=o.top;const{titleX:u,titleY:d,maxWidth:h,rotation:f}=M2(this,l,n,a);Gi(e,r.text,0,0,s,{color:r.color,maxWidth:h,rotation:f,textAlign:E2(a,n,i),textBaseline:"middle",translation:[u,d]})}draw(e){this._isVisible()&&(this.drawBackground(),this.drawGrid(e),this.drawBorder(),this.drawTitle(),this.drawLabels(e))}_layers(){const e=this.options,n=e.ticks&&e.ticks.z||0,r=V(e.grid&&e.grid.z,-1),i=V(e.border&&e.border.z,0);return!this._isVisible()||this.draw!==Wr.prototype.draw?[{z:n,draw:s=>{this.draw(s)}}]:[{z:r,draw:s=>{this.drawBackground(),this.drawGrid(s),this.drawTitle()}},{z:i,draw:()=>{this.drawBorder()}},{z:n,draw:s=>{this.drawLabels(s)}}]}getMatchingVisibleMetas(e){const n=this.chart.getSortedVisibleDatasetMetas(),r=this.axis+"AxisID",i=[];let s,o;for(s=0,o=n.length;s<o;++s){const a=n[s];a[r]===this.id&&(!e||a.type===e)&&i.push(a)}return i}_resolveTickFontOptions(e){const n=this.options.ticks.setContext(this.getContext(e));return Pe(n.font)}_maxDigits(){const e=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/e}}class Os{constructor(e,n,r){this.type=e,this.scope=n,this.override=r,this.items=Object.create(null)}isForType(e){return Object.prototype.isPrototypeOf.call(this.type.prototype,e.prototype)}register(e){const n=Object.getPrototypeOf(e);let r;R2(n)&&(r=this.register(n));const i=this.items,s=e.id,o=this.scope+"."+s;if(!s)throw new Error("class does not have id: "+e);return s in i||(i[s]=e,$2(e,o,r),this.override&&ue.override(e.id,e.overrides)),o}get(e){return this.items[e]}unregister(e){const n=this.items,r=e.id,i=this.scope;r in n&&delete n[r],i&&r in ue[i]&&(delete ue[i][r],this.override&&delete Qn[r])}}function $2(t,e,n){const r=Ki(Object.create(null),[n?ue.get(n):{},ue.get(e),t.defaults]);ue.set(e,r),t.defaultRoutes&&T2(e,t.defaultRoutes),t.descriptors&&ue.describe(e,t.descriptors)}function T2(t,e){Object.keys(e).forEach(n=>{const r=n.split("."),i=r.pop(),s=[t].concat(r).join("."),o=e[n].split("."),a=o.pop(),l=o.join(".");ue.route(s,i,l,a)})}function R2(t){return"id"in t&&"defaults"in t}class D2{constructor(){this.controllers=new Os(jr,"datasets",!0),this.elements=new Os(Yt,"elements"),this.plugins=new Os(Object,"plugins"),this.scales=new Os(Wr,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...e){this._each("register",e)}remove(...e){this._each("unregister",e)}addControllers(...e){this._each("register",e,this.controllers)}addElements(...e){this._each("register",e,this.elements)}addPlugins(...e){this._each("register",e,this.plugins)}addScales(...e){this._each("register",e,this.scales)}getController(e){return this._get(e,this.controllers,"controller")}getElement(e){return this._get(e,this.elements,"element")}getPlugin(e){return this._get(e,this.plugins,"plugin")}getScale(e){return this._get(e,this.scales,"scale")}removeControllers(...e){this._each("unregister",e,this.controllers)}removeElements(...e){this._each("unregister",e,this.elements)}removePlugins(...e){this._each("unregister",e,this.plugins)}removeScales(...e){this._each("unregister",e,this.scales)}_each(e,n,r){[...n].forEach(i=>{const s=r||this._getRegistryForType(i);r||s.isForType(i)||s===this.plugins&&i.id?this._exec(e,s,i):X(i,o=>{const a=r||this._getRegistryForType(o);this._exec(e,a,o)})})}_exec(e,n,r){const i=au(e);ee(r["before"+i],[],r),n[e](r),ee(r["after"+i],[],r)}_getRegistryForType(e){for(let n=0;n<this._typedRegistries.length;n++){const r=this._typedRegistries[n];if(r.isForType(e))return r}return this.plugins}_get(e,n,r){const i=n.get(e);if(i===void 0)throw new Error('"'+e+'" is not a registered '+r+".");return i}}var kt=new D2;class O2{constructor(){this._init=[]}notify(e,n,r,i){n==="beforeInit"&&(this._init=this._createDescriptors(e,!0),this._notify(this._init,e,"install"));const s=i?this._descriptors(e).filter(i):this._descriptors(e),o=this._notify(s,e,n,r);return n==="afterDestroy"&&(this._notify(s,e,"stop"),this._notify(this._init,e,"uninstall")),o}_notify(e,n,r,i){i=i||{};for(const s of e){const o=s.plugin,a=o[r],l=[n,i,s.options];if(ee(a,l,o)===!1&&i.cancelable)return!1}return!0}invalidate(){q(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(e){if(this._cache)return this._cache;const n=this._cache=this._createDescriptors(e);return this._notifyStateChanges(e),n}_createDescriptors(e,n){const r=e&&e.config,i=V(r.options&&r.options.plugins,{}),s=L2(r);return i===!1&&!n?[]:F2(e,s,i,n)}_notifyStateChanges(e){const n=this._oldCache||[],r=this._cache,i=(s,o)=>s.filter(a=>!o.some(l=>a.plugin.id===l.plugin.id));this._notify(i(n,r),e,"stop"),this._notify(i(r,n),e,"start")}}function L2(t){const e={},n=[],r=Object.keys(kt.plugins.items);for(let s=0;s<r.length;s++)n.push(kt.getPlugin(r[s]));const i=t.plugins||[];for(let s=0;s<i.length;s++){const o=i[s];n.indexOf(o)===-1&&(n.push(o),e[o.id]=!0)}return{plugins:n,localIds:e}}function A2(t,e){return!e&&t===!1?null:t===!0?{}:t}function F2(t,{plugins:e,localIds:n},r,i){const s=[],o=t.getContext();for(const a of e){const l=a.id,u=A2(r[l],i);u!==null&&s.push({plugin:a,options:z2(t.config,{plugin:a,local:n[l]},u,o)})}return s}function z2(t,{plugin:e,local:n},r,i){const s=t.pluginScopeKeys(e),o=t.getOptionScopes(r,s);return n&&e.defaults&&o.push(e.defaults),t.createResolver(o,i,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function rc(t,e){const n=ue.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||n.indexAxis||"x"}function I2(t,e){let n=t;return t==="_index_"?n=e:t==="_value_"&&(n=e==="x"?"y":"x"),n}function U2(t,e){return t===e?"_index_":"_value_"}function Th(t){if(t==="x"||t==="y"||t==="r")return t}function B2(t){if(t==="top"||t==="bottom")return"x";if(t==="left"||t==="right")return"y"}function ic(t,...e){if(Th(t))return t;for(const n of e){const r=n.axis||B2(n.position)||t.length>1&&Th(t[0].toLowerCase());if(r)return r}throw new Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function Rh(t,e,n){if(n[e+"AxisID"]===t)return{axis:e}}function H2(t,e){if(e.data&&e.data.datasets){const n=e.data.datasets.filter(r=>r.xAxisID===t||r.yAxisID===t);if(n.length)return Rh(t,"x",n[0])||Rh(t,"y",n[0])}return{}}function W2(t,e){const n=Qn[t.type]||{scales:{}},r=e.scales||{},i=rc(t.type,e),s=Object.create(null);return Object.keys(r).forEach(o=>{const a=r[o];if(!W(a))return console.error(`Invalid scale configuration for scale: ${o}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${o}`);const l=ic(o,a,H2(o,t),ue.scales[a.type]),u=U2(l,i),d=n.scales||{};s[o]=ki(Object.create(null),[{axis:l},a,d[l],d[u]])}),t.data.datasets.forEach(o=>{const a=o.type||t.type,l=o.indexAxis||rc(a,e),d=(Qn[a]||{}).scales||{};Object.keys(d).forEach(h=>{const f=I2(h,l),m=o[f+"AxisID"]||f;s[m]=s[m]||Object.create(null),ki(s[m],[{axis:f},r[m],d[h]])})}),Object.keys(s).forEach(o=>{const a=s[o];ki(a,[ue.scales[a.type],ue.scale])}),s}function Ng(t){const e=t.options||(t.options={});e.plugins=V(e.plugins,{}),e.scales=W2(t,e)}function jg(t){return t=t||{},t.datasets=t.datasets||[],t.labels=t.labels||[],t}function V2(t){return t=t||{},t.data=jg(t.data),Ng(t),t}const Dh=new Map,Cg=new Set;function Ls(t,e){let n=Dh.get(t);return n||(n=e(),Dh.set(t,n),Cg.add(n)),n}const si=(t,e,n)=>{const r=Kn(e,n);r!==void 0&&t.add(r)};class Y2{constructor(e){this._config=V2(e),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(e){this._config.type=e}get data(){return this._config.data}set data(e){this._config.data=jg(e)}get options(){return this._config.options}set options(e){this._config.options=e}get plugins(){return this._config.plugins}update(){const e=this._config;this.clearCache(),Ng(e)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(e){return Ls(e,()=>[[`datasets.${e}`,""]])}datasetAnimationScopeKeys(e,n){return Ls(`${e}.transition.${n}`,()=>[[`datasets.${e}.transitions.${n}`,`transitions.${n}`],[`datasets.${e}`,""]])}datasetElementScopeKeys(e,n){return Ls(`${e}-${n}`,()=>[[`datasets.${e}.elements.${n}`,`datasets.${e}`,`elements.${n}`,""]])}pluginScopeKeys(e){const n=e.id,r=this.type;return Ls(`${r}-plugin-${n}`,()=>[[`plugins.${n}`,...e.additionalOptionScopes||[]]])}_cachedScopes(e,n){const r=this._scopeCache;let i=r.get(e);return(!i||n)&&(i=new Map,r.set(e,i)),i}getOptionScopes(e,n,r){const{options:i,type:s}=this,o=this._cachedScopes(e,r),a=o.get(n);if(a)return a;const l=new Set;n.forEach(d=>{e&&(l.add(e),d.forEach(h=>si(l,e,h))),d.forEach(h=>si(l,i,h)),d.forEach(h=>si(l,Qn[s]||{},h)),d.forEach(h=>si(l,ue,h)),d.forEach(h=>si(l,nc,h))});const u=Array.from(l);return u.length===0&&u.push(Object.create(null)),Cg.has(n)&&o.set(n,u),u}chartOptionScopes(){const{options:e,type:n}=this;return[e,Qn[n]||{},ue.datasets[n]||{},{type:n},ue,nc]}resolveNamedOptions(e,n,r,i=[""]){const s={$shared:!0},{resolver:o,subPrefixes:a}=Oh(this._resolverCache,e,i);let l=o;if(K2(o,n)){s.$shared=!1,r=wn(r)?r():r;const u=this.createResolver(e,r,a);l=Or(o,r,u)}for(const u of n)s[u]=l[u];return s}createResolver(e,n,r=[""],i){const{resolver:s}=Oh(this._resolverCache,e,r);return W(n)?Or(s,n,void 0,i):s}}function Oh(t,e,n){let r=t.get(e);r||(r=new Map,t.set(e,r));const i=n.join();let s=r.get(i);return s||(s={resolver:mu(e,n),subPrefixes:n.filter(a=>!a.toLowerCase().includes("hover"))},r.set(i,s)),s}const X2=t=>W(t)&&Object.getOwnPropertyNames(t).some(e=>wn(t[e]));function K2(t,e){const{isScriptable:n,isIndexable:r}=dg(t);for(const i of e){const s=n(i),o=r(i),a=(o||s)&&t[i];if(s&&(wn(a)||X2(a))||o&&ge(a))return!0}return!1}var Q2="4.4.9";const G2=["top","bottom","left","right","chartArea"];function Lh(t,e){return t==="top"||t==="bottom"||G2.indexOf(t)===-1&&e==="x"}function Ah(t,e){return function(n,r){return n[t]===r[t]?n[e]-r[e]:n[t]-r[t]}}function Fh(t){const e=t.chart,n=e.options.animation;e.notifyPlugins("afterRender"),ee(n&&n.onComplete,[t],e)}function q2(t){const e=t.chart,n=e.options.animation;ee(n&&n.onProgress,[t],e)}function Pg(t){return xu()&&typeof t=="string"?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}const eo={},zh=t=>{const e=Pg(t);return Object.values(eo).filter(n=>n.canvas===e).pop()};function Z2(t,e,n){const r=Object.keys(t);for(const i of r){const s=+i;if(s>=e){const o=t[i];delete t[i],(n>0||s>e)&&(t[s+n]=o)}}}function J2(t,e,n,r){return!n||t.type==="mouseout"?null:r?e:t}var Gt;let ss=(Gt=class{static register(...e){kt.add(...e),Ih()}static unregister(...e){kt.remove(...e),Ih()}constructor(e,n){const r=this.config=new Y2(n),i=Pg(e),s=zh(i);if(s)throw new Error("Canvas is already in use. Chart with ID '"+s.id+"' must be destroyed before the canvas with ID '"+s.canvas.id+"' can be reused.");const o=r.createResolver(r.chartOptionScopes(),this.getContext());this.platform=new(r.platform||x2(i)),this.platform.updateConfig(r);const a=this.platform.acquireContext(i,o.aspectRatio),l=a&&a.canvas,u=l&&l.height,d=l&&l.width;if(this.id=gb(),this.ctx=a,this.canvas=l,this.width=d,this.height=u,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new O2,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=Db(h=>this.update(h),o.resizeDelay||0),this._dataChanges=[],eo[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}Rt.listen(this,"complete",Fh),Rt.listen(this,"progress",q2),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:e,maintainAspectRatio:n},width:r,height:i,_aspectRatio:s}=this;return q(e)?n&&s?s:i?r/i:null:e}get data(){return this.config.data}set data(e){this.config.data=e}get options(){return this._options}set options(e){this.config.options=e}get registry(){return kt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():dh(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return ah(this.canvas,this.ctx),this}stop(){return Rt.stop(this),this}resize(e,n){Rt.running(this)?this._resizeBeforeDraw={width:e,height:n}:this._resize(e,n)}_resize(e,n){const r=this.options,i=this.canvas,s=r.maintainAspectRatio&&this.aspectRatio,o=this.platform.getMaximumSize(i,e,n,s),a=r.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=o.width,this.height=o.height,this._aspectRatio=this.aspectRatio,dh(this,a,!0)&&(this.notifyPlugins("resize",{size:o}),ee(r.onResize,[this,o],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const n=this.options.scales||{};X(n,(r,i)=>{r.id=i})}buildOrUpdateScales(){const e=this.options,n=e.scales,r=this.scales,i=Object.keys(r).reduce((o,a)=>(o[a]=!1,o),{});let s=[];n&&(s=s.concat(Object.keys(n).map(o=>{const a=n[o],l=ic(o,a),u=l==="r",d=l==="x";return{options:a,dposition:u?"chartArea":d?"bottom":"left",dtype:u?"radialLinear":d?"category":"linear"}}))),X(s,o=>{const a=o.options,l=a.id,u=ic(l,a),d=V(a.type,o.dtype);(a.position===void 0||Lh(a.position,u)!==Lh(o.dposition))&&(a.position=o.dposition),i[l]=!0;let h=null;if(l in r&&r[l].type===d)h=r[l];else{const f=kt.getScale(d);h=new f({id:l,type:d,ctx:this.ctx,chart:this}),r[h.id]=h}h.init(a,e)}),X(i,(o,a)=>{o||delete r[a]}),X(r,o=>{it.configure(this,o,o.options),it.addBox(this,o)})}_updateMetasets(){const e=this._metasets,n=this.data.datasets.length,r=e.length;if(e.sort((i,s)=>i.index-s.index),r>n){for(let i=n;i<r;++i)this._destroyDatasetMeta(i);e.splice(n,r-n)}this._sortedMetasets=e.slice(0).sort(Ah("order","index"))}_removeUnreferencedMetasets(){const{_metasets:e,data:{datasets:n}}=this;e.length>n.length&&delete this._stacks,e.forEach((r,i)=>{n.filter(s=>s===r._dataset).length===0&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){const e=[],n=this.data.datasets;let r,i;for(this._removeUnreferencedMetasets(),r=0,i=n.length;r<i;r++){const s=n[r];let o=this.getDatasetMeta(r);const a=s.type||this.config.type;if(o.type&&o.type!==a&&(this._destroyDatasetMeta(r),o=this.getDatasetMeta(r)),o.type=a,o.indexAxis=s.indexAxis||rc(a,this.options),o.order=s.order||0,o.index=r,o.label=""+s.label,o.visible=this.isDatasetVisible(r),o.controller)o.controller.updateIndex(r),o.controller.linkScales();else{const l=kt.getController(a),{datasetElementType:u,dataElementType:d}=ue.datasets[a];Object.assign(l,{dataElementType:kt.getElement(d),datasetElementType:u&&kt.getElement(u)}),o.controller=new l(this,r),e.push(o.controller)}}return this._updateMetasets(),e}_resetElements(){X(this.data.datasets,(e,n)=>{this.getDatasetMeta(n).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(e){const n=this.config;n.update();const r=this._options=n.createResolver(n.chartOptionScopes(),this.getContext()),i=this._animationsDisabled=!r.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:e,cancelable:!0})===!1)return;const s=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let u=0,d=this.data.datasets.length;u<d;u++){const{controller:h}=this.getDatasetMeta(u),f=!i&&s.indexOf(h)===-1;h.buildOrUpdateElements(f),o=Math.max(+h.getMaxOverflow(),o)}o=this._minPadding=r.layout.autoPadding?o:0,this._updateLayout(o),i||X(s,u=>{u.reset()}),this._updateDatasets(e),this.notifyPlugins("afterUpdate",{mode:e}),this._layers.sort(Ah("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){X(this.scales,e=>{it.removeBox(this,e)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const e=this.options,n=new Set(Object.keys(this._listeners)),r=new Set(e.events);(!qd(n,r)||!!this._responsiveListeners!==e.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:e}=this,n=this._getUniformDataChanges()||[];for(const{method:r,start:i,count:s}of n){const o=r==="_removeElements"?-s:s;Z2(e,i,o)}}_getUniformDataChanges(){const e=this._dataChanges;if(!e||!e.length)return;this._dataChanges=[];const n=this.data.datasets.length,r=s=>new Set(e.filter(o=>o[0]===s).map((o,a)=>a+","+o.splice(1).join(","))),i=r(0);for(let s=1;s<n;s++)if(!qd(i,r(s)))return;return Array.from(i).map(s=>s.split(",")).map(s=>({method:s[1],start:+s[2],count:+s[3]}))}_updateLayout(e){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;it.update(this,this.width,this.height,e);const n=this.chartArea,r=n.width<=0||n.height<=0;this._layers=[],X(this.boxes,i=>{r&&i.position==="chartArea"||(i.configure&&i.configure(),this._layers.push(...i._layers()))},this),this._layers.forEach((i,s)=>{i._idx=s}),this.notifyPlugins("afterLayout")}_updateDatasets(e){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:e,cancelable:!0})!==!1){for(let n=0,r=this.data.datasets.length;n<r;++n)this.getDatasetMeta(n).controller.configure();for(let n=0,r=this.data.datasets.length;n<r;++n)this._updateDataset(n,wn(e)?e({datasetIndex:n}):e);this.notifyPlugins("afterDatasetsUpdate",{mode:e})}}_updateDataset(e,n){const r=this.getDatasetMeta(e),i={meta:r,index:e,mode:n,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",i)!==!1&&(r.controller._update(n),i.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",i))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(Rt.has(this)?this.attached&&!Rt.running(this)&&Rt.start(this):(this.draw(),Fh({chart:this})))}draw(){let e;if(this._resizeBeforeDraw){const{width:r,height:i}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(r,i)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const n=this._layers;for(e=0;e<n.length&&n[e].z<=0;++e)n[e].draw(this.chartArea);for(this._drawDatasets();e<n.length;++e)n[e].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(e){const n=this._sortedMetasets,r=[];let i,s;for(i=0,s=n.length;i<s;++i){const o=n[i];(!e||o.visible)&&r.push(o)}return r}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const e=this.getSortedVisibleDatasetMetas();for(let n=e.length-1;n>=0;--n)this._drawDataset(e[n]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(e){const n=this.ctx,r={meta:e,index:e.index,cancelable:!0},i=yw(this,e);this.notifyPlugins("beforeDatasetDraw",r)!==!1&&(i&&du(n,i),e.controller.draw(),i&&hu(n),r.cancelable=!1,this.notifyPlugins("afterDatasetDraw",r))}isPointInArea(e){return cg(e,this.chartArea,this._minPadding)}getElementsAtEventForMode(e,n,r,i){const s=Gw.modes[n];return typeof s=="function"?s(this,e,r,i):[]}getDatasetMeta(e){const n=this.data.datasets[e],r=this._metasets;let i=r.filter(s=>s&&s._dataset===n).pop();return i||(i={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:n&&n.order||0,index:e,_dataset:n,_parsed:[],_sorted:!1},r.push(i)),i}getContext(){return this.$context||(this.$context=Hr(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(e){const n=this.data.datasets[e];if(!n)return!1;const r=this.getDatasetMeta(e);return typeof r.hidden=="boolean"?!r.hidden:!n.hidden}setDatasetVisibility(e,n){const r=this.getDatasetMeta(e);r.hidden=!n}toggleDataVisibility(e){this._hiddenIndices[e]=!this._hiddenIndices[e]}getDataVisibility(e){return!this._hiddenIndices[e]}_updateVisibility(e,n,r){const i=r?"show":"hide",s=this.getDatasetMeta(e),o=s.controller._resolveAnimations(void 0,i);Qi(n)?(s.data[n].hidden=!r,this.update()):(this.setDatasetVisibility(e,r),o.update(s,{visible:r}),this.update(a=>a.datasetIndex===e?i:void 0))}hide(e,n){this._updateVisibility(e,n,!1)}show(e,n){this._updateVisibility(e,n,!0)}_destroyDatasetMeta(e){const n=this._metasets[e];n&&n.controller&&n.controller._destroy(),delete this._metasets[e]}_stop(){let e,n;for(this.stop(),Rt.remove(this),e=0,n=this.data.datasets.length;e<n;++e)this._destroyDatasetMeta(e)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:e,ctx:n}=this;this._stop(),this.config.clearCache(),e&&(this.unbindEvents(),ah(e,n),this.platform.releaseContext(n),this.canvas=null,this.ctx=null),delete eo[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...e){return this.canvas.toDataURL(...e)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const e=this._listeners,n=this.platform,r=(s,o)=>{n.addEventListener(this,s,o),e[s]=o},i=(s,o,a)=>{s.offsetX=o,s.offsetY=a,this._eventHandler(s)};X(this.options.events,s=>r(s,i))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const e=this._responsiveListeners,n=this.platform,r=(l,u)=>{n.addEventListener(this,l,u),e[l]=u},i=(l,u)=>{e[l]&&(n.removeEventListener(this,l,u),delete e[l])},s=(l,u)=>{this.canvas&&this.resize(l,u)};let o;const a=()=>{i("attach",a),this.attached=!0,this.resize(),r("resize",s),r("detach",o)};o=()=>{this.attached=!1,i("resize",s),this._stop(),this._resize(0,0),r("attach",a)},n.isAttached(this.canvas)?a():o()}unbindEvents(){X(this._listeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._listeners={},X(this._responsiveListeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._responsiveListeners=void 0}updateHoverStyle(e,n,r){const i=r?"set":"remove";let s,o,a,l;for(n==="dataset"&&(s=this.getDatasetMeta(e[0].datasetIndex),s.controller["_"+i+"DatasetHoverStyle"]()),a=0,l=e.length;a<l;++a){o=e[a];const u=o&&this.getDatasetMeta(o.datasetIndex).controller;u&&u[i+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(e){const n=this._active||[],r=e.map(({datasetIndex:s,index:o})=>{const a=this.getDatasetMeta(s);if(!a)throw new Error("No dataset found at index "+s);return{datasetIndex:s,element:a.data[o],index:o}});!Do(r,n)&&(this._active=r,this._lastEvent=null,this._updateHoverStyles(r,n))}notifyPlugins(e,n,r){return this._plugins.notify(this,e,n,r)}isPluginEnabled(e){return this._plugins._cache.filter(n=>n.plugin.id===e).length===1}_updateHoverStyles(e,n,r){const i=this.options.hover,s=(l,u)=>l.filter(d=>!u.some(h=>d.datasetIndex===h.datasetIndex&&d.index===h.index)),o=s(n,e),a=r?e:s(e,n);o.length&&this.updateHoverStyle(o,i.mode,!1),a.length&&i.mode&&this.updateHoverStyle(a,i.mode,!0)}_eventHandler(e,n){const r={event:e,replay:n,cancelable:!0,inChartArea:this.isPointInArea(e)},i=o=>(o.options.events||this.options.events).includes(e.native.type);if(this.notifyPlugins("beforeEvent",r,i)===!1)return;const s=this._handleEvent(e,n,r.inChartArea);return r.cancelable=!1,this.notifyPlugins("afterEvent",r,i),(s||r.changed)&&this.render(),this}_handleEvent(e,n,r){const{_active:i=[],options:s}=this,o=n,a=this._getActiveElements(e,i,r,o),l=Sb(e),u=J2(e,this._lastEvent,r,l);r&&(this._lastEvent=null,ee(s.onHover,[e,a,this],this),l&&ee(s.onClick,[e,a,this],this));const d=!Do(a,i);return(d||n)&&(this._active=a,this._updateHoverStyles(a,i,n)),this._lastEvent=u,d}_getActiveElements(e,n,r,i){if(e.type==="mouseout")return[];if(!r)return n;const s=this.options.hover;return this.getElementsAtEventForMode(e,s.mode,s,i)}},F(Gt,"defaults",ue),F(Gt,"instances",eo),F(Gt,"overrides",Qn),F(Gt,"registry",kt),F(Gt,"version",Q2),F(Gt,"getChart",zh),Gt);function Ih(){return X(ss.instances,t=>t._plugins.invalidate())}function eS(t,e,n){const{startAngle:r,pixelMargin:i,x:s,y:o,outerRadius:a,innerRadius:l}=e;let u=i/a;t.beginPath(),t.arc(s,o,a,r-u,n+u),l>i?(u=i/l,t.arc(s,o,l,n+u,r-u,!0)):t.arc(s,o,i,n+ve,r-ve),t.closePath(),t.clip()}function tS(t){return fu(t,["outerStart","outerEnd","innerStart","innerEnd"])}function nS(t,e,n,r){const i=tS(t.options.borderRadius),s=(n-e)/2,o=Math.min(s,r*e/2),a=l=>{const u=(n-Math.min(s,l))*r/2;return Ue(l,0,Math.min(s,u))};return{outerStart:a(i.outerStart),outerEnd:a(i.outerEnd),innerStart:Ue(i.innerStart,0,o),innerEnd:Ue(i.innerEnd,0,o)}}function rr(t,e,n,r){return{x:n+t*Math.cos(e),y:r+t*Math.sin(e)}}function Bo(t,e,n,r,i,s){const{x:o,y:a,startAngle:l,pixelMargin:u,innerRadius:d}=e,h=Math.max(e.outerRadius+r+n-u,0),f=d>0?d+r+n+u:0;let m=0;const x=i-l;if(r){const z=d>0?d-r:0,B=h>0?h-r:0,R=(z+B)/2,M=R!==0?x*R/(R+r):x;m=(x-M)/2}const v=Math.max(.001,x*h-n/fe)/h,b=(x-v)/2,g=l+b+m,p=i-b-m,{outerStart:y,outerEnd:w,innerStart:_,innerEnd:S}=nS(e,f,h,p-g),k=h-y,j=h-w,E=g+y/k,C=p-w/j,$=f+_,D=f+S,T=g+_/$,U=p-S/D;if(t.beginPath(),s){const z=(E+C)/2;if(t.arc(o,a,h,E,z),t.arc(o,a,h,z,C),w>0){const L=rr(j,C,o,a);t.arc(L.x,L.y,w,C,p+ve)}const B=rr(D,p,o,a);if(t.lineTo(B.x,B.y),S>0){const L=rr(D,U,o,a);t.arc(L.x,L.y,S,p+ve,U+Math.PI)}const R=(p-S/f+(g+_/f))/2;if(t.arc(o,a,f,p-S/f,R,!0),t.arc(o,a,f,R,g+_/f,!0),_>0){const L=rr($,T,o,a);t.arc(L.x,L.y,_,T+Math.PI,g-ve)}const M=rr(k,g,o,a);if(t.lineTo(M.x,M.y),y>0){const L=rr(k,E,o,a);t.arc(L.x,L.y,y,g-ve,E)}}else{t.moveTo(o,a);const z=Math.cos(E)*h+o,B=Math.sin(E)*h+a;t.lineTo(z,B);const R=Math.cos(C)*h+o,M=Math.sin(C)*h+a;t.lineTo(R,M)}t.closePath()}function rS(t,e,n,r,i){const{fullCircles:s,startAngle:o,circumference:a}=e;let l=e.endAngle;if(s){Bo(t,e,n,r,l,i);for(let u=0;u<s;++u)t.fill();isNaN(a)||(l=o+(a%he||he))}return Bo(t,e,n,r,l,i),t.fill(),l}function iS(t,e,n,r,i){const{fullCircles:s,startAngle:o,circumference:a,options:l}=e,{borderWidth:u,borderJoinStyle:d,borderDash:h,borderDashOffset:f}=l,m=l.borderAlign==="inner";if(!u)return;t.setLineDash(h||[]),t.lineDashOffset=f,m?(t.lineWidth=u*2,t.lineJoin=d||"round"):(t.lineWidth=u,t.lineJoin=d||"bevel");let x=e.endAngle;if(s){Bo(t,e,n,r,x,i);for(let v=0;v<s;++v)t.stroke();isNaN(a)||(x=o+(a%he||he))}m&&eS(t,e,x),s||(Bo(t,e,n,r,x,i),t.stroke())}class fi extends Yt{constructor(n){super();F(this,"circumference");F(this,"endAngle");F(this,"fullCircles");F(this,"innerRadius");F(this,"outerRadius");F(this,"pixelMargin");F(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,n&&Object.assign(this,n)}inRange(n,r,i){const s=this.getProps(["x","y"],i),{angle:o,distance:a}=tg(s,{x:n,y:r}),{startAngle:l,endAngle:u,innerRadius:d,outerRadius:h,circumference:f}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),m=(this.options.spacing+this.options.borderWidth)/2,x=V(f,u-l),v=Fo(o,l,u)&&l!==u,b=x>=he||v,g=Fn(a,d+m,h+m);return b&&g}getCenterPoint(n){const{x:r,y:i,startAngle:s,endAngle:o,innerRadius:a,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],n),{offset:u,spacing:d}=this.options,h=(s+o)/2,f=(a+l+d+u)/2;return{x:r+Math.cos(h)*f,y:i+Math.sin(h)*f}}tooltipPosition(n){return this.getCenterPoint(n)}draw(n){const{options:r,circumference:i}=this,s=(r.offset||0)/4,o=(r.spacing||0)/2,a=r.circular;if(this.pixelMargin=r.borderAlign==="inner"?.33:0,this.fullCircles=i>he?Math.floor(i/he):0,i===0||this.innerRadius<0||this.outerRadius<0)return;n.save();const l=(this.startAngle+this.endAngle)/2;n.translate(Math.cos(l)*s,Math.sin(l)*s);const u=1-Math.sin(Math.min(fe,i||0)),d=s*u;n.fillStyle=r.backgroundColor,n.strokeStyle=r.borderColor,rS(n,this,d,o,a),iS(n,this,d,o,a),n.restore()}}F(fi,"id","arc"),F(fi,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),F(fi,"defaultRoutes",{backgroundColor:"backgroundColor"}),F(fi,"descriptors",{_scriptable:!0,_indexable:n=>n!=="borderDash"});function Eg(t,e){const{x:n,y:r,base:i,width:s,height:o}=t.getProps(["x","y","base","width","height"],e);let a,l,u,d,h;return t.horizontal?(h=o/2,a=Math.min(n,i),l=Math.max(n,i),u=r-h,d=r+h):(h=s/2,a=n-h,l=n+h,u=Math.min(r,i),d=Math.max(r,i)),{left:a,top:u,right:l,bottom:d}}function on(t,e,n,r){return t?0:Ue(e,n,r)}function sS(t,e,n){const r=t.options.borderWidth,i=t.borderSkipped,s=ug(r);return{t:on(i.top,s.top,0,n),r:on(i.right,s.right,0,e),b:on(i.bottom,s.bottom,0,n),l:on(i.left,s.left,0,e)}}function oS(t,e,n){const{enableBorderRadius:r}=t.getProps(["enableBorderRadius"]),i=t.options.borderRadius,s=kr(i),o=Math.min(e,n),a=t.borderSkipped,l=r||W(i);return{topLeft:on(!l||a.top||a.left,s.topLeft,0,o),topRight:on(!l||a.top||a.right,s.topRight,0,o),bottomLeft:on(!l||a.bottom||a.left,s.bottomLeft,0,o),bottomRight:on(!l||a.bottom||a.right,s.bottomRight,0,o)}}function aS(t){const e=Eg(t),n=e.right-e.left,r=e.bottom-e.top,i=sS(t,n/2,r/2),s=oS(t,n/2,r/2);return{outer:{x:e.left,y:e.top,w:n,h:r,radius:s},inner:{x:e.left+i.l,y:e.top+i.t,w:n-i.l-i.r,h:r-i.t-i.b,radius:{topLeft:Math.max(0,s.topLeft-Math.max(i.t,i.l)),topRight:Math.max(0,s.topRight-Math.max(i.t,i.r)),bottomLeft:Math.max(0,s.bottomLeft-Math.max(i.b,i.l)),bottomRight:Math.max(0,s.bottomRight-Math.max(i.b,i.r))}}}}function qa(t,e,n,r){const i=e===null,s=n===null,a=t&&!(i&&s)&&Eg(t,r);return a&&(i||Fn(e,a.left,a.right))&&(s||Fn(n,a.top,a.bottom))}function lS(t){return t.topLeft||t.topRight||t.bottomLeft||t.bottomRight}function cS(t,e){t.rect(e.x,e.y,e.w,e.h)}function Za(t,e,n={}){const r=t.x!==n.x?-e:0,i=t.y!==n.y?-e:0,s=(t.x+t.w!==n.x+n.w?e:0)-r,o=(t.y+t.h!==n.y+n.h?e:0)-i;return{x:t.x+r,y:t.y+i,w:t.w+s,h:t.h+o,radius:t.radius}}class to extends Yt{constructor(e){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,e&&Object.assign(this,e)}draw(e){const{inflateAmount:n,options:{borderColor:r,backgroundColor:i}}=this,{inner:s,outer:o}=aS(this),a=lS(o.radius)?zo:cS;e.save(),(o.w!==s.w||o.h!==s.h)&&(e.beginPath(),a(e,Za(o,n,s)),e.clip(),a(e,Za(s,-n,o)),e.fillStyle=r,e.fill("evenodd")),e.beginPath(),a(e,Za(s,n)),e.fillStyle=i,e.fill(),e.restore()}inRange(e,n,r){return qa(this,e,n,r)}inXRange(e,n){return qa(this,e,null,n)}inYRange(e,n){return qa(this,null,e,n)}getCenterPoint(e){const{x:n,y:r,base:i,horizontal:s}=this.getProps(["x","y","base","horizontal"],e);return{x:s?(n+i)/2:n,y:s?r:(r+i)/2}}getRange(e){return e==="x"?this.width/2:this.height/2}}F(to,"id","bar"),F(to,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),F(to,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});const Uh=(t,e)=>{let{boxHeight:n=e,boxWidth:r=e}=t;return t.usePointStyle&&(n=Math.min(n,e),r=t.pointStyleWidth||Math.min(r,e)),{boxWidth:r,boxHeight:n,itemHeight:Math.max(e,n)}},uS=(t,e)=>t!==null&&e!==null&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class Bh extends Yt{constructor(e){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n,r){this.maxWidth=e,this.maxHeight=n,this._margins=r,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const e=this.options.labels||{};let n=ee(e.generateLabels,[this.chart],this)||[];e.filter&&(n=n.filter(r=>e.filter(r,this.chart.data))),e.sort&&(n=n.sort((r,i)=>e.sort(r,i,this.chart.data))),this.options.reverse&&n.reverse(),this.legendItems=n}fit(){const{options:e,ctx:n}=this;if(!e.display){this.width=this.height=0;return}const r=e.labels,i=Pe(r.font),s=i.size,o=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=Uh(r,s);let u,d;n.font=i.string,this.isHorizontal()?(u=this.maxWidth,d=this._fitRows(o,s,a,l)+10):(d=this.maxHeight,u=this._fitCols(o,i,a,l)+10),this.width=Math.min(u,e.maxWidth||this.maxWidth),this.height=Math.min(d,e.maxHeight||this.maxHeight)}_fitRows(e,n,r,i){const{ctx:s,maxWidth:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],u=this.lineWidths=[0],d=i+a;let h=e;s.textAlign="left",s.textBaseline="middle";let f=-1,m=-d;return this.legendItems.forEach((x,v)=>{const b=r+n/2+s.measureText(x.text).width;(v===0||u[u.length-1]+b+2*a>o)&&(h+=d,u[u.length-(v>0?0:1)]=0,m+=d,f++),l[v]={left:0,top:m,row:f,width:b,height:i},u[u.length-1]+=b+a}),h}_fitCols(e,n,r,i){const{ctx:s,maxHeight:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],u=this.columnSizes=[],d=o-e;let h=a,f=0,m=0,x=0,v=0;return this.legendItems.forEach((b,g)=>{const{itemWidth:p,itemHeight:y}=dS(r,n,s,b,i);g>0&&m+y+2*a>d&&(h+=f+a,u.push({width:f,height:m}),x+=f+a,v++,f=m=0),l[g]={left:x,top:m,col:v,width:p,height:y},f=Math.max(f,p),m+=y+a}),h+=f,u.push({width:f,height:m}),h}adjustHitBoxes(){if(!this.options.display)return;const e=this._computeTitleHeight(),{legendHitBoxes:n,options:{align:r,labels:{padding:i},rtl:s}}=this,o=Nr(s,this.left,this.width);if(this.isHorizontal()){let a=0,l=je(r,this.left+i,this.right-this.lineWidths[a]);for(const u of n)a!==u.row&&(a=u.row,l=je(r,this.left+i,this.right-this.lineWidths[a])),u.top+=this.top+e+i,u.left=o.leftForLtr(o.x(l),u.width),l+=u.width+i}else{let a=0,l=je(r,this.top+e+i,this.bottom-this.columnSizes[a].height);for(const u of n)u.col!==a&&(a=u.col,l=je(r,this.top+e+i,this.bottom-this.columnSizes[a].height)),u.top=l,u.left+=this.left+i,u.left=o.leftForLtr(o.x(u.left),u.width),l+=u.height+i}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const e=this.ctx;du(e,this),this._draw(),hu(e)}}_draw(){const{options:e,columnSizes:n,lineWidths:r,ctx:i}=this,{align:s,labels:o}=e,a=ue.color,l=Nr(e.rtl,this.left,this.width),u=Pe(o.font),{padding:d}=o,h=u.size,f=h/2;let m;this.drawTitle(),i.textAlign=l.textAlign("left"),i.textBaseline="middle",i.lineWidth=.5,i.font=u.string;const{boxWidth:x,boxHeight:v,itemHeight:b}=Uh(o,h),g=function(S,k,j){if(isNaN(x)||x<=0||isNaN(v)||v<0)return;i.save();const E=V(j.lineWidth,1);if(i.fillStyle=V(j.fillStyle,a),i.lineCap=V(j.lineCap,"butt"),i.lineDashOffset=V(j.lineDashOffset,0),i.lineJoin=V(j.lineJoin,"miter"),i.lineWidth=E,i.strokeStyle=V(j.strokeStyle,a),i.setLineDash(V(j.lineDash,[])),o.usePointStyle){const C={radius:v*Math.SQRT2/2,pointStyle:j.pointStyle,rotation:j.rotation,borderWidth:E},$=l.xPlus(S,x/2),D=k+f;lg(i,C,$,D,o.pointStyleWidth&&x)}else{const C=k+Math.max((h-v)/2,0),$=l.leftForLtr(S,x),D=kr(j.borderRadius);i.beginPath(),Object.values(D).some(T=>T!==0)?zo(i,{x:$,y:C,w:x,h:v,radius:D}):i.rect($,C,x,v),i.fill(),E!==0&&i.stroke()}i.restore()},p=function(S,k,j){Gi(i,j.text,S,k+b/2,u,{strikethrough:j.hidden,textAlign:l.textAlign(j.textAlign)})},y=this.isHorizontal(),w=this._computeTitleHeight();y?m={x:je(s,this.left+d,this.right-r[0]),y:this.top+d+w,line:0}:m={x:this.left+d,y:je(s,this.top+w+d,this.bottom-n[0].height),line:0},pg(this.ctx,e.textDirection);const _=b+d;this.legendItems.forEach((S,k)=>{i.strokeStyle=S.fontColor,i.fillStyle=S.fontColor;const j=i.measureText(S.text).width,E=l.textAlign(S.textAlign||(S.textAlign=o.textAlign)),C=x+f+j;let $=m.x,D=m.y;l.setWidth(this.width),y?k>0&&$+C+d>this.right&&(D=m.y+=_,m.line++,$=m.x=je(s,this.left+d,this.right-r[m.line])):k>0&&D+_>this.bottom&&($=m.x=$+n[m.line].width+d,m.line++,D=m.y=je(s,this.top+w+d,this.bottom-n[m.line].height));const T=l.x($);if(g(T,D,S),$=Ob(E,$+x+f,y?$+C:this.right,e.rtl),p(l.x($),D,S),y)m.x+=C+d;else if(typeof S.text!="string"){const U=u.lineHeight;m.y+=Mg(S,U)+d}else m.y+=_}),gg(this.ctx,e.textDirection)}drawTitle(){const e=this.options,n=e.title,r=Pe(n.font),i=ct(n.padding);if(!n.display)return;const s=Nr(e.rtl,this.left,this.width),o=this.ctx,a=n.position,l=r.size/2,u=i.top+l;let d,h=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),d=this.top+u,h=je(e.align,h,this.right-f);else{const x=this.columnSizes.reduce((v,b)=>Math.max(v,b.height),0);d=u+je(e.align,this.top,this.bottom-x-e.labels.padding-this._computeTitleHeight())}const m=je(a,h,h+f);o.textAlign=s.textAlign(cu(a)),o.textBaseline="middle",o.strokeStyle=n.color,o.fillStyle=n.color,o.font=r.string,Gi(o,n.text,m,d,r)}_computeTitleHeight(){const e=this.options.title,n=Pe(e.font),r=ct(e.padding);return e.display?n.lineHeight+r.height:0}_getLegendItemAt(e,n){let r,i,s;if(Fn(e,this.left,this.right)&&Fn(n,this.top,this.bottom)){for(s=this.legendHitBoxes,r=0;r<s.length;++r)if(i=s[r],Fn(e,i.left,i.left+i.width)&&Fn(n,i.top,i.top+i.height))return this.legendItems[r]}return null}handleEvent(e){const n=this.options;if(!mS(e.type,n))return;const r=this._getLegendItemAt(e.x,e.y);if(e.type==="mousemove"||e.type==="mouseout"){const i=this._hoveredItem,s=uS(i,r);i&&!s&&ee(n.onLeave,[e,i,this],this),this._hoveredItem=r,r&&!s&&ee(n.onHover,[e,r,this],this)}else r&&ee(n.onClick,[e,r,this],this)}}function dS(t,e,n,r,i){const s=hS(r,t,e,n),o=fS(i,r,e.lineHeight);return{itemWidth:s,itemHeight:o}}function hS(t,e,n,r){let i=t.text;return i&&typeof i!="string"&&(i=i.reduce((s,o)=>s.length>o.length?s:o)),e+n.size/2+r.measureText(i).width}function fS(t,e,n){let r=t;return typeof e.text!="string"&&(r=Mg(e,n)),r}function Mg(t,e){const n=t.text?t.text.length:0;return e*n}function mS(t,e){return!!((t==="mousemove"||t==="mouseout")&&(e.onHover||e.onLeave)||e.onClick&&(t==="click"||t==="mouseup"))}var $g={id:"legend",_element:Bh,start(t,e,n){const r=t.legend=new Bh({ctx:t.ctx,options:n,chart:t});it.configure(t,r,n),it.addBox(t,r)},stop(t){it.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,n){const r=t.legend;it.configure(t,r,n),r.options=n},afterUpdate(t){const e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,n){const r=e.datasetIndex,i=n.chart;i.isDatasetVisible(r)?(i.hide(r),e.hidden=!0):(i.show(r),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){const e=t.data.datasets,{labels:{usePointStyle:n,pointStyle:r,textAlign:i,color:s,useBorderRadius:o,borderRadius:a}}=t.legend.options;return t._getSortedDatasetMetas().map(l=>{const u=l.controller.getStyle(n?0:void 0),d=ct(u.borderWidth);return{text:e[l.index].label,fillStyle:u.backgroundColor,fontColor:s,hidden:!l.visible,lineCap:u.borderCapStyle,lineDash:u.borderDash,lineDashOffset:u.borderDashOffset,lineJoin:u.borderJoinStyle,lineWidth:(d.width+d.height)/4,strokeStyle:u.borderColor,pointStyle:r||u.pointStyle,rotation:u.rotation,textAlign:i||u.textAlign,borderRadius:o&&(a||u.borderRadius),datasetIndex:l.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class Tg extends Yt{constructor(e){super(),this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n){const r=this.options;if(this.left=0,this.top=0,!r.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=e,this.height=this.bottom=n;const i=ge(r.text)?r.text.length:1;this._padding=ct(r.padding);const s=i*Pe(r.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=s:this.width=s}isHorizontal(){const e=this.options.position;return e==="top"||e==="bottom"}_drawArgs(e){const{top:n,left:r,bottom:i,right:s,options:o}=this,a=o.align;let l=0,u,d,h;return this.isHorizontal()?(d=je(a,r,s),h=n+e,u=s-r):(o.position==="left"?(d=r+e,h=je(a,i,n),l=fe*-.5):(d=s-e,h=je(a,n,i),l=fe*.5),u=i-n),{titleX:d,titleY:h,maxWidth:u,rotation:l}}draw(){const e=this.ctx,n=this.options;if(!n.display)return;const r=Pe(n.font),s=r.lineHeight/2+this._padding.top,{titleX:o,titleY:a,maxWidth:l,rotation:u}=this._drawArgs(s);Gi(e,n.text,0,0,r,{color:n.color,maxWidth:l,rotation:u,textAlign:cu(n.align),textBaseline:"middle",translation:[o,a]})}}function pS(t,e){const n=new Tg({ctx:t.ctx,options:e,chart:t});it.configure(t,n,e),it.addBox(t,n),t.titleBlock=n}var gS={id:"title",_element:Tg,start(t,e,n){pS(t,n)},stop(t){const e=t.titleBlock;it.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,n){const r=t.titleBlock;it.configure(t,r,n),r.options=n},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const mi={average(t){if(!t.length)return!1;let e,n,r=new Set,i=0,s=0;for(e=0,n=t.length;e<n;++e){const a=t[e].element;if(a&&a.hasValue()){const l=a.tooltipPosition();r.add(l.x),i+=l.y,++s}}return s===0||r.size===0?!1:{x:[...r].reduce((a,l)=>a+l)/r.size,y:i/s}},nearest(t,e){if(!t.length)return!1;let n=e.x,r=e.y,i=Number.POSITIVE_INFINITY,s,o,a;for(s=0,o=t.length;s<o;++s){const l=t[s].element;if(l&&l.hasValue()){const u=l.getCenterPoint(),d=Eb(e,u);d<i&&(i=d,a=l)}}if(a){const l=a.tooltipPosition();n=l.x,r=l.y}return{x:n,y:r}}};function St(t,e){return e&&(ge(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function Dt(t){return(typeof t=="string"||t instanceof String)&&t.indexOf(`
`)>-1?t.split(`
`):t}function xS(t,e){const{element:n,datasetIndex:r,index:i}=e,s=t.getDatasetMeta(r).controller,{label:o,value:a}=s.getLabelAndValue(i);return{chart:t,label:o,parsed:s.getParsed(i),raw:t.data.datasets[r].data[i],formattedValue:a,dataset:s.getDataset(),dataIndex:i,datasetIndex:r,element:n}}function Hh(t,e){const n=t.chart.ctx,{body:r,footer:i,title:s}=t,{boxWidth:o,boxHeight:a}=e,l=Pe(e.bodyFont),u=Pe(e.titleFont),d=Pe(e.footerFont),h=s.length,f=i.length,m=r.length,x=ct(e.padding);let v=x.height,b=0,g=r.reduce((w,_)=>w+_.before.length+_.lines.length+_.after.length,0);if(g+=t.beforeBody.length+t.afterBody.length,h&&(v+=h*u.lineHeight+(h-1)*e.titleSpacing+e.titleMarginBottom),g){const w=e.displayColors?Math.max(a,l.lineHeight):l.lineHeight;v+=m*w+(g-m)*l.lineHeight+(g-1)*e.bodySpacing}f&&(v+=e.footerMarginTop+f*d.lineHeight+(f-1)*e.footerSpacing);let p=0;const y=function(w){b=Math.max(b,n.measureText(w).width+p)};return n.save(),n.font=u.string,X(t.title,y),n.font=l.string,X(t.beforeBody.concat(t.afterBody),y),p=e.displayColors?o+2+e.boxPadding:0,X(r,w=>{X(w.before,y),X(w.lines,y),X(w.after,y)}),p=0,n.font=d.string,X(t.footer,y),n.restore(),b+=x.width,{width:b,height:v}}function vS(t,e){const{y:n,height:r}=e;return n<r/2?"top":n>t.height-r/2?"bottom":"center"}function yS(t,e,n,r){const{x:i,width:s}=r,o=n.caretSize+n.caretPadding;if(t==="left"&&i+s+o>e.width||t==="right"&&i-s-o<0)return!0}function bS(t,e,n,r){const{x:i,width:s}=n,{width:o,chartArea:{left:a,right:l}}=t;let u="center";return r==="center"?u=i<=(a+l)/2?"left":"right":i<=s/2?u="left":i>=o-s/2&&(u="right"),yS(u,t,e,n)&&(u="center"),u}function Wh(t,e,n){const r=n.yAlign||e.yAlign||vS(t,n);return{xAlign:n.xAlign||e.xAlign||bS(t,e,n,r),yAlign:r}}function wS(t,e){let{x:n,width:r}=t;return e==="right"?n-=r:e==="center"&&(n-=r/2),n}function SS(t,e,n){let{y:r,height:i}=t;return e==="top"?r+=n:e==="bottom"?r-=i+n:r-=i/2,r}function Vh(t,e,n,r){const{caretSize:i,caretPadding:s,cornerRadius:o}=t,{xAlign:a,yAlign:l}=n,u=i+s,{topLeft:d,topRight:h,bottomLeft:f,bottomRight:m}=kr(o);let x=wS(e,a);const v=SS(e,l,u);return l==="center"?a==="left"?x+=u:a==="right"&&(x-=u):a==="left"?x-=Math.max(d,f)+i:a==="right"&&(x+=Math.max(h,m)+i),{x:Ue(x,0,r.width-e.width),y:Ue(v,0,r.height-e.height)}}function As(t,e,n){const r=ct(n.padding);return e==="center"?t.x+t.width/2:e==="right"?t.x+t.width-r.right:t.x+r.left}function Yh(t){return St([],Dt(t))}function _S(t,e,n){return Hr(t,{tooltip:e,tooltipItems:n,type:"tooltip"})}function Xh(t,e){const n=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return n?t.override(n):t}const Rg={beforeTitle:Tt,title(t){if(t.length>0){const e=t[0],n=e.chart.data.labels,r=n?n.length:0;if(this&&this.options&&this.options.mode==="dataset")return e.dataset.label||"";if(e.label)return e.label;if(r>0&&e.dataIndex<r)return n[e.dataIndex]}return""},afterTitle:Tt,beforeBody:Tt,beforeLabel:Tt,label(t){if(this&&this.options&&this.options.mode==="dataset")return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");const n=t.formattedValue;return q(n)||(e+=n),e},labelColor(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:n.borderColor,backgroundColor:n.backgroundColor,borderWidth:n.borderWidth,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:n.pointStyle,rotation:n.rotation}},afterLabel:Tt,afterBody:Tt,beforeFooter:Tt,footer:Tt,afterFooter:Tt};function Ae(t,e,n,r){const i=t[e].call(n,r);return typeof i>"u"?Rg[e].call(n,r):i}class sc extends Yt{constructor(e){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=e.chart,this.options=e.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(e){this.options=e,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const e=this._cachedAnimations;if(e)return e;const n=this.chart,r=this.options.setContext(this.getContext()),i=r.enabled&&n.options.animation&&r.animations,s=new xg(this.chart,i);return i._cacheable&&(this._cachedAnimations=Object.freeze(s)),s}getContext(){return this.$context||(this.$context=_S(this.chart.getContext(),this,this._tooltipItems))}getTitle(e,n){const{callbacks:r}=n,i=Ae(r,"beforeTitle",this,e),s=Ae(r,"title",this,e),o=Ae(r,"afterTitle",this,e);let a=[];return a=St(a,Dt(i)),a=St(a,Dt(s)),a=St(a,Dt(o)),a}getBeforeBody(e,n){return Yh(Ae(n.callbacks,"beforeBody",this,e))}getBody(e,n){const{callbacks:r}=n,i=[];return X(e,s=>{const o={before:[],lines:[],after:[]},a=Xh(r,s);St(o.before,Dt(Ae(a,"beforeLabel",this,s))),St(o.lines,Ae(a,"label",this,s)),St(o.after,Dt(Ae(a,"afterLabel",this,s))),i.push(o)}),i}getAfterBody(e,n){return Yh(Ae(n.callbacks,"afterBody",this,e))}getFooter(e,n){const{callbacks:r}=n,i=Ae(r,"beforeFooter",this,e),s=Ae(r,"footer",this,e),o=Ae(r,"afterFooter",this,e);let a=[];return a=St(a,Dt(i)),a=St(a,Dt(s)),a=St(a,Dt(o)),a}_createItems(e){const n=this._active,r=this.chart.data,i=[],s=[],o=[];let a=[],l,u;for(l=0,u=n.length;l<u;++l)a.push(xS(this.chart,n[l]));return e.filter&&(a=a.filter((d,h,f)=>e.filter(d,h,f,r))),e.itemSort&&(a=a.sort((d,h)=>e.itemSort(d,h,r))),X(a,d=>{const h=Xh(e.callbacks,d);i.push(Ae(h,"labelColor",this,d)),s.push(Ae(h,"labelPointStyle",this,d)),o.push(Ae(h,"labelTextColor",this,d))}),this.labelColors=i,this.labelPointStyles=s,this.labelTextColors=o,this.dataPoints=a,a}update(e,n){const r=this.options.setContext(this.getContext()),i=this._active;let s,o=[];if(!i.length)this.opacity!==0&&(s={opacity:0});else{const a=mi[r.position].call(this,i,this._eventPosition);o=this._createItems(r),this.title=this.getTitle(o,r),this.beforeBody=this.getBeforeBody(o,r),this.body=this.getBody(o,r),this.afterBody=this.getAfterBody(o,r),this.footer=this.getFooter(o,r);const l=this._size=Hh(this,r),u=Object.assign({},a,l),d=Wh(this.chart,r,u),h=Vh(r,u,d,this.chart);this.xAlign=d.xAlign,this.yAlign=d.yAlign,s={opacity:1,x:h.x,y:h.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=o,this.$context=void 0,s&&this._resolveAnimations().update(this,s),e&&r.external&&r.external.call(this,{chart:this.chart,tooltip:this,replay:n})}drawCaret(e,n,r,i){const s=this.getCaretPosition(e,r,i);n.lineTo(s.x1,s.y1),n.lineTo(s.x2,s.y2),n.lineTo(s.x3,s.y3)}getCaretPosition(e,n,r){const{xAlign:i,yAlign:s}=this,{caretSize:o,cornerRadius:a}=r,{topLeft:l,topRight:u,bottomLeft:d,bottomRight:h}=kr(a),{x:f,y:m}=e,{width:x,height:v}=n;let b,g,p,y,w,_;return s==="center"?(w=m+v/2,i==="left"?(b=f,g=b-o,y=w+o,_=w-o):(b=f+x,g=b+o,y=w-o,_=w+o),p=b):(i==="left"?g=f+Math.max(l,d)+o:i==="right"?g=f+x-Math.max(u,h)-o:g=this.caretX,s==="top"?(y=m,w=y-o,b=g-o,p=g+o):(y=m+v,w=y+o,b=g+o,p=g-o),_=y),{x1:b,x2:g,x3:p,y1:y,y2:w,y3:_}}drawTitle(e,n,r){const i=this.title,s=i.length;let o,a,l;if(s){const u=Nr(r.rtl,this.x,this.width);for(e.x=As(this,r.titleAlign,r),n.textAlign=u.textAlign(r.titleAlign),n.textBaseline="middle",o=Pe(r.titleFont),a=r.titleSpacing,n.fillStyle=r.titleColor,n.font=o.string,l=0;l<s;++l)n.fillText(i[l],u.x(e.x),e.y+o.lineHeight/2),e.y+=o.lineHeight+a,l+1===s&&(e.y+=r.titleMarginBottom-a)}}_drawColorBox(e,n,r,i,s){const o=this.labelColors[r],a=this.labelPointStyles[r],{boxHeight:l,boxWidth:u}=s,d=Pe(s.bodyFont),h=As(this,"left",s),f=i.x(h),m=l<d.lineHeight?(d.lineHeight-l)/2:0,x=n.y+m;if(s.usePointStyle){const v={radius:Math.min(u,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},b=i.leftForLtr(f,u)+u/2,g=x+l/2;e.strokeStyle=s.multiKeyBackground,e.fillStyle=s.multiKeyBackground,lh(e,v,b,g),e.strokeStyle=o.borderColor,e.fillStyle=o.backgroundColor,lh(e,v,b,g)}else{e.lineWidth=W(o.borderWidth)?Math.max(...Object.values(o.borderWidth)):o.borderWidth||1,e.strokeStyle=o.borderColor,e.setLineDash(o.borderDash||[]),e.lineDashOffset=o.borderDashOffset||0;const v=i.leftForLtr(f,u),b=i.leftForLtr(i.xPlus(f,1),u-2),g=kr(o.borderRadius);Object.values(g).some(p=>p!==0)?(e.beginPath(),e.fillStyle=s.multiKeyBackground,zo(e,{x:v,y:x,w:u,h:l,radius:g}),e.fill(),e.stroke(),e.fillStyle=o.backgroundColor,e.beginPath(),zo(e,{x:b,y:x+1,w:u-2,h:l-2,radius:g}),e.fill()):(e.fillStyle=s.multiKeyBackground,e.fillRect(v,x,u,l),e.strokeRect(v,x,u,l),e.fillStyle=o.backgroundColor,e.fillRect(b,x+1,u-2,l-2))}e.fillStyle=this.labelTextColors[r]}drawBody(e,n,r){const{body:i}=this,{bodySpacing:s,bodyAlign:o,displayColors:a,boxHeight:l,boxWidth:u,boxPadding:d}=r,h=Pe(r.bodyFont);let f=h.lineHeight,m=0;const x=Nr(r.rtl,this.x,this.width),v=function(j){n.fillText(j,x.x(e.x+m),e.y+f/2),e.y+=f+s},b=x.textAlign(o);let g,p,y,w,_,S,k;for(n.textAlign=o,n.textBaseline="middle",n.font=h.string,e.x=As(this,b,r),n.fillStyle=r.bodyColor,X(this.beforeBody,v),m=a&&b!=="right"?o==="center"?u/2+d:u+2+d:0,w=0,S=i.length;w<S;++w){for(g=i[w],p=this.labelTextColors[w],n.fillStyle=p,X(g.before,v),y=g.lines,a&&y.length&&(this._drawColorBox(n,e,w,x,r),f=Math.max(h.lineHeight,l)),_=0,k=y.length;_<k;++_)v(y[_]),f=h.lineHeight;X(g.after,v)}m=0,f=h.lineHeight,X(this.afterBody,v),e.y-=s}drawFooter(e,n,r){const i=this.footer,s=i.length;let o,a;if(s){const l=Nr(r.rtl,this.x,this.width);for(e.x=As(this,r.footerAlign,r),e.y+=r.footerMarginTop,n.textAlign=l.textAlign(r.footerAlign),n.textBaseline="middle",o=Pe(r.footerFont),n.fillStyle=r.footerColor,n.font=o.string,a=0;a<s;++a)n.fillText(i[a],l.x(e.x),e.y+o.lineHeight/2),e.y+=o.lineHeight+r.footerSpacing}}drawBackground(e,n,r,i){const{xAlign:s,yAlign:o}=this,{x:a,y:l}=e,{width:u,height:d}=r,{topLeft:h,topRight:f,bottomLeft:m,bottomRight:x}=kr(i.cornerRadius);n.fillStyle=i.backgroundColor,n.strokeStyle=i.borderColor,n.lineWidth=i.borderWidth,n.beginPath(),n.moveTo(a+h,l),o==="top"&&this.drawCaret(e,n,r,i),n.lineTo(a+u-f,l),n.quadraticCurveTo(a+u,l,a+u,l+f),o==="center"&&s==="right"&&this.drawCaret(e,n,r,i),n.lineTo(a+u,l+d-x),n.quadraticCurveTo(a+u,l+d,a+u-x,l+d),o==="bottom"&&this.drawCaret(e,n,r,i),n.lineTo(a+m,l+d),n.quadraticCurveTo(a,l+d,a,l+d-m),o==="center"&&s==="left"&&this.drawCaret(e,n,r,i),n.lineTo(a,l+h),n.quadraticCurveTo(a,l,a+h,l),n.closePath(),n.fill(),i.borderWidth>0&&n.stroke()}_updateAnimationTarget(e){const n=this.chart,r=this.$animations,i=r&&r.x,s=r&&r.y;if(i||s){const o=mi[e.position].call(this,this._active,this._eventPosition);if(!o)return;const a=this._size=Hh(this,e),l=Object.assign({},o,this._size),u=Wh(n,e,l),d=Vh(e,l,u,n);(i._to!==d.x||s._to!==d.y)&&(this.xAlign=u.xAlign,this.yAlign=u.yAlign,this.width=a.width,this.height=a.height,this.caretX=o.x,this.caretY=o.y,this._resolveAnimations().update(this,d))}}_willRender(){return!!this.opacity}draw(e){const n=this.options.setContext(this.getContext());let r=this.opacity;if(!r)return;this._updateAnimationTarget(n);const i={width:this.width,height:this.height},s={x:this.x,y:this.y};r=Math.abs(r)<.001?0:r;const o=ct(n.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;n.enabled&&a&&(e.save(),e.globalAlpha=r,this.drawBackground(s,e,i,n),pg(e,n.textDirection),s.y+=o.top,this.drawTitle(s,e,n),this.drawBody(s,e,n),this.drawFooter(s,e,n),gg(e,n.textDirection),e.restore())}getActiveElements(){return this._active||[]}setActiveElements(e,n){const r=this._active,i=e.map(({datasetIndex:a,index:l})=>{const u=this.chart.getDatasetMeta(a);if(!u)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:u.data[l],index:l}}),s=!Do(r,i),o=this._positionChanged(i,n);(s||o)&&(this._active=i,this._eventPosition=n,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(e,n,r=!0){if(n&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const i=this.options,s=this._active||[],o=this._getActiveElements(e,s,n,r),a=this._positionChanged(o,e),l=n||!Do(o,s)||a;return l&&(this._active=o,(i.enabled||i.external)&&(this._eventPosition={x:e.x,y:e.y},this.update(!0,n))),l}_getActiveElements(e,n,r,i){const s=this.options;if(e.type==="mouseout")return[];if(!i)return n.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const o=this.chart.getElementsAtEventForMode(e,s.mode,s,r);return s.reverse&&o.reverse(),o}_positionChanged(e,n){const{caretX:r,caretY:i,options:s}=this,o=mi[s.position].call(this,e,n);return o!==!1&&(r!==o.x||i!==o.y)}}F(sc,"positioners",mi);var Dg={id:"tooltip",_element:sc,positioners:mi,afterInit(t,e,n){n&&(t.tooltip=new sc({chart:t,options:n}))},beforeUpdate(t,e,n){t.tooltip&&t.tooltip.initialize(n)},reset(t,e,n){t.tooltip&&t.tooltip.initialize(n)},afterDraw(t){const e=t.tooltip;if(e&&e._willRender()){const n={tooltip:e};if(t.notifyPlugins("beforeTooltipDraw",{...n,cancelable:!0})===!1)return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",n)}},afterEvent(t,e){if(t.tooltip){const n=e.replay;t.tooltip.handleEvent(e.event,n,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Rg},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>t!=="filter"&&t!=="itemSort"&&t!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const kS=(t,e,n,r)=>(typeof e=="string"?(n=t.push(e)-1,r.unshift({index:n,label:e})):isNaN(e)&&(n=null),n);function NS(t,e,n,r){const i=t.indexOf(e);if(i===-1)return kS(t,e,n,r);const s=t.lastIndexOf(e);return i!==s?n:i}const jS=(t,e)=>t===null?null:Ue(Math.round(t),0,e);function Kh(t){const e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class oc extends Wr{constructor(e){super(e),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(e){const n=this._addedLabels;if(n.length){const r=this.getLabels();for(const{index:i,label:s}of n)r[i]===s&&r.splice(i,1);this._addedLabels=[]}super.init(e)}parse(e,n){if(q(e))return null;const r=this.getLabels();return n=isFinite(n)&&r[n]===e?n:NS(r,e,V(n,e),this._addedLabels),jS(n,r.length-1)}determineDataLimits(){const{minDefined:e,maxDefined:n}=this.getUserBounds();let{min:r,max:i}=this.getMinMax(!0);this.options.bounds==="ticks"&&(e||(r=0),n||(i=this.getLabels().length-1)),this.min=r,this.max=i}buildTicks(){const e=this.min,n=this.max,r=this.options.offset,i=[];let s=this.getLabels();s=e===0&&n===s.length-1?s:s.slice(e,n+1),this._valueRange=Math.max(s.length-(r?0:1),1),this._startValue=this.min-(r?.5:0);for(let o=e;o<=n;o++)i.push({value:o});return i}getLabelForValue(e){return Kh.call(this,e)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(e){return typeof e!="number"&&(e=this.parse(e)),e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getValueForPixel(e){return Math.round(this._startValue+this.getDecimalForPixel(e)*this._valueRange)}getBasePixel(){return this.bottom}}F(oc,"id","category"),F(oc,"defaults",{ticks:{callback:Kh}});function CS(t,e){const n=[],{bounds:i,step:s,min:o,max:a,precision:l,count:u,maxTicks:d,maxDigits:h,includeBounds:f}=t,m=s||1,x=d-1,{min:v,max:b}=e,g=!q(o),p=!q(a),y=!q(u),w=(b-v)/(h+1);let _=Jd((b-v)/x/m)*m,S,k,j,E;if(_<1e-14&&!g&&!p)return[{value:v},{value:b}];E=Math.ceil(b/_)-Math.floor(v/_),E>x&&(_=Jd(E*_/x/m)*m),q(l)||(S=Math.pow(10,l),_=Math.ceil(_*S)/S),i==="ticks"?(k=Math.floor(v/_)*_,j=Math.ceil(b/_)*_):(k=v,j=b),g&&p&&s&&jb((a-o)/s,_/1e3)?(E=Math.round(Math.min((a-o)/_,d)),_=(a-o)/E,k=o,j=a):y?(k=g?o:k,j=p?a:j,E=u-1,_=(j-k)/E):(E=(j-k)/_,qs(E,Math.round(E),_/1e3)?E=Math.round(E):E=Math.ceil(E));const C=Math.max(eh(_),eh(k));S=Math.pow(10,q(l)?C:l),k=Math.round(k*S)/S,j=Math.round(j*S)/S;let $=0;for(g&&(f&&k!==o?(n.push({value:o}),k<o&&$++,qs(Math.round((k+$*_)*S)/S,o,Qh(o,w,t))&&$++):k<o&&$++);$<E;++$){const D=Math.round((k+$*_)*S)/S;if(p&&D>a)break;n.push({value:D})}return p&&f&&j!==a?n.length&&qs(n[n.length-1].value,a,Qh(a,w,t))?n[n.length-1].value=a:n.push({value:a}):(!p||j===a)&&n.push({value:j}),n}function Qh(t,e,{horizontal:n,minRotation:r}){const i=It(r),s=(n?Math.sin(i):Math.cos(i))||.001,o=.75*e*(""+t).length;return Math.min(e/s,o)}class PS extends Wr{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(e,n){return q(e)||(typeof e=="number"||e instanceof Number)&&!isFinite(+e)?null:+e}handleTickRangeOptions(){const{beginAtZero:e}=this.options,{minDefined:n,maxDefined:r}=this.getUserBounds();let{min:i,max:s}=this;const o=l=>i=n?i:l,a=l=>s=r?s:l;if(e){const l=vn(i),u=vn(s);l<0&&u<0?a(0):l>0&&u>0&&o(0)}if(i===s){let l=s===0?1:Math.abs(s*.05);a(s+l),e||o(i-l)}this.min=i,this.max=s}getTickLimit(){const e=this.options.ticks;let{maxTicksLimit:n,stepSize:r}=e,i;return r?(i=Math.ceil(this.max/r)-Math.floor(this.min/r)+1,i>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${r} would result generating up to ${i} ticks. Limiting to 1000.`),i=1e3)):(i=this.computeTickLimit(),n=n||11),n&&(i=Math.min(n,i)),i}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const e=this.options,n=e.ticks;let r=this.getTickLimit();r=Math.max(2,r);const i={maxTicks:r,bounds:e.bounds,min:e.min,max:e.max,precision:n.precision,step:n.stepSize,count:n.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:n.minRotation||0,includeBounds:n.includeBounds!==!1},s=this._range||this,o=CS(i,s);return e.bounds==="ticks"&&Cb(o,this,"value"),e.reverse?(o.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),o}configure(){const e=this.ticks;let n=this.min,r=this.max;if(super.configure(),this.options.offset&&e.length){const i=(r-n)/Math.max(e.length-1,1)/2;n-=i,r+=i}this._startValue=n,this._endValue=r,this._valueRange=r-n}getLabelForValue(e){return uu(e,this.chart.options.locale,this.options.ticks.format)}}class ac extends PS{determineDataLimits(){const{min:e,max:n}=this.getMinMax(!0);this.min=lt(e)?e:0,this.max=lt(n)?n:1,this.handleTickRangeOptions()}computeTickLimit(){const e=this.isHorizontal(),n=e?this.width:this.height,r=It(this.options.ticks.minRotation),i=(e?Math.sin(r):Math.cos(r))||.001,s=this._resolveTickFontOptions(0);return Math.ceil(n/Math.min(40,s.lineHeight/i))}getPixelForValue(e){return e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getValueForPixel(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange}}F(ac,"id","linear"),F(ac,"defaults",{ticks:{callback:ag.formatters.numeric}});const ha={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},ze=Object.keys(ha);function Gh(t,e){return t-e}function qh(t,e){if(q(e))return null;const n=t._adapter,{parser:r,round:i,isoWeekday:s}=t._parseOpts;let o=e;return typeof r=="function"&&(o=r(o)),lt(o)||(o=typeof r=="string"?n.parse(o,r):n.parse(o)),o===null?null:(i&&(o=i==="week"&&(Ao(s)||s===!0)?n.startOf(o,"isoWeek",s):n.startOf(o,i)),+o)}function Zh(t,e,n,r){const i=ze.length;for(let s=ze.indexOf(t);s<i-1;++s){const o=ha[ze[s]],a=o.steps?o.steps:Number.MAX_SAFE_INTEGER;if(o.common&&Math.ceil((n-e)/(a*o.size))<=r)return ze[s]}return ze[i-1]}function ES(t,e,n,r,i){for(let s=ze.length-1;s>=ze.indexOf(n);s--){const o=ze[s];if(ha[o].common&&t._adapter.diff(i,r,o)>=e-1)return o}return ze[n?ze.indexOf(n):0]}function MS(t){for(let e=ze.indexOf(t)+1,n=ze.length;e<n;++e)if(ha[ze[e]].common)return ze[e]}function Jh(t,e,n){if(!n)t[e]=!0;else if(n.length){const{lo:r,hi:i}=lu(n,e),s=n[r]>=e?n[r]:n[i];t[s]=!0}}function $S(t,e,n,r){const i=t._adapter,s=+i.startOf(e[0].value,r),o=e[e.length-1].value;let a,l;for(a=s;a<=o;a=+i.add(a,1,r))l=n[a],l>=0&&(e[l].major=!0);return e}function ef(t,e,n){const r=[],i={},s=e.length;let o,a;for(o=0;o<s;++o)a=e[o],i[a]=o,r.push({value:a,major:!1});return s===0||!n?r:$S(t,r,i,n)}class Ho extends Wr{constructor(e){super(e),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(e,n={}){const r=e.time||(e.time={}),i=this._adapter=new Vw._date(e.adapters.date);i.init(n),ki(r.displayFormats,i.formats()),this._parseOpts={parser:r.parser,round:r.round,isoWeekday:r.isoWeekday},super.init(e),this._normalized=n.normalized}parse(e,n){return e===void 0?null:qh(this,e)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const e=this.options,n=this._adapter,r=e.time.unit||"day";let{min:i,max:s,minDefined:o,maxDefined:a}=this.getUserBounds();function l(u){!o&&!isNaN(u.min)&&(i=Math.min(i,u.min)),!a&&!isNaN(u.max)&&(s=Math.max(s,u.max))}(!o||!a)&&(l(this._getLabelBounds()),(e.bounds!=="ticks"||e.ticks.source!=="labels")&&l(this.getMinMax(!1))),i=lt(i)&&!isNaN(i)?i:+n.startOf(Date.now(),r),s=lt(s)&&!isNaN(s)?s:+n.endOf(Date.now(),r)+1,this.min=Math.min(i,s-1),this.max=Math.max(i+1,s)}_getLabelBounds(){const e=this.getLabelTimestamps();let n=Number.POSITIVE_INFINITY,r=Number.NEGATIVE_INFINITY;return e.length&&(n=e[0],r=e[e.length-1]),{min:n,max:r}}buildTicks(){const e=this.options,n=e.time,r=e.ticks,i=r.source==="labels"?this.getLabelTimestamps():this._generate();e.bounds==="ticks"&&i.length&&(this.min=this._userMin||i[0],this.max=this._userMax||i[i.length-1]);const s=this.min,o=this.max,a=Tb(i,s,o);return this._unit=n.unit||(r.autoSkip?Zh(n.minUnit,this.min,this.max,this._getLabelCapacity(s)):ES(this,a.length,n.minUnit,this.min,this.max)),this._majorUnit=!r.major.enabled||this._unit==="year"?void 0:MS(this._unit),this.initOffsets(i),e.reverse&&a.reverse(),ef(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(e=>+e.value))}initOffsets(e=[]){let n=0,r=0,i,s;this.options.offset&&e.length&&(i=this.getDecimalForValue(e[0]),e.length===1?n=1-i:n=(this.getDecimalForValue(e[1])-i)/2,s=this.getDecimalForValue(e[e.length-1]),e.length===1?r=s:r=(s-this.getDecimalForValue(e[e.length-2]))/2);const o=e.length<3?.5:.25;n=Ue(n,0,o),r=Ue(r,0,o),this._offsets={start:n,end:r,factor:1/(n+1+r)}}_generate(){const e=this._adapter,n=this.min,r=this.max,i=this.options,s=i.time,o=s.unit||Zh(s.minUnit,n,r,this._getLabelCapacity(n)),a=V(i.ticks.stepSize,1),l=o==="week"?s.isoWeekday:!1,u=Ao(l)||l===!0,d={};let h=n,f,m;if(u&&(h=+e.startOf(h,"isoWeek",l)),h=+e.startOf(h,u?"day":o),e.diff(r,n,o)>1e5*a)throw new Error(n+" and "+r+" are too far apart with stepSize of "+a+" "+o);const x=i.ticks.source==="data"&&this.getDataTimestamps();for(f=h,m=0;f<r;f=+e.add(f,a,o),m++)Jh(d,f,x);return(f===r||i.bounds==="ticks"||m===1)&&Jh(d,f,x),Object.keys(d).sort(Gh).map(v=>+v)}getLabelForValue(e){const n=this._adapter,r=this.options.time;return r.tooltipFormat?n.format(e,r.tooltipFormat):n.format(e,r.displayFormats.datetime)}format(e,n){const i=this.options.time.displayFormats,s=this._unit,o=n||i[s];return this._adapter.format(e,o)}_tickFormatFunction(e,n,r,i){const s=this.options,o=s.ticks.callback;if(o)return ee(o,[e,n,r],this);const a=s.time.displayFormats,l=this._unit,u=this._majorUnit,d=l&&a[l],h=u&&a[u],f=r[n],m=u&&h&&f&&f.major;return this._adapter.format(e,i||(m?h:d))}generateTickLabels(e){let n,r,i;for(n=0,r=e.length;n<r;++n)i=e[n],i.label=this._tickFormatFunction(i.value,n,e)}getDecimalForValue(e){return e===null?NaN:(e-this.min)/(this.max-this.min)}getPixelForValue(e){const n=this._offsets,r=this.getDecimalForValue(e);return this.getPixelForDecimal((n.start+r)*n.factor)}getValueForPixel(e){const n=this._offsets,r=this.getDecimalForPixel(e)/n.factor-n.end;return this.min+r*(this.max-this.min)}_getLabelSize(e){const n=this.options.ticks,r=this.ctx.measureText(e).width,i=It(this.isHorizontal()?n.maxRotation:n.minRotation),s=Math.cos(i),o=Math.sin(i),a=this._resolveTickFontOptions(0).size;return{w:r*s+a*o,h:r*o+a*s}}_getLabelCapacity(e){const n=this.options.time,r=n.displayFormats,i=r[n.unit]||r.millisecond,s=this._tickFormatFunction(e,0,ef(this,[e],this._majorUnit),i),o=this._getLabelSize(s),a=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return a>0?a:1}getDataTimestamps(){let e=this._cache.data||[],n,r;if(e.length)return e;const i=this.getMatchingVisibleMetas();if(this._normalized&&i.length)return this._cache.data=i[0].controller.getAllParsedValues(this);for(n=0,r=i.length;n<r;++n)e=e.concat(i[n].controller.getAllParsedValues(this));return this._cache.data=this.normalize(e)}getLabelTimestamps(){const e=this._cache.labels||[];let n,r;if(e.length)return e;const i=this.getLabels();for(n=0,r=i.length;n<r;++n)e.push(qh(this,i[n]));return this._cache.labels=this._normalized?e:this.normalize(e)}normalize(e){return rg(e.sort(Gh))}}F(Ho,"id","time"),F(Ho,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function Fs(t,e,n){let r=0,i=t.length-1,s,o,a,l;n?(e>=t[r].pos&&e<=t[i].pos&&({lo:r,hi:i}=tc(t,"pos",e)),{pos:s,time:a}=t[r],{pos:o,time:l}=t[i]):(e>=t[r].time&&e<=t[i].time&&({lo:r,hi:i}=tc(t,"time",e)),{time:s,pos:a}=t[r],{time:o,pos:l}=t[i]);const u=o-s;return u?a+(l-a)*(e-s)/u:a}class tf extends Ho{constructor(e){super(e),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const e=this._getTimestampsForTable(),n=this._table=this.buildLookupTable(e);this._minPos=Fs(n,this.min),this._tableRange=Fs(n,this.max)-this._minPos,super.initOffsets(e)}buildLookupTable(e){const{min:n,max:r}=this,i=[],s=[];let o,a,l,u,d;for(o=0,a=e.length;o<a;++o)u=e[o],u>=n&&u<=r&&i.push(u);if(i.length<2)return[{time:n,pos:0},{time:r,pos:1}];for(o=0,a=i.length;o<a;++o)d=i[o+1],l=i[o-1],u=i[o],Math.round((d+l)/2)!==u&&s.push({time:u,pos:o/(a-1)});return s}_generate(){const e=this.min,n=this.max;let r=super.getDataTimestamps();return(!r.includes(e)||!r.length)&&r.splice(0,0,e),(!r.includes(n)||r.length===1)&&r.push(n),r.sort((i,s)=>i-s)}_getTimestampsForTable(){let e=this._cache.all||[];if(e.length)return e;const n=this.getDataTimestamps(),r=this.getLabelTimestamps();return n.length&&r.length?e=this.normalize(n.concat(r)):e=n.length?n:r,e=this._cache.all=e,e}getDecimalForValue(e){return(Fs(this._table,e)-this._minPos)/this._tableRange}getValueForPixel(e){const n=this._offsets,r=this.getDecimalForPixel(e)/n.factor-n.end;return Fs(this._table,r*this._tableRange+this._minPos,!0)}}F(tf,"id","timeseries"),F(tf,"defaults",Ho.defaults);const Og="label";function nf(t,e){typeof t=="function"?t(e):t&&(t.current=e)}function TS(t,e){const n=t.options;n&&e&&Object.assign(n,e)}function Lg(t,e){t.labels=e}function Ag(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Og;const r=[];t.datasets=e.map(i=>{const s=t.datasets.find(o=>o[n]===i[n]);return!s||!i.data||r.includes(s)?{...i}:(r.push(s),Object.assign(s,i),s)})}function RS(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Og;const n={labels:[],datasets:[]};return Lg(n,t.labels),Ag(n,t.datasets,e),n}function DS(t,e){const{height:n=150,width:r=300,redraw:i=!1,datasetIdKey:s,type:o,data:a,options:l,plugins:u=[],fallbackContent:d,updateMode:h,...f}=t,m=N.useRef(null),x=N.useRef(null),v=()=>{m.current&&(x.current=new ss(m.current,{type:o,data:RS(a,s),options:l&&{...l},plugins:u}),nf(e,x.current))},b=()=>{nf(e,null),x.current&&(x.current.destroy(),x.current=null)};return N.useEffect(()=>{!i&&x.current&&l&&TS(x.current,l)},[i,l]),N.useEffect(()=>{!i&&x.current&&Lg(x.current.config.data,a.labels)},[i,a.labels]),N.useEffect(()=>{!i&&x.current&&a.datasets&&Ag(x.current.config.data,a.datasets,s)},[i,a.datasets]),N.useEffect(()=>{x.current&&(i?(b(),setTimeout(v)):x.current.update(h))},[i,l,a.labels,a.datasets,h]),N.useEffect(()=>{x.current&&(b(),setTimeout(v))},[o]),N.useEffect(()=>(v(),()=>b()),[]),pt.createElement("canvas",{ref:m,role:"img",height:n,width:r,...f},d)}const OS=N.forwardRef(DS);function Fg(t,e){return ss.register(e),N.forwardRef((n,r)=>pt.createElement(OS,{...n,ref:r,type:t}))}const LS=Fg("bar",Zs),AS=Fg("doughnut",di);ss.register(fi,Dg,$g);function FS(){const{state:t,usageSinceLastRecording:e,getDisplayUnitName:n}=Xe(),{theme:r}=me(),i=N.useRef(null),s=t.currentUnits,o=e,a=s+o,l=a>0?o/a*100:0;N.useEffect(()=>{const h=i.current;if(h){const f=h.ctx,m=f.createRadialGradient(200,200,50,200,200,150);m.addColorStop(0,"#667eea"),m.addColorStop(.3,"#764ba2"),m.addColorStop(.6,"#667eea"),m.addColorStop(1,"#f093fb");const x=f.createRadialGradient(200,200,50,200,200,150);x.addColorStop(0,"#ff9a9e"),x.addColorStop(.3,"#fecfef"),x.addColorStop(.6,"#fecfef"),x.addColorStop(1,"#ffc3a0"),h.data.datasets[0].backgroundColor=[m,x],h.update()}},[s,o]);const u={labels:[`Remaining ${n()}`,`Used ${n()}`],datasets:[{data:[s,o],backgroundColor:["linear-gradient(135deg, #667eea 0%, #764ba2 100%)","linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #ffc3a0 100%)"],borderColor:["rgba(255, 255, 255, 0.9)","rgba(255, 255, 255, 0.9)"],borderWidth:4,cutout:"78%",borderRadius:12,borderJoinStyle:"round",hoverBorderWidth:6,hoverBorderColor:["rgba(255, 255, 255, 1)","rgba(255, 255, 255, 1)"],shadowOffsetX:3,shadowOffsetY:3,shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.1)"}]},d={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"rgba(255, 255, 255, 0.2)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(h){const f=h.label||"",m=h.parsed,x=a>0?(m/a*100).toFixed(1):0;return`${f}: ${m.toFixed(2)} (${x}%)`}}}},animation:{animateRotate:!0,animateScale:!0,duration:2e3,easing:"easeInOutCubic",delay:h=>h.dataIndex*200},interaction:{intersect:!1,mode:"nearest"},elements:{arc:{borderWidth:4,hoverBorderWidth:6,borderSkipped:!1,borderAlign:"inner"}},layout:{padding:{top:20,bottom:20,left:20,right:20}}};return c.jsxs("div",{className:"relative",children:[c.jsxs("div",{className:"relative h-[32rem] mb-8 flex items-center justify-center",children:[c.jsx("div",{className:`absolute top-8 left-8 w-20 h-20 bg-gradient-to-r ${r.gradient} rounded-full opacity-20 blur-xl animate-pulse`}),c.jsx("div",{className:`absolute bottom-8 right-8 w-24 h-24 bg-gradient-to-r ${r.gradient} rounded-full opacity-15 blur-2xl animate-pulse`,style:{animationDelay:"1s"}}),c.jsx("div",{className:`absolute top-1/2 left-4 w-16 h-16 bg-gradient-to-r ${r.gradient} rounded-full opacity-10 blur-lg animate-pulse`,style:{animationDelay:"2s"}}),c.jsx("div",{className:`absolute top-1/4 right-8 w-12 h-12 bg-gradient-to-r ${r.gradient} rounded-full opacity-25 blur-md animate-pulse`,style:{animationDelay:"0.5s"}}),c.jsxs("div",{className:"relative h-full w-full max-w-lg max-h-lg flex items-center justify-center",children:[c.jsx(AS,{ref:i,data:u,options:d}),c.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 w-64 h-64 bg-gradient-to-r from-indigo-400 via-purple-500 to-pink-500 rounded-full blur-2xl opacity-40 animate-pulse"}),c.jsxs("div",{className:`relative w-64 h-64 ${r.card}/50 border-4 ${r.border}/40 backdrop-blur-xl rounded-full shadow-2xl flex items-center justify-center`,children:[c.jsx("div",{className:`absolute inset-3 ${r.secondary}/70 rounded-full`}),c.jsxs("div",{className:"relative text-center z-10",children:[c.jsx("div",{className:"mb-2 flex justify-center",children:c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full blur-sm opacity-75 animate-pulse"}),c.jsx(Ye,{className:"relative h-8 w-8 text-transparent bg-gradient-to-r from-yellow-500 to-orange-600 bg-clip-text animate-bounce",style:{animationDuration:"2s"}})]})}),c.jsxs("div",{className:"relative mb-2",children:[c.jsx("div",{className:`text-4xl font-black ${r.text} drop-shadow-lg`,children:s.toFixed(2)}),c.jsxs("div",{className:`text-sm font-bold ${r.textSecondary} mt-1 tracking-wide`,children:[n()," Left"]})]}),c.jsx("div",{className:"mt-1",children:c.jsxs("div",{className:`text-base font-bold tracking-tight ${r.textSecondary} drop-shadow-lg`,children:[l.toFixed(1),"% Used"]})})]}),c.jsx("div",{className:"absolute top-6 right-6 w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-70 animate-pulse"}),c.jsx("div",{className:"absolute bottom-6 left-6 w-2 h-2 bg-gradient-to-r from-pink-400 to-orange-400 rounded-full opacity-60 animate-pulse",style:{animationDelay:"1s"}}),c.jsx("div",{className:"absolute top-1/4 right-8 w-1.5 h-1.5 bg-gradient-to-r from-green-400 to-blue-400 rounded-full opacity-50 animate-pulse",style:{animationDelay:"1.5s"}}),c.jsx("div",{className:"absolute bottom-1/4 left-8 w-1 h-1 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-40 animate-pulse",style:{animationDelay:"0.5s"}})]})]})})]})]}),c.jsxs("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-2 gap-4",children:[c.jsx("div",{className:`${r.card} rounded-2xl shadow-lg p-4 border ${r.border}`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("h3",{className:`text-base font-semibold mb-1 ${r.text}`,children:"Current Units"}),c.jsx("p",{className:`text-2xl font-bold ${r.text}`,children:t.currentUnits.toFixed(2)}),c.jsxs("p",{className:`text-xs font-medium ${r.textSecondary}`,children:[n()," remaining"]}),c.jsxs("p",{className:`text-sm ${r.textSecondary} font-semibold mt-1`,children:["Value: ",t.currencySymbol,(t.currentUnits*t.unitCost).toFixed(2)]})]}),c.jsx("div",{className:`p-3 rounded-xl bg-gradient-to-br ${r.gradient} shadow-lg`,children:c.jsx(Ye,{className:"h-6 w-6 text-white"})})]})}),c.jsx("div",{className:`${r.card} rounded-2xl shadow-lg p-4 border ${r.border}`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("h3",{className:`text-base font-semibold mb-1 ${r.text}`,children:"Usage Since Last Recording"}),c.jsx("p",{className:`text-2xl font-bold ${r.text}`,children:e.toFixed(2)}),c.jsxs("p",{className:`text-xs font-medium ${r.textSecondary}`,children:[n()," used"]}),c.jsxs("p",{className:`text-sm ${r.textSecondary} font-semibold mt-1`,children:["Cost: ",t.currencySymbol,(e*t.unitCost).toFixed(2)]})]}),c.jsx("div",{className:`p-3 rounded-xl bg-gradient-to-br ${r.gradient} shadow-lg`,children:c.jsx(jt,{className:"h-6 w-6 text-white"})})]})})]}),c.jsxs("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-2 gap-4",children:[c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${r.card} border ${r.border} p-4 shadow-lg`,children:[c.jsx("div",{className:`absolute inset-0 ${r.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("div",{className:`text-xs font-semibold ${r.textSecondary} tracking-wider uppercase mb-1`,children:"Total Cost"}),c.jsxs("div",{className:`text-2xl font-black ${r.text}`,children:[t.currencySymbol||"R",(o*t.unitCost).toFixed(2)]}),c.jsxs("div",{className:`text-xs font-medium ${r.textSecondary} mt-1`,children:["For ",o.toFixed(2)," ",n()," used"]})]}),c.jsx("div",{className:`p-3 rounded-xl bg-gradient-to-br ${r.gradient} shadow-lg`,children:c.jsx(ru,{className:"h-6 w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-1 right-1 w-6 h-6 bg-gradient-to-r ${r.gradient} rounded-full opacity-20 blur-sm`}),c.jsx("div",{className:`absolute bottom-1 left-1 w-4 h-4 bg-gradient-to-r ${r.gradient} rounded-full opacity-15 blur-sm`})]}),c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${r.card} border ${r.border} p-4 shadow-lg`,children:[c.jsx("div",{className:`absolute inset-0 ${r.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("div",{className:`text-xs font-semibold ${r.textSecondary} tracking-wider uppercase mb-1`,children:"Current Rate"}),c.jsxs("div",{className:`text-2xl font-black ${r.text}`,children:[t.currencySymbol||"R",t.unitCost.toFixed(2)]}),c.jsxs("div",{className:`text-xs font-medium ${r.textSecondary} mt-1`,children:["Per ",n()]})]}),c.jsx("div",{className:`p-3 rounded-xl bg-gradient-to-br ${r.gradient} shadow-lg`,children:c.jsx(B1,{className:"h-6 w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-1 right-1 w-4 h-4 bg-gradient-to-r ${r.gradient} rounded-full opacity-15 blur-sm`}),c.jsx("div",{className:`absolute bottom-1 left-1 w-6 h-6 bg-gradient-to-r ${r.gradient} rounded-full opacity-20 blur-sm`})]})]}),c.jsxs("div",{className:"mt-8",children:[c.jsxs("div",{className:"flex justify-between items-center mb-4",children:[c.jsx("span",{className:`text-sm font-bold ${r.textSecondary} tracking-wide uppercase`,children:"Usage Progress"}),c.jsxs("span",{className:`text-lg font-black px-3 py-1 rounded-full ${r.secondary} ${r.text}`,children:[l.toFixed(1),"%"]})]}),c.jsx("div",{className:"relative",children:c.jsx("div",{className:`w-full ${r.secondary} rounded-full h-4 border ${r.border}`,children:c.jsx("div",{className:`h-4 rounded-full transition-all duration-1000 ease-out bg-gradient-to-r ${l>70?"from-red-500 to-red-600":l>40?"from-amber-500 to-orange-600":"from-emerald-500 to-green-600"}`,style:{width:`${Math.min(l,100)}%`}})})})]})]})}function zS(){const t=Jn(),{state:e,getDisplayUnitName:n}=Xe(),{theme:r}=me(),i=e.currentUnits,s=e.thresholdLimit;return c.jsx("div",{className:"bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-xl p-6 shadow-lg",children:c.jsxs("div",{className:"flex items-start",children:[c.jsx("div",{className:"flex-shrink-0",children:c.jsx("div",{className:"p-2 bg-gradient-to-r from-amber-400 to-orange-500 rounded-lg",children:c.jsx(_r,{className:"h-6 w-6 text-white animate-pulse"})})}),c.jsxs("div",{className:"ml-4 flex-1",children:[c.jsxs("h3",{className:"text-xl font-bold text-amber-800 mb-2",children:["⚠️ Low ",n()," Warning!"]}),c.jsxs("div",{className:"text-sm text-amber-700 space-y-2",children:[c.jsxs("p",{className:"font-medium",children:["You have ",c.jsxs("strong",{className:"text-amber-900",children:[i.toFixed(2)," ",n()]})," remaining, which is below your threshold of ",c.jsxs("strong",{className:"text-amber-900",children:[s.toFixed(2)," ",n()]}),"."]}),c.jsxs("p",{children:["💡 ",c.jsx("strong",{children:"Time to top up!"})," Consider purchasing more ",n()," to avoid running out of power."]})]}),c.jsxs("div",{className:"mt-4 flex flex-wrap gap-3",children:[c.jsx("button",{onClick:()=>t("/purchases"),className:"bg-gradient-to-r from-emerald-500 to-green-600 text-white px-6 py-3 rounded-lg text-sm font-semibold hover:from-emerald-600 hover:to-green-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105",children:"🛒 Top Up Now"}),c.jsx("button",{onClick:()=>t("/settings"),className:"bg-white text-amber-700 border-2 border-amber-300 px-6 py-3 rounded-lg text-sm font-semibold hover:bg-amber-50 hover:border-amber-400 transition-all duration-200",children:"⚙️ Adjust Threshold"})]})]})]})})}function rf(){const t=Jn(),{state:e,isThresholdExceeded:n,getDisplayUnitName:r,weeklyPurchaseTotal:i,monthlyPurchaseTotal:s,weeklyUsageTotal:o,monthlyUsageTotal:a,usageSinceLastRecording:l}=Xe(),{theme:u,currentTheme:d}=me();return c.jsxs("div",{className:"space-y-6",children:[n&&c.jsx(zS,{}),c.jsx("div",{className:`${u.card} rounded-2xl shadow-lg p-8 border ${u.border} w-full`,children:c.jsx(FS,{})}),c.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-1 gap-6",children:c.jsx("div",{className:`${u.card} rounded-2xl shadow-lg p-6 border ${u.border}`,children:c.jsxs("div",{className:"space-y-3",children:[e.purchases.slice(0,3).map(h=>c.jsx("div",{className:`p-4 ${u.secondary} border ${u.border} rounded-xl shadow-sm hover:shadow-md transition-all duration-200`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-lg ${u.accent} shadow-sm`,children:c.jsx(Ve,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsxs("p",{className:`text-sm font-semibold ${u.text}`,children:["Purchase: ",e.currencySymbol||"R",h.currency.toFixed(2)]}),c.jsx("p",{className:`text-xs ${u.textSecondary}`,children:new Date(h.date).toLocaleDateString()})]})]}),c.jsxs("span",{className:`text-sm font-semibold ${u.text}`,children:["+",h.units.toFixed(2)," ",r()]})]})},h.id)),e.usageHistory.slice(0,2).map(h=>c.jsx("div",{className:`p-4 ${u.secondary} border ${u.border} rounded-xl shadow-sm hover:shadow-md transition-all duration-200`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-lg ${u.accent} shadow-sm`,children:c.jsx(jt,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsx("p",{className:`text-sm font-semibold ${u.text}`,children:"Usage recorded"}),c.jsx("p",{className:`text-xs ${u.textSecondary}`,children:new Date(h.date).toLocaleDateString()})]})]}),c.jsxs("span",{className:`text-sm font-semibold ${u.text}`,children:["-",h.usage.toFixed(2)," ",r()]})]})},h.id)),e.purchases.length===0&&e.usageHistory.length===0&&c.jsxs("div",{className:`text-center py-12 ${u.secondary} border ${u.border} rounded-xl`,children:[c.jsx("div",{className:`p-4 rounded-2xl bg-gradient-to-br ${u.gradient} w-fit mx-auto mb-4`,children:c.jsx(Ye,{className:"h-12 w-12 text-white"})}),c.jsx("p",{className:`text-sm ${u.textSecondary} font-medium`,children:"No recent activity"}),c.jsx("p",{className:`text-xs ${u.textSecondary} mt-1`,children:"Start by making a purchase or recording usage"})]})]})})}),c.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:c.jsxs("div",{className:`${u.card} rounded-2xl shadow-lg p-6 border ${u.border}`,children:[c.jsxs("h2",{className:`text-2xl font-bold ${u.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:`p-3 rounded-2xl bg-gradient-to-br ${u.gradient} shadow-lg`,children:c.jsx(Ye,{className:"h-6 w-6 text-white"})}),"Quick Actions"]}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs("button",{onClick:()=>t("/purchases"),className:`w-full flex items-center gap-4 p-4 bg-gradient-to-r ${u.gradient} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:[c.jsx(Ve,{className:"h-6 w-6"}),c.jsxs("div",{className:"text-left",children:[c.jsx("span",{className:"block text-lg font-semibold",children:"Add Purchase"}),c.jsx("span",{className:"block text-sm opacity-80",children:"Top up your units"})]})]}),c.jsxs("button",{onClick:()=>t("/usage"),className:`w-full flex items-center gap-4 p-4 bg-gradient-to-r ${u.gradient} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:[c.jsx(jt,{className:"h-6 w-6"}),c.jsxs("div",{className:"text-left",children:[c.jsx("span",{className:"block text-lg font-semibold",children:"Record Usage"}),c.jsx("span",{className:"block text-sm opacity-80",children:"Track consumption"})]})]}),c.jsxs("button",{onClick:()=>t("/history"),className:`w-full flex items-center gap-4 p-4 bg-gradient-to-r ${u.gradient} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:[c.jsx(Ye,{className:"h-6 w-6"}),c.jsxs("div",{className:"text-left",children:[c.jsx("span",{className:"block text-lg font-semibold",children:"View History"}),c.jsx("span",{className:"block text-sm opacity-80",children:"See all records"})]})]})]})]})})]})}const Lr=t=>{t.target.type==="number"&&t.preventDefault()};function IS(){var b;const[t,e]=N.useState(""),[n,r]=N.useState(!1),{state:i,addPurchase:s,getDisplayUnitName:o}=Xe(),{theme:a,currentTheme:l}=me(),u=[{code:"ZAR",name:"South African Rand",symbol:"R"},{code:"USD",name:"US Dollar",symbol:"$"},{code:"EUR",name:"Euro",symbol:"€"},{code:"GBP",name:"British Pound",symbol:"£"},{code:"JPY",name:"Japanese Yen",symbol:"¥"},{code:"CAD",name:"Canadian Dollar",symbol:"C$"},{code:"AUD",name:"Australian Dollar",symbol:"A$"},{code:"CHF",name:"Swiss Franc",symbol:"CHF"},{code:"CNY",name:"Chinese Yuan",symbol:"¥"},{code:"INR",name:"Indian Rupee",symbol:"₹"},{code:"BRL",name:"Brazilian Real",symbol:"R$"},{code:"KRW",name:"South Korean Won",symbol:"₩"},{code:"MXN",name:"Mexican Peso",symbol:"$"},{code:"SGD",name:"Singapore Dollar",symbol:"S$"},{code:"NZD",name:"New Zealand Dollar",symbol:"NZ$"}],d=g=>Math.round((g+Number.EPSILON)*100)/100,h=g=>{const p=d(g);return p%1===0?p.toString():p.toFixed(2)},f=parseFloat(t)||0,m=i.unitCost||0,x=m>0?d(f/m):0,v=async g=>{g.preventDefault(),r(!0);try{const p=d(parseFloat(t)),y=x;if(isNaN(p)||p<=0){alert("Please enter a valid positive amount");return}if(m<=0){alert("Please set a valid unit cost in Settings before making a purchase");return}if(y<=0){alert("The calculated units must be greater than 0");return}s(p,y),e(""),alert(`Purchase added successfully! Added ${h(y)} ${o()} for ${i.currencySymbol||"R"}${h(p)}`)}catch(p){console.error("Error adding purchase:",p),alert("Error adding purchase. Please try again.")}finally{r(!1)}};return c.jsxs("form",{onSubmit:v,className:"space-y-6",children:[c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"currency",className:`block text-sm font-semibold ${a.text} mb-3`,children:["💰 Amount (",((b=u.find(g=>g.code===(i.currency||"ZAR")))==null?void 0:b.name)||"South African Rand",")"]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${a.gradient}`,children:c.jsx(Ve,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"currency",value:t,onChange:g=>e(g.target.value),onWheel:Lr,step:"0.01",min:"0",placeholder:"Enter amount to spend",className:`w-full pl-12 pr-4 py-4 border-4 ${a.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${a.border} ${a.card} ${a.text} ${a.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${a.textSecondary} opacity-80 font-medium`,children:"💡 Enter the amount you want to spend"})]}),c.jsxs("div",{children:[c.jsxs("label",{className:`block text-sm font-semibold ${a.text} mb-3`,children:["⚡ Units Preview (",o(),")"]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${a.gradient}`,children:c.jsx(Ye,{className:"h-4 w-4 text-white"})})}),c.jsx("div",{className:`w-full pl-12 pr-4 py-4 border-4 ${a.border} rounded-xl ${a.secondary} ${a.text} font-bold text-lg shadow-lg flex items-center min-h-[56px]`,children:f>0&&m>0?c.jsxs("span",{className:`${a.text} font-bold`,children:[h(x)," ",o()]}):c.jsx("span",{className:`${a.textSecondary} font-medium`,children:m<=0?"Set unit cost in Settings first":"Enter amount above to see units"})})]}),c.jsx("p",{className:`mt-2 text-xs ${a.textSecondary} opacity-80 font-medium`,children:"⚡ Live preview of units you'll receive"})]}),c.jsxs("div",{className:`p-6 ${a.card} rounded-xl border ${a.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${a.gradient} mr-3`,children:c.jsx(Vp,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${a.text} text-lg`,children:"Calculation Preview"})]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:`flex justify-between items-center p-3 ${a.secondary} rounded-lg`,children:[c.jsx("span",{className:`${a.textSecondary} font-medium`,children:"Unit Cost:"}),c.jsxs("span",{className:`${a.text} font-bold`,children:[i.currencySymbol||"R",h(m)," per ",o()]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${a.secondary} rounded-lg border ${a.border}`,children:[c.jsx("span",{className:`${a.textSecondary} font-medium`,children:"Amount:"}),c.jsxs("span",{className:`font-bold text-lg ${a.text}`,children:[i.currencySymbol||"R",h(f)]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${a.secondary} rounded-lg border ${a.border}`,children:[c.jsx("span",{className:`${a.textSecondary} font-medium`,children:"Units:"}),c.jsxs("span",{className:`font-bold text-lg ${a.text}`,children:[h(x)," ",o()]})]}),c.jsx("div",{className:`border-t ${a.border} my-3`}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${a.secondary} rounded-lg border ${a.border}`,children:[c.jsx("span",{className:`${a.textSecondary} font-medium`,children:"New Total Units:"}),c.jsxs("span",{className:`font-bold text-xl ${a.text}`,children:[h(d(i.currentUnits+x))," ",o()]})]})]})]}),c.jsx("button",{type:"submit",disabled:n||f<=0||x<=0||m<=0,className:`w-full bg-gradient-to-r ${a.gradient} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:c.jsx("div",{className:"flex items-center justify-center gap-2",children:n?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),"Adding Purchase..."]}):c.jsxs(c.Fragment,{children:[c.jsx(Ve,{className:"h-5 w-5"}),"Add Purchase"]})})}),c.jsxs("div",{className:`text-center p-4 ${a.card} rounded-xl border ${a.border}`,children:[c.jsxs("div",{className:"flex items-center justify-center mb-2",children:[c.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${a.gradient} mr-2`,children:c.jsx("span",{className:"text-white text-xs",children:"💡"})}),c.jsx("span",{className:`text-sm font-semibold ${a.text}`,children:"Live Calculator"})]}),c.jsx("p",{className:`text-xs ${a.textSecondary} leading-relaxed`,children:"Enter the amount you want to spend and see the units you'll receive in real-time. The calculation is based on your current unit cost setting."})]})]})}function US(){const{state:t,getDisplayUnitName:e}=Xe(),{theme:n,currentTheme:r}=me(),i=(a,l="bg-gray-800/50")=>r==="dark"?l:a,s=t.purchases.reduce((a,l)=>a+l.currency,0),o=t.purchases.reduce((a,l)=>a+l.units,0);return c.jsx("div",{className:"space-y-6",children:c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:`${n.card} rounded-2xl shadow-lg p-6 border ${n.border} ${i("bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50","bg-gray-800/50")}`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${n.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md",children:c.jsx(Ve,{className:"h-5 w-5 text-white"})}),"Add New Purchase"]}),c.jsx("div",{className:`${i("bg-white/60","bg-gray-700/50")} backdrop-blur-sm rounded-xl p-4`,children:c.jsx(IS,{})})]}),c.jsxs("div",{className:"space-y-3",children:[c.jsx("div",{className:`${n.card} rounded-xl shadow-lg p-4 border ${n.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${n.gradient} shadow-lg`,children:c.jsx(Ve,{className:"h-5 w-5 text-white"})}),c.jsxs("div",{children:[c.jsx("p",{className:`text-sm font-medium ${n.textSecondary} opacity-80`,children:"Total Spent"}),c.jsxs("p",{className:`text-lg font-bold ${n.text}`,children:[t.currencySymbol||"R",s.toFixed(2)]})]})]}),c.jsx("div",{className:"text-right",children:c.jsx("p",{className:"text-xs text-emerald-500 font-medium",children:"All Purchases"})})]})}),c.jsx("div",{className:`${n.card} rounded-xl shadow-lg p-4 border ${n.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${n.gradient} shadow-lg`,children:c.jsx(Ye,{className:"h-5 w-5 text-white"})}),c.jsxs("div",{children:[c.jsxs("p",{className:`text-sm font-medium ${n.textSecondary} opacity-80`,children:["Total ",e()," Purchased"]}),c.jsx("p",{className:`text-lg font-bold ${n.text}`,children:o.toFixed(2)})]})]}),c.jsx("div",{className:"text-right",children:c.jsx("p",{className:`text-xs ${n.textSecondary} font-medium`,children:e()})})]})}),c.jsx("div",{className:`${n.card} rounded-xl shadow-lg p-4 border ${n.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${n.gradient} shadow-lg`,children:c.jsx(To,{className:"h-5 w-5 text-white"})}),c.jsxs("div",{children:[c.jsx("p",{className:`text-sm font-medium ${n.textSecondary} opacity-80`,children:"Total Purchases"}),c.jsx("p",{className:`text-lg font-bold ${n.text}`,children:t.purchases.length})]})]}),c.jsx("div",{className:"text-right",children:c.jsx("p",{className:"text-xs text-violet-500 font-medium",children:"Transactions"})})]})})]})]}),c.jsxs("div",{className:`${n.card} rounded-2xl shadow-lg p-6 border ${n.border} ${i("bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50","bg-gray-800/50")}`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${n.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:`p-2 rounded-xl bg-gradient-to-br ${n.gradient} shadow-md`,children:c.jsx(To,{className:"h-5 w-5 text-white"})}),"Recent Purchases"]}),c.jsxs("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:[t.purchases.slice(0,10).map(a=>c.jsx("div",{className:`p-4 ${i("bg-white/60","bg-gray-700/50")} backdrop-blur-sm rounded-xl border ${i("border-white/40","border-gray-600")} shadow-sm hover:shadow-md transition-all duration-200`,children:c.jsxs("div",{className:"flex justify-between items-start",children:[c.jsxs("div",{children:[c.jsxs("p",{className:`font-semibold ${n.text} text-lg`,children:[t.currencySymbol||"R",a.currency.toFixed(2)]}),c.jsxs("p",{className:`text-sm ${n.textSecondary} opacity-80`,children:[a.units.toFixed(2)," ",e()," @ ",t.currencySymbol||"R",a.unitCost.toFixed(2),"/",e()]}),c.jsx("p",{className:`text-xs ${n.textSecondary} mt-1 opacity-70`,children:a.timestamp})]}),c.jsxs("div",{className:"text-right",children:[c.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-700 border border-emerald-200",children:["+",a.units.toFixed(2)," ",e()]}),c.jsxs("p",{className:`text-xs ${n.textSecondary} mt-1 font-medium`,children:[t.currencySymbol||"R",a.currency.toFixed(2)]})]})]})},a.id)),t.purchases.length===0&&c.jsxs("div",{className:`text-center py-12 ${i("bg-white/40","bg-gray-700/40")} backdrop-blur-sm rounded-xl border ${i("border-white/40","border-gray-600")}`,children:[c.jsx("div",{className:`p-4 rounded-2xl ${n.secondary} w-fit mx-auto mb-4`,children:c.jsx(Ve,{className:`h-12 w-12 ${n.textSecondary}`})}),c.jsx("p",{className:`text-sm ${n.textSecondary} opacity-80 font-medium`,children:"No purchases yet"}),c.jsx("p",{className:`text-xs ${n.textSecondary} opacity-60 mt-1`,children:"Add your first purchase above to get started"})]})]})]})]})})}function BS(){const[t,e]=N.useState(""),[n,r]=N.useState(!1),{state:i,updateUsage:s,usageSinceLastRecording:o,getDisplayUnitName:a}=Xe(),{theme:l,currentTheme:u}=me(),d=parseFloat(t)||0,h=i.currentUnits-d,f=h*i.unitCost,m=async x=>{x.preventDefault(),r(!0);try{const v=parseFloat(t);if(isNaN(v)||v<0){alert("Please enter a valid meter reading (0 or greater)");return}if(v>i.currentUnits){alert("Current reading cannot be higher than your available units");return}s(v),e(""),alert(`Usage recorded successfully! Used ${h.toFixed(2)} ${a()} costing ${i.currencySymbol||"R"}${f.toFixed(2)}`)}catch(v){console.error("Error recording usage:",v),alert("Error recording usage. Please try again.")}finally{r(!1)}};return c.jsxs("form",{onSubmit:m,className:"space-y-6",children:[c.jsxs("div",{className:`p-5 ${l.card} rounded-xl border ${l.border} shadow-sm`,children:[c.jsxs("h3",{className:`font-semibold ${l.text} mb-4 flex items-center gap-2`,children:[c.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${l.gradient}`,children:c.jsx(Ye,{className:"h-4 w-4 text-white"})}),"Current Status"]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:`flex justify-between items-center p-2 ${l.secondary} rounded-lg`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Available Units:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[i.currentUnits.toFixed(2)," ",a()]})]}),c.jsxs("div",{className:`flex justify-between items-center p-2 ${l.secondary} rounded-lg`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Previous Reading:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[i.previousUnits.toFixed(2)," ",a()]})]}),c.jsxs("div",{className:`flex justify-between items-center p-2 ${l.secondary} rounded-lg`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Usage Since Last:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[o.toFixed(2)," ",a()]})]})]})]}),c.jsxs("div",{children:[c.jsx("label",{htmlFor:"currentReading",className:`block text-sm font-semibold ${l.text} mb-3`,children:"Current Meter Reading"}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${l.gradient}`,children:c.jsx(Ye,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"currentReading",value:t,onChange:x=>e(x.target.value),onWheel:Lr,step:"0.01",min:"0",max:i.currentUnits,placeholder:"Enter current meter reading",className:`w-full pl-12 pr-4 py-4 border-4 ${l.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${l.border} ${l.card} ${l.text} ${l.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${l.textSecondary} opacity-80 font-medium`,children:"📊 Enter the current reading from your electricity meter"})]}),d>0&&c.jsxs("div",{className:`p-6 ${l.card} rounded-xl border ${l.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${l.gradient} mr-3`,children:c.jsx(Vp,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${l.text} text-lg`,children:"Usage Calculation"})]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:`flex justify-between items-center p-3 ${l.secondary} rounded-lg`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Previous Units:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[i.currentUnits.toFixed(2)," ",a()]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${l.secondary} rounded-lg`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"New Reading:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[d.toFixed(2)," ",a()]})]}),c.jsx("div",{className:`border-t ${l.border} my-3`}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${l.secondary} rounded-lg border ${l.border}`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Units Used:"}),c.jsxs("span",{className:`font-bold text-lg ${l.text}`,children:[h.toFixed(2)," ",a()]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${l.secondary} rounded-lg border ${l.border}`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Cost of Usage:"}),c.jsxs("span",{className:`font-bold text-lg ${l.text}`,children:[i.currencySymbol||"R",f.toFixed(2)]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${l.secondary} rounded-lg border ${l.border}`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Remaining Units:"}),c.jsxs("span",{className:`font-bold text-lg ${l.text}`,children:[d.toFixed(2)," ",a()]})]})]}),h<0&&c.jsx("div",{className:`mt-4 p-4 ${l.secondary} border ${l.border} rounded-xl`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-1 rounded-lg ${l.accent} mr-2`,children:c.jsx("span",{className:"text-white text-xs",children:"⚠️"})}),c.jsx("span",{className:`${l.textSecondary} text-sm font-medium`,children:"Warning: New reading cannot be higher than available units"})]})})]}),c.jsx("button",{type:"submit",disabled:n||d<=0||d>i.currentUnits,className:`w-full bg-gradient-to-r ${l.gradient} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:c.jsx("div",{className:"flex items-center justify-center gap-2",children:n?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),"Recording Usage..."]}):c.jsxs(c.Fragment,{children:[c.jsx(Ye,{className:"h-5 w-5"}),"Record Usage"]})})}),c.jsxs("div",{className:`text-center p-4 ${l.card} rounded-xl border ${l.border}`,children:[c.jsxs("div",{className:"flex items-center justify-center mb-2",children:[c.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${l.gradient} mr-2`,children:c.jsx("span",{className:"text-white text-xs",children:"💡"})}),c.jsx("span",{className:`text-sm font-semibold ${l.text}`,children:"How it works"})]}),c.jsx("p",{className:`text-xs ${l.textSecondary} leading-relaxed`,children:"Record your current meter reading to track electricity usage. The system will calculate how many units you've used since the last recording."})]})]})}function lc({children:t,className:e=""}){const{theme:n}=me(),[r,i]=N.useState(!0),[s,o]=N.useState(!1),a=N.useRef(null);N.useEffect(()=>{const u=()=>{if(a.current){const{scrollWidth:d,clientWidth:h}=a.current;o(d>h)}};return u(),window.addEventListener("resize",u),()=>window.removeEventListener("resize",u)},[t]),N.useEffect(()=>{if(!s){i(!1);return}const u=setTimeout(()=>{i(!1)},5e3);return()=>clearTimeout(u)},[s]);const l=()=>{i(!1)};return c.jsxs("div",{className:`relative ${e}`,children:[r&&s&&c.jsx("div",{className:"md:hidden absolute top-2 right-2 z-20 pointer-events-none",children:c.jsxs("div",{className:`flex items-center gap-2 ${n.primary} text-white px-3 py-2 rounded-full shadow-lg backdrop-blur-sm animate-bounce`,children:[c.jsx("span",{className:"text-xs font-medium",children:"Swipe to see more"}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-pulse"}),c.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-pulse",style:{animationDelay:"0.2s"}}),c.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-pulse",style:{animationDelay:"0.4s"}})]}),c.jsx("span",{className:"text-sm",children:"→"})]})}),s&&c.jsx("div",{className:`md:hidden absolute top-0 right-0 bottom-0 w-8 bg-gradient-to-l ${n.card}/80 to-transparent z-10 pointer-events-none`}),c.jsx("div",{ref:a,className:"overflow-x-auto overflow-y-hidden",style:{WebkitOverflowScrolling:"touch"},onScroll:l,children:t})]})}ss.register(oc,ac,to,gS,Dg,$g);function HS(){const{state:t,usageSinceLastRecording:e,getDisplayUnitName:n,weeklyUsageTotal:r,monthlyUsageTotal:i,weeklyPurchaseTotal:s,monthlyPurchaseTotal:o}=Xe(),{theme:a,currentTheme:l}=me(),u=(v,b="bg-gray-800/50")=>l==="dark"?b:v,d=t.usageHistory.reduce((v,b)=>v+b.usage,0),h=t.usageHistory.length>0?d/t.usageHistory.length:0,f=t.usageHistory.slice(-7).reverse(),m={labels:f.length>0?f.map((v,b)=>new Date(v.timestamp).toLocaleDateString("en-US",{month:"short",day:"numeric"})):["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],datasets:[{label:`Daily Usage (${n()})`,data:f.length>0?f.map(v=>v.usage):[12.5,15.2,8.7,22.1,18.9,14.3,16.8],backgroundColor:["rgba(99, 102, 241, 0.8)","rgba(139, 92, 246, 0.8)","rgba(236, 72, 153, 0.8)","rgba(34, 197, 94, 0.8)","rgba(251, 146, 60, 0.8)","rgba(14, 165, 233, 0.8)","rgba(168, 85, 247, 0.8)"],borderColor:["rgba(99, 102, 241, 1)","rgba(139, 92, 246, 1)","rgba(236, 72, 153, 1)","rgba(34, 197, 94, 1)","rgba(251, 146, 60, 1)","rgba(14, 165, 233, 1)","rgba(168, 85, 247, 1)"],borderWidth:2,borderRadius:8,borderSkipped:!1}]},x={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},title:{display:!0,text:"Daily Usage Trend",font:{size:16,weight:"bold"},color:a.text==="text-gray-900"?"#1f2937":"#f9fafb",padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"rgba(255, 255, 255, 0.2)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(v){return`Usage: ${v.parsed.y.toFixed(2)} ${n()}`}}}},scales:{y:{beginAtZero:!0,grid:{color:"rgba(156, 163, 175, 0.2)",drawBorder:!1},ticks:{color:a.textSecondary==="text-gray-600"?"#6b7280":"#9ca3af",font:{size:12},callback:function(v){return v+" "+n()}}},x:{grid:{display:!1},ticks:{color:a.textSecondary==="text-gray-600"?"#6b7280":"#9ca3af",font:{size:12}}}},animation:{duration:1500,easing:"easeInOutQuart"}};return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.jsxs("div",{className:`${a.card} rounded-2xl shadow-lg p-6 border ${a.border} ${u("bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50","bg-gray-800/50")}`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${a.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md",children:c.jsx(Ye,{className:"h-5 w-5 text-white"})}),"Record New Reading"]}),c.jsx("div",{className:`${u("bg-white/60","bg-gray-700/50")} backdrop-blur-sm rounded-xl p-4`,children:c.jsx(BS,{})})]}),c.jsxs("div",{className:`${a.card} rounded-2xl shadow-lg p-6 border ${a.border} ${u("bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50","bg-gray-800/50")}`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${a.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-md",children:c.jsx(To,{className:"h-5 w-5 text-white"})}),"Recent Readings"]}),c.jsxs("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:[t.usageHistory.slice(0,10).map(v=>c.jsx("div",{className:`p-4 ${u("bg-white/60","bg-gray-700/50")} backdrop-blur-sm rounded-xl border ${u("border-white/40","border-gray-600")} shadow-sm hover:shadow-md transition-all duration-200`,children:c.jsxs("div",{className:"flex justify-between items-start",children:[c.jsxs("div",{children:[c.jsxs("p",{className:`font-semibold ${a.text} text-lg`,children:[v.currentUnits.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-sm ${a.textSecondary} opacity-80`,children:["Previous: ",v.previousUnits.toFixed(2)," ",n()]}),c.jsx("p",{className:`text-xs ${a.textSecondary} mt-1 opacity-70`,children:v.timestamp})]}),c.jsxs("div",{className:"text-right",children:[c.jsxs("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${v.usage>0?"bg-gradient-to-r from-rose-100 to-pink-100 text-rose-700 border border-rose-200":"bg-gradient-to-r from-gray-100 to-slate-100 text-gray-700 border border-gray-200"}`,children:["-",v.usage.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-xs ${a.textSecondary} mt-1 font-medium`,children:[t.currencySymbol||"R",(v.usage*t.unitCost).toFixed(2)]})]})]})},v.id)),t.usageHistory.length===0&&c.jsxs("div",{className:`text-center py-12 ${u("bg-white/40","bg-gray-700/40")} backdrop-blur-sm rounded-xl border ${u("border-white/40","border-gray-600")}`,children:[c.jsx("div",{className:`p-4 rounded-2xl ${a.secondary} w-fit mx-auto mb-4`,children:c.jsx(jt,{className:`h-12 w-12 ${a.textSecondary}`})}),c.jsx("p",{className:`text-sm ${a.textSecondary} opacity-80 font-medium`,children:"No usage records yet"}),c.jsx("p",{className:`text-xs ${a.textSecondary} opacity-60 mt-1`,children:"Record your first reading above to get started"})]})]})]})]}),c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.jsxs("div",{className:"space-y-3",children:[c.jsx("div",{className:`${a.card} rounded-xl shadow-lg p-3 border ${a.border}`,children:c.jsxs(lc,{children:[c.jsxs("div",{className:"flex items-center space-x-2 mb-2 min-w-max",children:[c.jsx("h3",{className:`text-base font-semibold ${a.text}`,children:"This Week"}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`p-1 rounded-lg ${a.secondary}`,children:c.jsx(Ve,{className:`h-3 w-3 ${a.textSecondary}`})}),c.jsx("div",{className:`p-1 rounded-lg ${a.secondary}`,children:c.jsx(jt,{className:`h-3 w-3 ${a.textSecondary}`})})]})]}),c.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0 sm:space-x-4 min-w-max",children:[c.jsxs("div",{className:"text-center sm:text-right",children:[c.jsx("p",{className:`text-xs font-medium ${a.textSecondary}`,children:"Purchases"}),c.jsxs("p",{className:`text-base font-bold ${a.text}`,children:[t.currencySymbol,s.toFixed(2)]})]}),c.jsx("div",{className:`border-t sm:border-t-0 sm:border-l ${a.border} h-px sm:h-6 w-full sm:w-px`}),c.jsxs("div",{className:"text-center sm:text-right",children:[c.jsx("p",{className:`text-xs font-medium ${a.textSecondary}`,children:"Usage"}),c.jsxs("p",{className:`text-base font-bold ${a.text}`,children:[r.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-xs ${a.textSecondary}`,children:["Cost: ",t.currencySymbol,(r*t.unitCost).toFixed(2)]})]})]})]})}),c.jsx("div",{className:`${a.card} rounded-xl shadow-lg p-3 border ${a.border}`,children:c.jsxs(lc,{children:[c.jsxs("div",{className:"flex items-center space-x-2 mb-2 min-w-max",children:[c.jsx("h3",{className:`text-base font-semibold ${a.text}`,children:"This Month"}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`p-1 rounded-lg ${a.secondary}`,children:c.jsx(Ve,{className:`h-3 w-3 ${a.textSecondary}`})}),c.jsx("div",{className:`p-1 rounded-lg ${a.secondary}`,children:c.jsx(jt,{className:`h-3 w-3 ${a.textSecondary}`})})]})]}),c.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0 sm:space-x-4 min-w-max",children:[c.jsxs("div",{className:"text-center sm:text-right",children:[c.jsx("p",{className:`text-xs font-medium ${a.textSecondary}`,children:"Purchases"}),c.jsxs("p",{className:`text-base font-bold ${a.text}`,children:[t.currencySymbol,o.toFixed(2)]})]}),c.jsx("div",{className:`border-t sm:border-t-0 sm:border-l ${a.border} h-px sm:h-6 w-full sm:w-px`}),c.jsxs("div",{className:"text-center sm:text-right",children:[c.jsx("p",{className:`text-xs font-medium ${a.textSecondary}`,children:"Usage"}),c.jsxs("p",{className:`text-base font-bold ${a.text}`,children:[i.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-xs ${a.textSecondary}`,children:["Cost: ",t.currencySymbol,(i*t.unitCost).toFixed(2)]})]})]})]})})]}),c.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[c.jsx("div",{className:`${a.card} rounded-xl shadow-lg p-3 border ${a.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-xl bg-gradient-to-br ${a.gradient} shadow-lg`,children:c.jsx(Ye,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsx("p",{className:`text-xs font-medium ${a.textSecondary}`,children:"Current Reading"}),c.jsx("p",{className:`text-lg font-bold ${a.text}`,children:t.currentUnits.toFixed(2)}),c.jsx("p",{className:`text-xs ${a.textSecondary} font-medium`,children:n()})]})]})}),c.jsx("div",{className:`${a.card} rounded-xl shadow-lg p-3 border ${a.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-xl bg-gradient-to-br ${a.gradient} shadow-lg`,children:c.jsx(jt,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsx("p",{className:`text-xs font-medium ${a.textSecondary}`,children:"Usage Since Last"}),c.jsx("p",{className:`text-lg font-bold ${a.text}`,children:e.toFixed(2)}),c.jsx("p",{className:`text-xs ${a.textSecondary} font-medium`,children:n()})]})]})}),c.jsx("div",{className:`${a.card} rounded-xl shadow-lg p-3 border ${a.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-xl bg-gradient-to-br ${a.gradient} shadow-lg`,children:c.jsx(To,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsx("p",{className:`text-xs font-medium ${a.textSecondary}`,children:"Total Usage"}),c.jsx("p",{className:`text-lg font-bold ${a.text}`,children:d.toFixed(2)}),c.jsx("p",{className:`text-xs ${a.textSecondary} font-medium`,children:n()})]})]})}),c.jsx("div",{className:`${a.card} rounded-xl shadow-lg p-3 border ${a.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-xl bg-gradient-to-br ${a.gradient} shadow-lg`,children:c.jsx(Hd,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsx("p",{className:`text-xs font-medium ${a.textSecondary}`,children:"Average Usage"}),c.jsx("p",{className:`text-lg font-bold ${a.text}`,children:h.toFixed(2)}),c.jsx("p",{className:`text-xs ${a.textSecondary} font-medium`,children:n()})]})]})})]})]}),c.jsxs("div",{className:`${a.card} rounded-2xl shadow-lg p-8 border ${a.border} ${u("bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50","bg-gray-800/50")}`,children:[c.jsx("div",{className:"flex items-center justify-between mb-6",children:c.jsxs("div",{children:[c.jsxs("h2",{className:`text-2xl font-bold ${a.text} flex items-center gap-3`,children:[c.jsx("div",{className:"p-3 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg",children:c.jsx(ru,{className:"h-6 w-6 text-white"})}),"Usage Analytics"]}),c.jsx("p",{className:`mt-2 ${a.textSecondary} opacity-80`,children:"Visual representation of your daily electricity consumption"})]})}),c.jsxs("div",{className:"h-80 relative",children:[c.jsx("div",{className:`absolute inset-0 ${u("bg-gradient-to-br from-white/80 to-white/40","bg-gray-700/40")} rounded-xl backdrop-blur-sm`}),c.jsx("div",{className:"relative h-full p-4",children:c.jsx(LS,{data:m,options:x})})]})]}),c.jsxs("div",{className:`${a.card} rounded-2xl shadow-lg p-8 border ${a.border} ${u("bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50","bg-gray-800/50")}`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${a.text} mb-6 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-amber-500 to-orange-600 shadow-md",children:c.jsx(Hd,{className:"h-5 w-5 text-white"})}),"How Usage is Calculated"]}),c.jsx("div",{className:`p-6 ${a.card} rounded-xl border ${a.border} shadow-sm`,children:c.jsxs("div",{className:"space-y-4 text-sm",children:[c.jsxs("div",{className:`flex justify-between items-center p-3 ${a.secondary} rounded-lg border ${a.border}`,children:[c.jsx("span",{className:`${a.textSecondary} font-medium`,children:"Previous Reading:"}),c.jsxs("span",{className:`${a.text} font-bold text-lg`,children:[t.previousUnits.toFixed(2)," ",n()]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${a.secondary} rounded-lg border ${a.border}`,children:[c.jsx("span",{className:`${a.textSecondary} font-medium`,children:"Current Reading:"}),c.jsxs("span",{className:`${a.text} font-bold text-lg`,children:[t.currentUnits.toFixed(2)," ",n()]})]}),c.jsx("div",{className:`border-t ${a.border} my-4`}),c.jsxs("div",{className:`flex justify-between items-center p-4 ${a.secondary} rounded-lg border ${a.border}`,children:[c.jsx("span",{className:`${a.text} font-semibold`,children:"Usage Since Last Recording:"}),c.jsxs("div",{className:"text-right",children:[c.jsxs("div",{className:`${a.textSecondary} text-sm mb-1`,children:[t.previousUnits.toFixed(2)," - ",t.currentUnits.toFixed(2)]}),c.jsxs("span",{className:`${a.text} font-bold text-xl`,children:[e.toFixed(2)," ",n()]})]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${a.secondary} rounded-lg border ${a.border}`,children:[c.jsx("span",{className:`${a.textSecondary} font-medium`,children:"Cost of Usage:"}),c.jsxs("span",{className:`${a.text} font-bold text-lg`,children:[t.currencySymbol||"R",(e*t.unitCost).toFixed(2)]})]})]})})]})]})}function WS({history:t}){const{state:e,getDisplayUnitName:n}=Xe(),{theme:r,currentTheme:i}=me();return t.length===0?null:c.jsx(lc,{children:c.jsxs("table",{className:`min-w-full divide-y ${i==="dark"?"divide-gray-600":"divide-gray-200"}`,style:{minWidth:"600px"},children:[c.jsx("thead",{className:r.secondary,children:c.jsxs("tr",{children:[c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Type"}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Date & Time"}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Details"}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:n()}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Amount"})]})}),c.jsx("tbody",{className:`${r.card} divide-y ${i==="dark"?"divide-gray-600":"divide-gray-200"}`,children:t.map(s=>c.jsxs("tr",{className:`${i==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"} transition-colors duration-200`,children:[c.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:c.jsx("div",{className:"flex items-center",children:s.type==="purchase"?c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-lg ${r.secondary}`,children:c.jsx(Ve,{className:`h-4 w-4 ${r.textSecondary}`})}),c.jsx("div",{className:"ml-3",children:c.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${i==="dark"?"bg-green-900/30 text-green-400":"bg-green-100 text-green-800"}`,children:[c.jsx(O1,{className:"mr-1 h-3 w-3"}),"Purchase"]})})]}):c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-lg ${i==="dark"?"bg-red-900/30":"bg-red-100"}`,children:c.jsx(jt,{className:"h-4 w-4 text-red-600"})}),c.jsx("div",{className:"ml-3",children:c.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${i==="dark"?"bg-red-900/30 text-red-400":"bg-red-100 text-red-800"}`,children:[c.jsx(D1,{className:"mr-1 h-3 w-3"}),"Usage"]})})]})})}),c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${r.text}`,children:s.timestamp}),c.jsx("td",{className:`px-6 py-4 text-sm ${r.text}`,children:s.type==="purchase"?c.jsxs("div",{children:[c.jsx("p",{className:"font-medium",children:"Electricity Purchase"}),c.jsxs("p",{className:`text-xs ${r.textSecondary}`,children:["@ ",e.currencySymbol||"R",s.unitCost.toFixed(2)," per ",n()]})]}):c.jsxs("div",{children:[c.jsx("p",{className:"font-medium",children:"Usage Recording"}),c.jsxs("p",{className:`text-xs ${r.textSecondary}`,children:["From ",s.previousUnits.toFixed(2)," to ",s.currentUnits.toFixed(2)," ",n()]})]})}),c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${r.text}`,children:s.type==="purchase"?c.jsxs("span",{className:`${r.text} font-medium`,children:["+",s.units.toFixed(2)]}):c.jsxs("span",{className:`${r.text} font-medium`,children:["-",s.usage.toFixed(2)]})}),c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${r.text}`,children:s.type==="purchase"?c.jsxs("span",{className:`${r.text} font-medium`,children:["+",e.currencySymbol||"R",s.currency.toFixed(2)]}):c.jsxs("span",{className:`${r.text} font-medium`,children:["-",e.currencySymbol||"R",(s.usage*e.unitCost).toFixed(2)]})})]},`${s.type}-${s.id}`))})]})})}function VS(){const t=Jn(),[e,n]=N.useState("all"),[r,i]=N.useState(""),[s,o]=N.useState(!1),[a,l]=N.useState(""),[u,d]=N.useState(""),[h,f]=N.useState(new Date),m=N.useRef(null),{state:x,getDisplayUnitName:v,weeklyPurchaseTotal:b,monthlyPurchaseTotal:g,weeklyUsageTotal:p,monthlyUsageTotal:y}=Xe(),{theme:w,currentTheme:_}=me(),k=[...x.purchases.map(T=>({...T,type:"purchase"})),...x.usageHistory.map(T=>({...T,type:"usage"}))].sort((T,U)=>new Date(U.date)-new Date(T.date)).filter(T=>{const U=e==="all"||T.type===e;if(r&&!a&&!u)return U&&T.date.includes(r);if(a||u){const z=new Date(T.date),B=a?new Date(a):null,R=u?new Date(u):null;let M=!0;return B&&(M=M&&z>=B),R&&(M=M&&z<=R),U&&M}return U});x.purchases.reduce((T,U)=>T+U.currency,0),x.usageHistory.reduce((T,U)=>T+U.usage,0)*x.unitCost;const E=[{id:"all",name:"All Activity",icon:Ro},{id:"purchase",name:"Purchases",icon:Ve},{id:"usage",name:"Usage",icon:jt}],C=()=>{i(""),o(!1)},$=()=>{i(""),l(""),d(""),o(!1)},D=r||a||u;return N.useEffect(()=>{const T=setInterval(()=>{f(new Date)},1e3);return()=>clearInterval(T)},[]),N.useEffect(()=>{const T=U=>{m.current&&!m.current.contains(U.target)&&o(!1)};if(s)return document.addEventListener("mousedown",T),()=>document.removeEventListener("mousedown",T)},[s]),c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:`${w.card} rounded-2xl shadow-lg border ${w.border}`,children:[c.jsx("div",{className:`p-4 md:p-8 ${w.secondary} rounded-t-2xl`,children:c.jsxs("div",{className:"space-y-4",children:[c.jsx("div",{className:"flex flex-wrap gap-2",children:E.map(T=>c.jsxs("button",{onClick:()=>n(T.id),className:`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${e===T.id?`${w.primary} text-white`:`${w.text} hover:${w.secondary}`}`,children:[c.jsx(T.icon,{className:"mr-2 h-4 w-4"}),T.name]},T.id))}),c.jsxs("div",{ref:m,className:"space-y-3",children:[c.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[c.jsx(A1,{className:`h-5 w-5 ${w.textSecondary}`}),c.jsx("span",{className:`text-sm font-medium ${w.text}`,children:"Filter by Date"})]}),c.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[c.jsx("input",{type:"date",value:r||new Date().toISOString().split("T")[0],onChange:T=>{i(T.target.value),T.target.value&&(l(""),d(""))},className:`px-3 py-2 border ${w.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${w.card} ${w.text} text-sm w-full sm:w-40`,placeholder:"Filter by date"}),c.jsx("button",{onClick:()=>o(!s),className:`px-3 py-2 border ${w.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${w.card} ${w.text} hover:${w.secondary} transition-colors text-sm ${a||u?"ring-2 ring-blue-500":""}`,children:"📅 Range"}),D&&c.jsx("button",{onClick:$,className:`px-3 py-2 text-sm ${w.textSecondary} hover:${w.text} transition-colors border ${w.border} rounded-lg`,children:"Clear All"})]}),s&&c.jsx("div",{className:`mt-3 p-4 ${w.card} border ${w.border} rounded-xl shadow-xl w-full max-w-md`,children:c.jsxs("div",{className:"space-y-4",children:[c.jsx("h3",{className:`font-semibold ${w.text} text-sm`,children:"Select Date Range"}),c.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[c.jsxs("div",{children:[c.jsx("label",{className:`block text-xs font-medium ${w.textSecondary} mb-1`,children:"From Date"}),c.jsx("input",{type:"date",value:a,onChange:T=>{l(T.target.value),i("")},className:`w-full px-3 py-2 border ${w.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${w.card} ${w.text} text-sm`})]}),c.jsxs("div",{children:[c.jsx("label",{className:`block text-xs font-medium ${w.textSecondary} mb-1`,children:"To Date"}),c.jsx("input",{type:"date",value:u,onChange:T=>{d(T.target.value),i("")},className:`w-full px-3 py-2 border ${w.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${w.card} ${w.text} text-sm`})]})]}),c.jsxs("div",{className:"flex justify-between items-center pt-2",children:[c.jsx("button",{onClick:()=>{l(""),d("")},className:`text-xs ${w.textSecondary} hover:${w.text} transition-colors`,children:"Clear Range"}),c.jsx("button",{onClick:C,className:`px-4 py-2 bg-gradient-to-r ${w.gradient} text-white rounded-lg hover:opacity-90 transition-colors text-sm font-medium`,children:"Apply"})]})]})})]})]})}),c.jsx("div",{className:`border-t ${w.border}`,children:c.jsx(WS,{history:k})})]}),k.length===0&&c.jsxs("div",{className:`${w.card} rounded-2xl shadow-lg p-16 text-center border ${w.border}`,children:[c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:`absolute inset-0 ${w.secondary} rounded-full opacity-20 scale-110`}),c.jsx("div",{className:`relative p-6 rounded-2xl ${w.secondary} w-fit mx-auto`,children:c.jsx(Ro,{className:`h-16 w-16 ${w.textSecondary}`})})]}),c.jsx("h3",{className:`mt-6 text-2xl font-bold ${w.text}`,children:"No history found"}),c.jsx("p",{className:`mt-3 ${w.textSecondary} opacity-80 text-lg leading-relaxed max-w-md mx-auto`,children:D?"No records found for the selected date range. Try adjusting your filters or clear them to see all records.":"Start by making purchases or recording usage to see your history here."}),!D&&c.jsxs("div",{className:"mt-8 flex flex-col sm:flex-row justify-center gap-4",children:[c.jsx("button",{onClick:()=>t("/purchases"),className:`bg-gradient-to-r ${w.gradient} text-white px-6 py-3 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx("span",{children:"💰"}),"Add Purchase"]})}),c.jsx("button",{onClick:()=>t("/usage"),className:`bg-gradient-to-r ${w.gradient} text-white px-6 py-3 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx("span",{children:"⚡"}),"Record Usage"]})})]})]})]})}function YS(){const[t,e]=N.useState(!1),n=N.useRef(null),{currentTheme:r,setCurrentTheme:i,theme:s}=me(),o=a=>a?`${s.border} ring-2 ring-opacity-50`:r==="dark"?"border-gray-600 hover:border-gray-500":"border-gray-200 hover:border-gray-300";return N.useEffect(()=>{function a(l){n.current&&!n.current.contains(l.target)&&e(!1)}return document.addEventListener("mousedown",a),()=>{document.removeEventListener("mousedown",a)}},[]),c.jsxs("div",{className:"space-y-8",children:[c.jsxs("div",{children:[c.jsxs("h3",{className:`text-lg font-semibold ${s.text} mb-4 flex items-center`,children:[c.jsx(Yp,{className:"mr-2 h-5 w-5"}),"Choose Theme"]}),c.jsxs("div",{className:"relative",ref:n,children:[c.jsxs("button",{onClick:()=>e(!t),className:`w-full p-4 ${s.card} border ${s.border} rounded-lg flex items-center justify-between hover:${s.secondary} transition-colors`,children:[c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"space-y-2",children:[c.jsx("div",{className:`h-6 w-16 ${mt[r].gradient} bg-gradient-to-r rounded`}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`h-2 w-2 ${mt[r].primary} rounded`}),c.jsx("div",{className:`h-2 w-2 ${mt[r].accent} rounded`}),c.jsx("div",{className:`h-2 w-2 ${mt[r].secondary} rounded`})]})]}),c.jsx("span",{className:`text-lg font-medium ${s.text}`,children:mt[r].name})]}),c.jsx(Jl,{className:`h-5 w-5 ${s.textSecondary} transition-transform ${t?"rotate-180":""}`})]}),t&&c.jsx("div",{className:`absolute top-full left-0 right-0 mt-2 ${s.card} border ${s.border} rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto`,children:c.jsx("div",{className:"grid grid-cols-1 gap-2 p-2",children:Object.entries(mt).map(([a,l])=>c.jsx("button",{onClick:()=>{i(a),e(!1)},className:`relative p-4 rounded-lg border-2 transition-all hover:scale-105 text-left ${o(r===a)}`,children:c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"space-y-2 flex-shrink-0",children:[c.jsx("div",{className:`h-8 w-20 ${l.gradient} bg-gradient-to-r rounded`}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`h-3 w-3 ${l.primary} rounded`}),c.jsx("div",{className:`h-3 w-3 ${l.accent} rounded`}),c.jsx("div",{className:`h-3 w-3 ${l.secondary} rounded`})]})]}),c.jsx("div",{className:"flex-1",children:c.jsx("p",{className:`text-sm font-medium ${s.text}`,children:l.name})}),r===a&&c.jsx("div",{className:"flex-shrink-0",children:c.jsx(L1,{className:`h-5 w-5 ${s.text}`})})]})},a))})})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:`text-lg font-semibold ${s.text} mb-4`,children:"Preview"}),c.jsxs("div",{className:`p-6 ${s.card} rounded-lg border ${s.border} space-y-4`,children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsx("h4",{className:`text-2xl font-bold ${s.text}`,children:"Sample Dashboard"}),c.jsx("div",{className:`px-3 py-1 ${s.primary} text-white rounded-full text-base`,children:"Active"})]}),c.jsx("div",{className:"grid grid-cols-1 gap-4",children:c.jsxs("div",{className:`p-4 ${s.card} border ${s.border} rounded-lg shadow-sm`,children:[c.jsx("p",{className:`text-base ${s.textSecondary}`,children:"Current Units"}),c.jsx("p",{className:`text-2xl font-bold ${s.text}`,children:"125.50"})]})}),c.jsxs("div",{className:"flex space-x-2",children:[c.jsx("button",{className:`px-4 py-2 ${s.primary} text-white rounded-lg text-base`,children:"Primary Button"}),c.jsx("button",{className:`px-4 py-2 border ${s.border} ${s.text} rounded-lg text-base`,children:"Secondary Button"})]})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:`text-lg font-semibold ${s.text} mb-4`,children:"Reset Appearance"}),c.jsx("div",{className:"space-y-3",children:c.jsx("button",{onClick:()=>{i("electric"),e(!1)},className:`w-full px-6 py-3 bg-gradient-to-r ${s.gradient} text-white rounded-lg hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl`,children:"Reset to Default Theme"})})]})]})}function XS(){const[t,e]=N.useState(!1),[n,r]=N.useState(!1),[i,s]=N.useState(!1),{state:o,factoryReset:a,dashboardReset:l}=Xe(),{theme:u,currentTheme:d}=me(),h=(x,v="bg-gray-800/50")=>d==="dark"?v:x,f=async()=>{s(!0);try{a(),e(!1),alert("Factory reset completed successfully! The app will now restart.")}catch(x){console.error("Error during factory reset:",x),alert("Error during factory reset. Please try again.")}finally{s(!1)}},m=async()=>{s(!0);try{l(),r(!1),alert("Dashboard data reset successfully! Your history has been preserved.")}catch(x){console.error("Error during dashboard reset:",x),alert("Error during dashboard reset. Please try again.")}finally{s(!1)}};return c.jsxs("div",{className:"space-y-8",children:[c.jsx("div",{className:`p-6 border ${u.border} rounded-lg ${h("bg-white","bg-gray-800/50")}`,children:c.jsxs("div",{className:"flex items-start",children:[c.jsx("div",{className:"flex-shrink-0",children:c.jsx(Kp,{className:"h-6 w-6 text-orange-600"})}),c.jsxs("div",{className:"ml-4 flex-1",children:[c.jsx("h3",{className:`text-lg font-semibold ${u.text}`,children:"Dashboard Data Reset"}),c.jsx("p",{className:`mt-2 text-sm ${u.textSecondary}`,children:"Reset current units and previous readings to zero. This will clear your dashboard data but preserve your purchase and usage history for reference."}),c.jsxs("div",{className:`mt-4 p-3 ${h("bg-orange-50 border-orange-200","bg-orange-900/20 border-orange-700")} border rounded-lg`,children:[c.jsx("h4",{className:`text-sm font-medium mb-2 ${d==="dark"?"text-orange-300":"text-orange-800"}`,children:"What will be reset:"}),c.jsxs("ul",{className:`text-xs space-y-1 ${d==="dark"?"text-orange-200":"text-orange-700"}`,children:[c.jsx("li",{children:"• Current units will be set to 0"}),c.jsx("li",{children:"• Previous units will be set to 0"}),c.jsx("li",{children:"• Usage since last recording will be reset"})]}),c.jsx("h4",{className:`text-sm font-medium mt-3 mb-2 ${d==="dark"?"text-orange-300":"text-orange-800"}`,children:"What will be preserved:"}),c.jsxs("ul",{className:`text-xs space-y-1 ${d==="dark"?"text-orange-200":"text-orange-700"}`,children:[c.jsx("li",{children:"• All purchase history"}),c.jsx("li",{children:"• All usage history"}),c.jsx("li",{children:"• Settings and preferences"}),c.jsx("li",{children:"• Theme and appearance settings"})]})]}),n?c.jsxs("div",{className:"mt-4 space-y-3",children:[c.jsxs("div",{className:`flex items-center p-3 ${h("bg-red-50 border-red-200","bg-red-900/20 border-red-700")} border rounded-lg`,children:[c.jsx(_r,{className:"h-5 w-5 text-red-600 mr-2"}),c.jsx("span",{className:`text-sm ${d==="dark"?"text-red-200":"text-red-800"}`,children:"Are you sure? This action cannot be undone."})]}),c.jsxs("div",{className:"flex space-x-3",children:[c.jsx("button",{onClick:m,disabled:i,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50",children:i?"Resetting...":"Yes, Reset Dashboard"}),c.jsx("button",{onClick:()=>r(!1),className:`px-4 py-2 border ${u.border} ${u.text} rounded-lg hover:${u.secondary} transition-colors`,children:"Cancel"})]})]}):c.jsx("button",{onClick:()=>r(!0),className:"mt-4 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors",children:"Reset Dashboard Data"})]})]})}),c.jsx("div",{className:`p-6 border ${h("border-red-200 bg-red-50","border-red-700 bg-red-900/20")} rounded-lg`,children:c.jsxs("div",{className:"flex items-start",children:[c.jsx("div",{className:"flex-shrink-0",children:c.jsx(U1,{className:"h-6 w-6 text-red-600"})}),c.jsxs("div",{className:"ml-4 flex-1",children:[c.jsx("h3",{className:`text-lg font-semibold ${d==="dark"?"text-red-300":"text-red-800"}`,children:"Factory Reset"}),c.jsx("p",{className:`mt-2 text-sm ${d==="dark"?"text-red-200":"text-red-700"}`,children:"Completely reset the app to its initial state. This will delete ALL data including purchases, usage history, and settings. You will need to set up the app again from scratch."}),c.jsxs("div",{className:`mt-4 p-3 ${h("bg-red-100 border-red-300","bg-red-900/30 border-red-600")} border rounded-lg`,children:[c.jsx("h4",{className:`text-sm font-medium mb-2 ${d==="dark"?"text-red-300":"text-red-800"}`,children:"What will be deleted:"}),c.jsxs("ul",{className:`text-xs space-y-1 ${d==="dark"?"text-red-200":"text-red-700"}`,children:[c.jsx("li",{children:"• All purchase records"}),c.jsx("li",{children:"• All usage history"}),c.jsx("li",{children:"• Current and previous unit readings"}),c.jsx("li",{children:"• All settings and preferences"}),c.jsx("li",{children:"• Theme and appearance settings"})]}),c.jsxs("div",{className:`mt-3 p-2 ${h("bg-red-200 border-red-400","bg-red-900/40 border-red-500")} border rounded text-xs ${d==="dark"?"text-red-200":"text-red-800"}`,children:[c.jsx("strong",{children:"Warning:"})," This action is irreversible. Make sure you have backed up any important data."]})]}),t?c.jsxs("div",{className:"mt-4 space-y-3",children:[c.jsxs("div",{className:`flex items-center p-3 ${h("bg-red-100 border-red-300","bg-red-900/30 border-red-600")} border rounded-lg`,children:[c.jsx(_r,{className:"h-5 w-5 text-red-700 mr-2"}),c.jsx("span",{className:`text-sm font-medium ${d==="dark"?"text-red-200":"text-red-800"}`,children:"This will permanently delete ALL your data. Are you absolutely sure?"})]}),c.jsxs("div",{className:"flex space-x-3",children:[c.jsx("button",{onClick:f,disabled:i,className:"px-4 py-2 bg-red-700 text-white rounded-lg hover:bg-red-800 transition-colors disabled:opacity-50",children:i?"Resetting...":"Yes, Delete Everything"}),c.jsx("button",{onClick:()=>e(!1),className:`px-4 py-2 border ${u.border} ${u.text} rounded-lg hover:${u.secondary} transition-colors`,children:"Cancel"})]})]}):c.jsx("button",{onClick:()=>e(!0),className:"mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Factory Reset"})]})]})}),c.jsxs("div",{className:`p-6 ${u.card} border ${u.border} rounded-lg`,children:[c.jsx("h3",{className:`text-lg font-semibold ${u.text} mb-4`,children:"Current Data Summary"}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[c.jsxs("div",{children:[c.jsx("h4",{className:`font-medium ${u.text} mb-2`,children:"App Data"}),c.jsxs("ul",{className:`space-y-1 ${u.textSecondary}`,children:[c.jsxs("li",{children:["• Current Units: ",o.currentUnits.toFixed(2)]}),c.jsxs("li",{children:["• Previous Units: ",o.previousUnits.toFixed(2)]}),c.jsxs("li",{children:["• Unit Cost: R",o.unitCost.toFixed(2)]}),c.jsxs("li",{children:["• Threshold: ",o.thresholdLimit.toFixed(2)," units"]})]})]}),c.jsxs("div",{children:[c.jsx("h4",{className:`font-medium ${u.text} mb-2`,children:"History"}),c.jsxs("ul",{className:`space-y-1 ${u.textSecondary}`,children:[c.jsxs("li",{children:["• Purchases: ",o.purchases.length," records"]}),c.jsxs("li",{children:["• Usage Records: ",o.usageHistory.length," records"]}),c.jsxs("li",{children:["• Last Reset: ",o.lastResetDate?new Date(o.lastResetDate).toLocaleDateString():"Never"]}),c.jsxs("li",{children:["• App Initialized: ",o.isInitialized?"Yes":"No"]})]})]})]})]})]})}function KS(){var z,B;const{state:t,updateSettings:e}=Xe(),{theme:n,currentTheme:r}=me(),[i]=wy(),s=(R,M="bg-gray-800/50")=>r==="dark"?M:R,[o,a]=N.useState(t.unitCost.toString()),[l,u]=N.useState(t.thresholdLimit.toString()),[d,h]=N.useState(t.currency||"ZAR"),[f,m]=N.useState(t.customCurrencyName||""),[x,v]=N.useState(t.customCurrencySymbol||""),[b,g]=N.useState(t.unitName||"kWh"),[p,y]=N.useState(t.customUnitName||""),[w,_]=N.useState(t.notificationsEnabled||!1),[S,k]=N.useState(t.notificationTime||"18:00"),[j,E]=N.useState(!1),C=[{code:"ZAR",name:"South African Rand",symbol:"R"},{code:"USD",name:"US Dollar",symbol:"$"},{code:"EUR",name:"Euro",symbol:"€"},{code:"GBP",name:"British Pound",symbol:"£"},{code:"JPY",name:"Japanese Yen",symbol:"¥"},{code:"CUSTOM",name:"Custom Currency",symbol:"C"}],$=[{value:"kWh",label:"kWh (Kilowatt Hours)"},{value:"Units",label:"Units"},{value:"custom",label:"Custom"}],D=async R=>{R.preventDefault(),E(!0);try{const M=parseFloat(o),L=parseFloat(l);if(isNaN(M)||M<=0){alert("Please enter a valid unit cost (greater than 0)");return}if(isNaN(L)||L<0){alert("Please enter a valid threshold limit (0 or greater)");return}if(b==="custom"&&!p.trim()){alert("Please enter a custom unit name");return}if(d==="CUSTOM"&&(!f.trim()||!x.trim())){alert("Please enter both custom currency name and symbol");return}const A=C.find(G=>G.code===d),Q=d==="CUSTOM"?x:(A==null?void 0:A.symbol)||"R";e({unitCost:M,thresholdLimit:L,currency:d,currencySymbol:Q,customCurrencyName:d==="CUSTOM"?f.trim():"",customCurrencySymbol:d==="CUSTOM"?x.trim():"",unitName:b,customUnitName:b==="custom"?p.trim():"",notificationsEnabled:w,notificationTime:S}),alert("Settings saved successfully!")}catch(M){console.error("Error saving settings:",M),alert("Error saving settings. Please try again.")}finally{E(!1)}},[T,U]=N.useState("general");return N.useEffect(()=>{const R=i.get("section");R&&["general","appearance","reset"].includes(R)&&U(R)},[i]),c.jsx("div",{className:"space-y-6",children:c.jsx("div",{className:`${n.card} rounded-2xl shadow-lg border ${n.border}`,children:c.jsxs("div",{className:"p-6",children:[T==="general"&&c.jsx("div",{className:"space-y-6",children:c.jsxs("form",{onSubmit:D,className:"space-y-6",children:[c.jsxs("div",{className:`p-6 ${n.card} rounded-xl border ${n.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${n.gradient} mr-3`,children:c.jsx(F1,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Currency Settings"})]}),c.jsxs("div",{children:[c.jsx("label",{htmlFor:"currency",className:`block text-sm font-semibold ${n.text} mb-3`,children:"💰 Currency"}),c.jsx("select",{id:"currency",value:d,onChange:R=>h(R.target.value),className:`w-full px-4 py-4 border-4 ${n.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${n.border} ${n.card} ${n.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200`,children:C.map(R=>c.jsxs("option",{value:R.code,children:[R.symbol," - ",R.name," (",R.code,")"]},R.code))}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"💡 Select your preferred currency for cost calculations"}),d==="CUSTOM"&&c.jsxs("div",{className:"mt-4 space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"customCurrencyName",className:`block text-sm font-semibold ${n.text} mb-2`,children:"🏷️ Custom Currency Name"}),c.jsx("input",{type:"text",id:"customCurrencyName",value:f,onChange:R=>m(R.target.value),placeholder:"Enter currency name (e.g., Bitcoin, Credits, Points)",className:`w-full px-4 py-3 border-4 ${n.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${n.border} ${n.card} ${n.text} ${n.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200`,required:d==="CUSTOM"})]}),c.jsxs("div",{children:[c.jsx("label",{htmlFor:"customCurrencySymbol",className:`block text-sm font-semibold ${n.text} mb-2`,children:"💰 Custom Currency Symbol"}),c.jsx("input",{type:"text",id:"customCurrencySymbol",value:x,onChange:R=>v(R.target.value),placeholder:"Enter symbol (e.g., ₿, Cr, Pts)",maxLength:"5",className:`w-full px-4 py-3 border-4 ${n.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${n.border} ${n.card} ${n.text} ${n.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200`,required:d==="CUSTOM"}),c.jsxs("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:['💰 This symbol will be displayed with all amounts (e.g., "',x||"Cr",'100.00")']})]})]})]})]}),c.jsxs("div",{className:`p-6 ${s("bg-gradient-to-br from-blue-50 to-indigo-50","bg-gray-800/50")} rounded-xl border ${n.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-blue-400 to-indigo-500 mr-3",children:c.jsx(Ye,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Unit Settings"})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"unitName",className:`block text-sm font-semibold ${n.text} mb-3`,children:"⚡ Unit Name"}),c.jsx("select",{id:"unitName",value:b,onChange:R=>g(R.target.value),className:`w-full px-4 py-4 border-4 border-blue-300 rounded-xl focus:ring-4 focus:ring-blue-500 focus:border-blue-600 ${n.card} ${n.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-blue-400`,children:$.map(R=>c.jsx("option",{value:R.value,children:R.label},R.value))}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"⚡ Choose how your units are displayed throughout the app"})]}),b==="custom"&&c.jsxs("div",{children:[c.jsx("label",{htmlFor:"customUnitName",className:`block text-sm font-semibold ${n.text} mb-3`,children:"🎯 Custom Unit Name"}),c.jsx("input",{type:"text",id:"customUnitName",value:p,onChange:R=>y(R.target.value),placeholder:"Enter custom unit name (e.g., Donkey, Credits, Points)",className:`w-full px-4 py-4 border-4 border-purple-300 rounded-xl focus:ring-4 focus:ring-purple-500 focus:border-purple-600 ${n.card} ${n.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-purple-400`,required:b==="custom"}),c.jsxs("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:['🎯 This name will be used everywhere (e.g., "Cost per ',p||"YourUnit",'")']})]})]})]}),c.jsxs("div",{className:`p-6 ${s("bg-gradient-to-br from-violet-50 to-purple-50","bg-gray-800/50")} rounded-xl border ${n.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500 mr-3",children:c.jsx(Ve,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Cost Settings"})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"unitCost",className:`block text-sm font-semibold ${n.text} mb-3`,children:["💵 Cost per ",b==="custom"?p||"Unit":b]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500",children:c.jsx(Ve,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"unitCost",value:o,onChange:R=>a(R.target.value),onWheel:Lr,step:"0.01",min:"0.01",placeholder:"Enter cost per unit",className:`w-full pl-12 pr-4 py-4 border-4 border-violet-300 rounded-xl focus:ring-4 focus:ring-violet-500 focus:border-violet-600 ${n.card} ${n.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-violet-400`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"💡 This is used to calculate the cost of your electricity usage"})]})]}),c.jsxs("div",{className:`p-6 ${s("bg-gradient-to-br from-amber-50 to-yellow-50","bg-gray-800/50")} rounded-xl border ${n.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-amber-400 to-yellow-500 mr-3",children:c.jsx(_r,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Alert Settings"})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"thresholdLimit",className:`block text-sm font-semibold ${n.text} mb-3`,children:["⚠️ Low ",b==="custom"?p||"Units":b," Warning Threshold"]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-amber-400 to-yellow-500",children:c.jsx(_r,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"thresholdLimit",value:l,onChange:R=>u(R.target.value),onWheel:Lr,step:"0.01",min:"0",placeholder:"Enter low units warning threshold",className:`w-full pl-12 pr-4 py-4 border-4 border-amber-300 rounded-xl focus:ring-4 focus:ring-amber-500 focus:border-amber-600 ${n.card} ${n.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-amber-400`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"⚠️ You'll receive a warning when your remaining units drop below this threshold"})]})]}),c.jsxs("div",{className:`p-6 ${s("bg-gradient-to-br from-slate-50 to-gray-50","bg-gray-800/50")} rounded-xl border ${n.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-gray-400 to-slate-500 mr-3",children:c.jsx(Yi,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Current Settings Preview"})]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:`flex justify-between items-center p-3 ${s("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Currency:"}),c.jsx("span",{className:`${n.text} font-bold`,children:t.currency==="CUSTOM"?`${t.customCurrencySymbol||"C"} - ${t.customCurrencyName||"Custom Currency"}`:`${((z=C.find(R=>R.code===(t.currency||"ZAR")))==null?void 0:z.symbol)||"R"} - ${((B=C.find(R=>R.code===(t.currency||"ZAR")))==null?void 0:B.name)||"South African Rand"}`})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${s("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Unit Name:"}),c.jsx("span",{className:`${n.text} font-bold`,children:t.unitName==="custom"?t.customUnitName||"Units":t.unitName||"kWh"})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${s("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Unit Cost:"}),c.jsxs("span",{className:`${n.text} font-bold`,children:[t.currencySymbol||"R",t.unitCost.toFixed(2)," per ",t.unitName==="custom"?t.customUnitName||"Unit":t.unitName||"kWh"]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${s("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Low Units Warning:"}),c.jsxs("span",{className:`${n.text} font-bold`,children:[t.thresholdLimit.toFixed(2)," ",t.unitName==="custom"?t.customUnitName||"Units":t.unitName||"kWh"]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${s("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Last Reset:"}),c.jsx("span",{className:`${n.text} font-bold`,children:t.lastResetDate?new Date(t.lastResetDate).toLocaleDateString():"Never"})]})]})]}),c.jsxs("div",{className:`p-6 ${s("bg-gradient-to-br from-indigo-50 to-purple-50","bg-gray-800/50")} rounded-xl border ${n.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-indigo-400 to-purple-500 mr-3",children:c.jsx(_r,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Notification Settings"})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("label",{className:`text-sm font-semibold ${n.text}`,children:"🔔 Daily Usage Reminders"}),c.jsx("p",{className:`text-xs ${n.textSecondary} opacity-80 mt-1`,children:"Get reminded to record your electricity usage every day"})]}),c.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[c.jsx("input",{type:"checkbox",checked:w,onChange:R=>_(R.target.checked),className:"sr-only peer","aria-label":"Enable daily usage reminder notifications"}),c.jsx("div",{className:`w-11 h-6 ${r==="dark"?"bg-gray-600":"bg-gray-200"} peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600`})]})]}),w&&c.jsxs("div",{children:[c.jsx("label",{htmlFor:"notificationTime",className:`block text-sm font-semibold ${n.text} mb-3`,children:"⏰ Reminder Time"}),c.jsx("input",{type:"time",id:"notificationTime",value:S,onChange:R=>k(R.target.value),className:`w-full px-4 py-4 border-4 border-indigo-300 rounded-xl focus:ring-4 focus:ring-indigo-500 focus:border-indigo-600 ${n.card} ${n.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-indigo-400`}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"⏰ You'll receive a notification at this time every day"})]})]})]}),c.jsx("button",{type:"submit",disabled:j,className:`w-full bg-gradient-to-r ${n.gradient} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:c.jsx("div",{className:"flex items-center justify-center gap-2",children:j?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),"Saving Settings..."]}):c.jsxs(c.Fragment,{children:[c.jsx(Yi,{className:"h-5 w-5"}),"Save Settings"]})})})]})}),T==="appearance"&&c.jsx("div",{className:"space-y-6",children:c.jsx(YS,{})}),T==="reset"&&c.jsx("div",{className:"space-y-6",children:c.jsx(XS,{})})]})})})}function QS(){const[t,e]=N.useState(""),[n,r]=N.useState(""),[i,s]=N.useState(""),{initializeApp:o,state:a}=Xe(),{theme:l}=me(),u=d=>{d.preventDefault(),s("");const h=parseFloat(t),f=parseFloat(n);if(isNaN(h)||h<0){s("Please enter a valid number of units (0 or greater)");return}if(isNaN(f)||f<=0){s("Please enter a valid cost per unit (greater than 0)");return}o(h,f)};return c.jsx("div",{className:`min-h-dvh flex items-center justify-center px-4 py-8 ${l.background}`,style:{paddingTop:"calc(env(safe-area-inset-top, 0px) + 2rem)",paddingBottom:"calc(env(safe-area-inset-bottom, 0px) + 2rem)"},children:c.jsxs("div",{className:`max-w-lg w-full ${l.card} rounded-2xl shadow-2xl p-8 border ${l.border}`,children:[c.jsxs("div",{className:"text-center mb-8",children:[c.jsx("div",{className:"flex justify-center mb-6",children:c.jsxs("div",{className:"h-24 w-24 flex items-center justify-center",children:[c.jsx("img",{src:"/oie_transparent (1).png",alt:"Prepaid User Electricity Logo",className:"h-24 w-24 object-contain",onError:d=>{d.target.style.display="none",d.target.nextElementSibling.style.display="flex"}}),c.jsx("div",{className:"h-24 w-24 rounded-full bg-blue-600 flex items-center justify-center shadow-2xl border-4 border-white",style:{display:"none"},children:c.jsx("span",{className:"text-white text-4xl font-bold",children:"⚡"})})]})}),c.jsx("h1",{className:`text-3xl font-bold ${l.text} mb-4`,children:"Welcome to Prepaid Meter App"}),c.jsx("p",{className:`${l.textSecondary} text-lg mb-2`,children:"Let's get started by setting up your initial meter reading and cost settings"})]}),c.jsxs("form",{onSubmit:u,className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"initialUnits",className:`block text-sm font-medium ${l.text} mb-2`,children:"Initial Unit Value"}),c.jsx("input",{type:"number",id:"initialUnits",value:t,onChange:d=>e(d.target.value),onWheel:Lr,step:"0.01",min:"0",placeholder:"Enter your current meter reading",className:`w-full px-4 py-4 border-2 ${l.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${l.card} ${l.text} text-lg transition-all duration-200 hover:border-blue-300`,required:!0})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"unitCost",className:`block text-sm font-medium ${l.text} mb-2`,children:["Cost per Unit (",a.currencySymbol||"R",")"]}),c.jsx("input",{type:"number",id:"unitCost",value:n,onChange:d=>r(d.target.value),onWheel:Lr,step:"0.01",min:"0.01",placeholder:"Enter cost per unit (e.g., 2.50)",className:`w-full px-4 py-4 border-2 ${l.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${l.card} ${l.text} text-lg transition-all duration-200 hover:border-blue-300`,required:!0})]}),i&&c.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:c.jsx("p",{className:"text-sm text-red-600",children:i})}),c.jsxs("div",{className:`p-4 ${l.secondary} rounded-lg`,children:[c.jsx("h3",{className:`font-medium ${l.text} mb-2`,children:"What happens next?"}),c.jsxs("ul",{className:`text-sm ${l.textSecondary} space-y-1`,children:[c.jsx("li",{children:"• This will be your starting point for tracking usage"}),c.jsx("li",{children:"• The cost setting will be used for purchase calculations"}),c.jsx("li",{children:"• You can add purchases to increase your units"}),c.jsx("li",{children:"• Track your daily electricity consumption"}),c.jsx("li",{children:"• Set up warnings and monthly resets"})]})]}),c.jsx("button",{type:"submit",className:`w-full bg-gradient-to-r ${l.gradient} text-white py-4 px-6 rounded-xl font-semibold text-lg hover:opacity-90 transition-all duration-200 focus:ring-2 focus:ring-opacity-50 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5`,children:"🚀 Initialize App"})]}),c.jsx("div",{className:"mt-6 text-center",children:c.jsx("p",{className:`text-xs ${l.textSecondary}`,children:"You can always change these values later in Settings"})})]})})}function GS(){const[t,e]=N.useState(!1),{state:n}=Xe(),{theme:r,currentTheme:i}=me(),s=Et(),o=()=>({electric:"safe-area-electric",green:"safe-area-green",teal:"safe-area-teal",pink:"safe-area-pink",dark:"safe-area-dark"})[i]||"safe-area-electric";return N.useEffect(()=>{const a=l=>{l.target.type==="number"&&document.activeElement===l.target&&l.preventDefault()};return document.addEventListener("wheel",a,{passive:!1}),()=>{document.removeEventListener("wheel",a)}},[]),N.useEffect(()=>{const a=l=>{t&&!l.target.closest(".sidebar-container")&&!l.target.closest(".hamburger-menu")&&e(!1)};return document.addEventListener("mousedown",a),()=>{document.removeEventListener("mousedown",a)}},[t]),N.useEffect(()=>{e(!1)},[s.pathname]),N.useEffect(()=>{e(!1);const a=()=>{window.innerWidth<1024&&e(!1)};return window.addEventListener("resize",a),()=>window.removeEventListener("resize",a)},[]),n.isInitialized?c.jsxs("div",{className:`flex flex-col h-screen ${r.background}`,style:{height:"100vh"},children:[c.jsx("div",{className:`${o()} safe-top mobile-safe-top flex-shrink-0`}),t&&c.jsx("div",{className:"fixed inset-0 bg-black/50 z-40 lg:hidden",onClick:()=>e(!1),style:{zIndex:40}}),c.jsx(Y1,{isOpen:t,onClose:()=>e(!1)}),c.jsx("div",{className:"flex-shrink-0",children:c.jsx(V1,{onMenuClick:()=>e(a=>!a)})}),c.jsx("main",{className:"flex-1 overflow-y-auto px-4 pt-2 safe-bottom-nav mobile-safe-bottom-nav",children:c.jsx("div",{className:"max-w-7xl mx-auto",children:c.jsxs(oy,{children:[c.jsx(Tn,{path:"/",element:c.jsx(rf,{})}),c.jsx(Tn,{path:"/dashboard",element:c.jsx(rf,{})}),c.jsx(Tn,{path:"/purchases",element:c.jsx(US,{})}),c.jsx(Tn,{path:"/usage",element:c.jsx(HS,{})}),c.jsx(Tn,{path:"/history",element:c.jsx(VS,{})}),c.jsx(Tn,{path:"/settings",element:c.jsx(KS,{})})]})})}),c.jsx("div",{className:"flex-shrink-0",children:c.jsx(X1,{})}),c.jsx("div",{className:`${o()} safe-bottom mobile-safe-bottom flex-shrink-0`}),t&&c.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>e(!1)})]}):c.jsx(QS,{})}const qS="modulepreload",ZS=function(t){return"/"+t},sf={},zg=function(e,n,r){let i=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),a=(o==null?void 0:o.nonce)||(o==null?void 0:o.getAttribute("nonce"));i=Promise.allSettled(n.map(l=>{if(l=ZS(l),l in sf)return;sf[l]=!0;const u=l.endsWith(".css"),d=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${l}"]${d}`))return;const h=document.createElement("link");if(h.rel=u?"stylesheet":qS,u||(h.as="script"),h.crossOrigin="",h.href=l,a&&h.setAttribute("nonce",a),document.head.appendChild(h),u)return new Promise((f,m)=>{h.addEventListener("load",f),h.addEventListener("error",()=>m(new Error(`Unable to preload CSS for ${l}`)))})}))}function s(o){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=o,window.dispatchEvent(a),!a.defaultPrevented)throw o}return i.then(o=>{for(const a of o||[])a.status==="rejected"&&s(a.reason);return e().catch(s)})};/*! Capacitor: https://capacitorjs.com/ - MIT License */var Ar;(function(t){t.Unimplemented="UNIMPLEMENTED",t.Unavailable="UNAVAILABLE"})(Ar||(Ar={}));class Ja extends Error{constructor(e,n,r){super(e),this.message=e,this.code=n,this.data=r}}const JS=t=>{var e,n;return t!=null&&t.androidBridge?"android":!((n=(e=t==null?void 0:t.webkit)===null||e===void 0?void 0:e.messageHandlers)===null||n===void 0)&&n.bridge?"ios":"web"},e_=t=>{const e=t.CapacitorCustomPlatform||null,n=t.Capacitor||{},r=n.Plugins=n.Plugins||{},i=()=>e!==null?e.name:JS(t),s=()=>i()!=="web",o=h=>{const f=u.get(h);return!!(f!=null&&f.platforms.has(i())||a(h))},a=h=>{var f;return(f=n.PluginHeaders)===null||f===void 0?void 0:f.find(m=>m.name===h)},l=h=>t.console.error(h),u=new Map,d=(h,f={})=>{const m=u.get(h);if(m)return console.warn(`Capacitor plugin "${h}" already registered. Cannot register plugins twice.`),m.proxy;const x=i(),v=a(h);let b;const g=async()=>(!b&&x in f?b=typeof f[x]=="function"?b=await f[x]():b=f[x]:e!==null&&!b&&"web"in f&&(b=typeof f.web=="function"?b=await f.web():b=f.web),b),p=(j,E)=>{var C,$;if(v){const D=v==null?void 0:v.methods.find(T=>E===T.name);if(D)return D.rtype==="promise"?T=>n.nativePromise(h,E.toString(),T):(T,U)=>n.nativeCallback(h,E.toString(),T,U);if(j)return(C=j[E])===null||C===void 0?void 0:C.bind(j)}else{if(j)return($=j[E])===null||$===void 0?void 0:$.bind(j);throw new Ja(`"${h}" plugin is not implemented on ${x}`,Ar.Unimplemented)}},y=j=>{let E;const C=(...$)=>{const D=g().then(T=>{const U=p(T,j);if(U){const z=U(...$);return E=z==null?void 0:z.remove,z}else throw new Ja(`"${h}.${j}()" is not implemented on ${x}`,Ar.Unimplemented)});return j==="addListener"&&(D.remove=async()=>E()),D};return C.toString=()=>`${j.toString()}() { [capacitor code] }`,Object.defineProperty(C,"name",{value:j,writable:!1,configurable:!1}),C},w=y("addListener"),_=y("removeListener"),S=(j,E)=>{const C=w({eventName:j},E),$=async()=>{const T=await C;_({eventName:j,callbackId:T},E)},D=new Promise(T=>C.then(()=>T({remove:$})));return D.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await $()},D},k=new Proxy({},{get(j,E){switch(E){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return v?S:w;case"removeListener":return _;default:return y(E)}}});return r[h]=k,u.set(h,{name:h,proxy:k,platforms:new Set([...Object.keys(f),...v?[x]:[]])}),k};return n.convertFileSrc||(n.convertFileSrc=h=>h),n.getPlatform=i,n.handleError=l,n.isNativePlatform=s,n.isPluginAvailable=o,n.registerPlugin=d,n.Exception=Ja,n.DEBUG=!!n.DEBUG,n.isLoggingEnabled=!!n.isLoggingEnabled,n},t_=t=>t.Capacitor=e_(t),Fr=t_(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),os=Fr.registerPlugin;class Ig{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,n){let r=!1;this.listeners[e]||(this.listeners[e]=[],r=!0),this.listeners[e].push(n);const s=this.windowListeners[e];s&&!s.registered&&this.addWindowListener(s),r&&this.sendRetainedArgumentsForEvent(e);const o=async()=>this.removeListener(e,n);return Promise.resolve({remove:o})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,n,r){const i=this.listeners[e];if(!i){if(r){let s=this.retainedEventArguments[e];s||(s=[]),s.push(n),this.retainedEventArguments[e]=s}return}i.forEach(s=>s(n))}hasListeners(e){return!!this.listeners[e].length}registerWindowListener(e,n){this.windowListeners[n]={registered:!1,windowEventName:e,pluginEventName:n,handler:r=>{this.notifyListeners(n,r)}}}unimplemented(e="not implemented"){return new Fr.Exception(e,Ar.Unimplemented)}unavailable(e="not available"){return new Fr.Exception(e,Ar.Unavailable)}async removeListener(e,n){const r=this.listeners[e];if(!r)return;const i=r.indexOf(n);this.listeners[e].splice(i,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const n=this.retainedEventArguments[e];n&&(delete this.retainedEventArguments[e],n.forEach(r=>{this.notifyListeners(e,r)}))}}const of=t=>encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),af=t=>t.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class n_ extends Ig{async getCookies(){const e=document.cookie,n={};return e.split(";").forEach(r=>{if(r.length<=0)return;let[i,s]=r.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");i=af(i).trim(),s=af(s).trim(),n[i]=s}),n}async setCookie(e){try{const n=of(e.key),r=of(e.value),i=`; expires=${(e.expires||"").replace("expires=","")}`,s=(e.path||"/").replace("path=",""),o=e.url!=null&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${n}=${r||""}${i}; path=${s}; ${o};`}catch(n){return Promise.reject(n)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(n){return Promise.reject(n)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const n of e)document.cookie=n.replace(/^ +/,"").replace(/=.*/,`=;expires=${new Date().toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}os("CapacitorCookies",{web:()=>new n_});const r_=async t=>new Promise((e,n)=>{const r=new FileReader;r.onload=()=>{const i=r.result;e(i.indexOf(",")>=0?i.split(",")[1]:i)},r.onerror=i=>n(i),r.readAsDataURL(t)}),i_=(t={})=>{const e=Object.keys(t);return Object.keys(t).map(i=>i.toLocaleLowerCase()).reduce((i,s,o)=>(i[s]=t[e[o]],i),{})},s_=(t,e=!0)=>t?Object.entries(t).reduce((r,i)=>{const[s,o]=i;let a,l;return Array.isArray(o)?(l="",o.forEach(u=>{a=e?encodeURIComponent(u):u,l+=`${s}=${a}&`}),l.slice(0,-1)):(a=e?encodeURIComponent(o):o,l=`${s}=${a}`),`${r}&${l}`},"").substr(1):null,o_=(t,e={})=>{const n=Object.assign({method:t.method||"GET",headers:t.headers},e),i=i_(t.headers)["content-type"]||"";if(typeof t.data=="string")n.body=t.data;else if(i.includes("application/x-www-form-urlencoded")){const s=new URLSearchParams;for(const[o,a]of Object.entries(t.data||{}))s.set(o,a);n.body=s.toString()}else if(i.includes("multipart/form-data")||t.data instanceof FormData){const s=new FormData;if(t.data instanceof FormData)t.data.forEach((a,l)=>{s.append(l,a)});else for(const a of Object.keys(t.data))s.append(a,t.data[a]);n.body=s;const o=new Headers(n.headers);o.delete("content-type"),n.headers=o}else(i.includes("application/json")||typeof t.data=="object")&&(n.body=JSON.stringify(t.data));return n};class a_ extends Ig{async request(e){const n=o_(e,e.webFetchExtra),r=s_(e.params,e.shouldEncodeUrlParams),i=r?`${e.url}?${r}`:e.url,s=await fetch(i,n),o=s.headers.get("content-type")||"";let{responseType:a="text"}=s.ok?e:{};o.includes("application/json")&&(a="json");let l,u;switch(a){case"arraybuffer":case"blob":u=await s.blob(),l=await r_(u);break;case"json":l=await s.json();break;case"document":case"text":default:l=await s.text()}const d={};return s.headers.forEach((h,f)=>{d[f]=h}),{data:l,headers:d,status:s.status,url:s.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}os("CapacitorHttp",{web:()=>new a_});const lf=os("App",{web:()=>zg(()=>import("./web-BN6y9yeg.js"),[]).then(t=>new t.AppWeb)}),l_=()=>{const t=Jn(),e=Et();N.useEffect(()=>{if(!Fr.isNativePlatform()||Fr.getPlatform()!=="android")return;const n=()=>{const i=e.pathname,s={"/purchases":"/","/usage":"/","/history":"/","/settings":"/"};if(s[i]){t(s[i]);return}if(i==="/"){lf.exitApp();return}t("/")},r=lf.addListener("backButton",n);return()=>{r.remove()}},[t,e.pathname])};var cf;(function(t){t.Dark="DARK",t.Light="LIGHT",t.Default="DEFAULT"})(cf||(cf={}));var uf;(function(t){t.None="NONE",t.Slide="SLIDE",t.Fade="FADE"})(uf||(uf={}));const ir=os("StatusBar");var df;(function(t){t.WHITE="#FFFFFF",t.BLACK="#000000",t.TRANSPARENT="transparent"})(df||(df={}));const hf=os("NavigationBar",{web:()=>zg(()=>import("./web-FfnyEzAr.js"),[]).then(t=>new t.NavigationBarWeb)});function c_(){const{currentTheme:t}=me();N.useEffect(()=>{if(!Fr.isNativePlatform())return;(async()=>{try{if(await ir.setOverlaysWebView({overlay:!1}),t==="dark")await ir.setStyle({style:"LIGHT"}),await ir.setBackgroundColor({color:"#111827"});else{await ir.setStyle({style:"LIGHT"});const r={electric:"#3B82F6",green:"#10B981",teal:"#14B8A6",pink:"#EC4899"}[t]||"#3B82F6";await ir.setBackgroundColor({color:r})}if(await ir.show(),t==="dark")await hf.setColor({color:"#111827",darkButtons:!1});else{const r={electric:"#3B82F6",green:"#10B981",teal:"#14B8A6",pink:"#EC4899"}[t]||"#3B82F6";await hf.setColor({color:r,darkButtons:!1})}}catch(n){console.log("StatusBar or NavigationBar plugin not available:",n)}})()},[t])}function u_(){return l_(),c_(),c.jsx(GS,{})}function d_(){return c.jsx(py,{children:c.jsx($1,{children:c.jsx(M1,{children:c.jsx(u_,{})})})})}el.createRoot(document.getElementById("root")).render(c.jsx(pt.StrictMode,{children:c.jsx(d_,{})}));export{Ig as W};
