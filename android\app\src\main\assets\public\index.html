<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/lightning.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, minimum-scale=0.5, user-scalable=yes, viewport-fit=cover" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="theme-color" content="#ffffff" />
    <title>Prepaid Meter App</title>
    <style>
      /* Enable smooth scrolling on mobile */
      html, body {
        -webkit-overflow-scrolling: touch;
        height: 100%;
        height: 100vh;
        height: 100dvh;
      }

      /* Enable horizontal scrolling for specific containers */
      .overflow-x-auto {
        -webkit-overflow-scrolling: touch;
        scrollbar-width: thin;
      }

      /* Hide scrollbars on mobile while keeping functionality */
      @media (max-width: 768px) {
        .overflow-x-auto::-webkit-scrollbar {
          height: 4px;
        }
        .overflow-x-auto::-webkit-scrollbar-track {
          background: transparent;
        }
        .overflow-x-auto::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 2px;
        }
      }

      /* Ensure inputs are properly sized for mobile */
      input, select, textarea {
        font-size: 16px;
      }

      /* Remove tap highlights */
      * {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        user-select: none;
      }

      /* Allow text selection for inputs and content */
      input, textarea, [contenteditable] {
        -webkit-user-select: text;
        -moz-user-select: text;
        user-select: text;
      }

      /* Enable smooth pinch-to-zoom on mobile */
      body {
        touch-action: manipulation;
      }

      /* Allow zooming on specific content areas */
      .zoomable {
        touch-action: pinch-zoom;
      }

      /* Improve touch targets for mobile */
      button, a, input, select, textarea {
        min-height: 44px;
        min-width: 44px;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-B7PCyZRe.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-LvTLgS5k.css">
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
