import { useEffect } from 'react';
import { StatusBar } from '@capacitor/status-bar';
import { Capacitor } from '@capacitor/core';
import { useTheme } from '../context/ThemeContext';

export function useStatusBar() {
  const { currentTheme } = useTheme();

  useEffect(() => {
    // Only run on mobile platforms
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    const updateStatusBar = async () => {
      try {
        // Always ensure status bar is visible and doesn't overlay content
        await StatusBar.setOverlaysWebView({ overlay: false });

        // Configure status bar based on current theme
        if (currentTheme === 'dark') {
          // Dark theme: light content on dark background matching Current Units card
          await StatusBar.setStyle({ style: 'LIGHT' });
          await StatusBar.setBackgroundColor({ color: '#1F2937' }); // gray-800 to match Current Units card
        } else {
          // Light themes: dark content on theme-colored background matching Current Units card
          await StatusBar.setStyle({ style: 'DARK' });

          // Set background color to match Current Units card theme colors
          const themeColors = {
            'electric': '#3B82F6', // blue-500 to match Current Units card
            'green': '#10B981',    // green-500
            'teal': '#14B8A6',     // teal-500
            'pink': '#EC4899'      // pink-500
          };

          const backgroundColor = themeColors[currentTheme] || '#3B82F6';
          await StatusBar.setBackgroundColor({ color: backgroundColor });
        }

        // Show the status bar if it was hidden
        await StatusBar.show();

      } catch (error) {
        console.log('StatusBar plugin not available:', error);
      }
    };

    updateStatusBar();
  }, [currentTheme]);
}
