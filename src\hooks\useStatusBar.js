import { useEffect } from 'react';
import { StatusBar } from '@capacitor/status-bar';
import { Capacitor } from '@capacitor/core';
import { useTheme } from '../context/ThemeContext';

export function useStatusBar() {
  const { currentTheme } = useTheme();

  useEffect(() => {
    // Only run on mobile platforms
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    const updateStatusBar = async () => {
      try {
        // Configure status bar based on current theme
        if (currentTheme === 'dark') {
          // Dark theme: light content on dark background
          await StatusBar.setStyle({ style: 'LIGHT' });
          await StatusBar.setBackgroundColor({ color: '#111827' }); // gray-900
        } else {
          // Light themes: dark content on light background
          await StatusBar.setStyle({ style: 'DARK' });
          
          // Set background color based on theme
          const themeColors = {
            'electric': '#3B82F6', // blue-500
            'green': '#10B981',    // green-500
            'teal': '#14B8A6',     // teal-500
            'pink': '#EC4899'      // pink-500
          };
          
          const backgroundColor = themeColors[currentTheme] || '#3B82F6';
          await StatusBar.setBackgroundColor({ color: backgroundColor });
        }
        
        // Ensure status bar doesn't overlay content
        await StatusBar.setOverlaysWebView({ overlay: false });
        
      } catch (error) {
        console.log('StatusBar plugin not available:', error);
      }
    };

    updateStatusBar();
  }, [currentTheme]);
}
