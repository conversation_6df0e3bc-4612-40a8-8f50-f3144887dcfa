import { useEffect } from 'react';
import { StatusBar } from '@capacitor/status-bar';
import { Capacitor } from '@capacitor/core';
import { useTheme } from '../context/ThemeContext';

export function useStatusBar() {
  const { currentTheme } = useTheme();

  useEffect(() => {
    // Only run on mobile platforms
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    const updateStatusBar = async () => {
      try {
        // Always ensure status bar is visible and doesn't overlay content
        await StatusBar.setOverlaysWebView({ overlay: false });

        // Configure status bar based on current theme
        if (currentTheme === 'dark') {
          // Dark theme: light content on dark background
          await StatusBar.setStyle({ style: 'LIGHT' });
          await StatusBar.setBackgroundColor({ color: '#111827' }); // gray-900
        } else {
          // Light themes: dark content on light background
          await StatusBar.setStyle({ style: 'DARK' });

          // Set background color based on theme - using lighter shades for better visibility
          const themeColors = {
            'electric': '#60A5FA', // blue-400 (lighter for better contrast)
            'green': '#34D399',    // green-400
            'teal': '#2DD4BF',     // teal-400
            'pink': '#F472B6'      // pink-400
          };

          const backgroundColor = themeColors[currentTheme] || '#60A5FA';
          await StatusBar.setBackgroundColor({ color: backgroundColor });
        }

        // Show the status bar if it was hidden
        await StatusBar.show();

      } catch (error) {
        console.log('StatusBar plugin not available:', error);
      }
    };

    updateStatusBar();
  }, [currentTheme]);
}
